{"body": {"content": [{"type": "heading", "attrs": {"level": 3}, "content": [{"type": "text", "text": "Script Results | PR - ${PR_ID} | #${JOB_ID} [${STATE}]"}]}, {"content": [{"text": "Executed By: ", "type": "text", "marks": [{"type": "strong"}]}, {"text": "${USER}", "type": "text"}, {"text": "\nRegion: ", "type": "text", "marks": [{"type": "strong"}]}, {"text": "${REGION}", "type": "text"}, {"text": "\nDuration: ", "type": "text", "marks": [{"type": "strong"}]}, {"text": "${DURATION} seconds", "type": "text"}, {"text": "\nExecuted Repl Relative Path: ", "type": "text", "marks": [{"type": "strong"}]}, {"text": "${CODE_PATH}", "type": "text", "marks": [{"type": "code"}]}, {"text": "\nScript Logs", "type": "text", "marks": [{"type": "link", "attrs": {"href": "${LOGS_URL}", "title": "Atlassian"}}]}, {"text": " | ", "type": "text"}, {"text": "Output Files", "type": "text", "marks": [{"type": "link", "attrs": {"href": "${OUTPUT_URL}", "title": "Atlassian"}}]}], "type": "paragraph"}], "type": "doc", "version": 1}}