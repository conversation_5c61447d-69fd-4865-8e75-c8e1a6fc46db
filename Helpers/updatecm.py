import yaml
import argparse

def update_config_map(config_map_path, config_file_path, config, stack, environment, microservice=""):
  with open(config_map_path, 'r') as yaml_file:
    configmap_yaml = yaml.safe_load(yaml_file)
    configmap_yaml['data'] = {}
  
  with open(config_file_path, 'r') as config_file:
    config_data = config_file.read().strip()
    if (config == 'haproxy'):
      whitelistedhosts_file = f'surveysparrow-production-config/configs/haproxy/{environment}/whitelistedhosts.txt'
      blacklistedhosts_file = f'surveysparrow-production-config/configs/haproxy/{environment}/blacklistedhosts.txt'
      blueaccounts_file     = f'surveysparrow-production-config/configs/haproxy/{environment}/blueaccounts.txt'

      with open(whitelistedhosts_file, 'r') as whitelistedhosts:
        whitelistedhosts_data = whitelistedhosts.read().strip()
      
      with open(blacklistedhosts_file, 'r') as blacklistedhosts:
        blacklistedhosts_data = blacklistedhosts.read().strip()
      
      with open(blueaccounts_file, 'r') as blueaccounts:
        blueaccounts_data = blueaccounts.read().strip()

  if (config == 'haproxy'):
    configmap_yaml['metadata']['name'] = f'haproxy-config-{stack}'  
    configmap_yaml['data']['haproxy.cfg'] = config_data + "\n"
    configmap_yaml['data']['blacklistedhosts.txt'] = blacklistedhosts_data + "\n"
    configmap_yaml['data']['whitelistedhosts.txt'] = whitelistedhosts_data + "\n"
    configmap_yaml['data']['blueaccounts.txt'] = blueaccounts_data + "\n"

  if (config == 'app'):
    configmap_yaml['metadata']['name'] = f'surveysparrow-config-{stack}'
    configmap_yaml['data']['production.json'] = f"{config_data}"

  if (config == 'migration' and len(microservice)):
    configmap_yaml['metadata']['name'] = f'surveysparrow-config-${microservice}-migration'
    configmap_yaml['data']['production.json'] = f"{config_data}"

  if (config == 'migration' and not len(microservice)):
    configmap_yaml['metadata']['name'] = f'surveysparrow-config-migration'
    configmap_yaml['data']['production.json'] = f"{config_data}"

  if (config == 'arena'):
    configmap_yaml['metadata']['name'] = 'surveysparrow-arena-config'
    configmap_yaml['data']['production.json'] = f"{config_data}"
  
  if (config == 'bullboard'):
    configmap_yaml['metadata']['name'] = 'surveysparrow-bull-dashboard-env'
    configmap_yaml['data']['config'] = config_data

  if (config == 'microservice'):
    configmap_yaml['metadata']['name'] = f'surveysparrow-{microservice}-config'
    configmap_yaml['data']['production.json'] = f"{config_data}"

  with open(config_map_path, 'w') as yaml_file:
    yaml.dump(configmap_yaml, yaml_file)

def main():
  try:
    parser = argparse.ArgumentParser(description="Utility to update the configmap yaml")
    parser.add_argument('-c', '--config', help="Config to update", type=str)
    parser.add_argument('-e', '--environment', help="Environment to update config for", type=str)
    parser.add_argument('-s', '--stack', help="Stack update config for", type=str, default="")
    parser.add_argument('-m', '--microservice', help="Microservice to update config for", type=str, default="")
    args = parser.parse_args()

    config        = args.config
    environment  = args.environment
    stack        = args.stack
    microservice = args.microservice

    configmap_yaml_path = 'Helpers/cm-template.yaml'
    if (config == 'haproxy'):
      config_file_path = f'surveysparrow-production-config/configs/haproxy/{environment}/{stack}.cfg'

    if (config == 'app' or (config == 'migration' and not len(microservice))):
      config_file_path = f'surveysparrow-production-config/{environment}.json'

    if (config == 'arena'):
      config_file_path = f'surveysparrow-production-config/arena/{environment}.json'

    if (config == 'bullboard'):
      config_file_path = f'surveysparrow-production-config/configs/bullboard/{environment}.json'
    
    if (config == 'microservice' or (config == 'migration' and len(microservice))):
      config_file_path = f'surveysparrow-production-config/microservices/{microservice}/{environment}.json'

    update_config_map(configmap_yaml_path, config_file_path, config, stack, environment, microservice)

  except Exception as e:
    print(f"==========> [ABORTING_BUILD] REASON : Exception catched - {e}. <==========")

if (__name__ == "__main__"):
  main()

