mkdir -p output

install_aws_cli_v2() {
    echo "Detecting operating system..."

    # Detect the OS
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$ID
        VERSION=$VERSION_ID
    else
        OS=$(uname -s)
        VERSION=$(uname -r)
    fi

    echo "Operating System: $OS $VERSION"

    # Install AWS CLI based on OS
    aws_cli_zip_url="https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip"
    case $OS in
        ubuntu|debian)
            apt-get update -y
            apt-get install -y unzip curl
            curl "$aws_cli_zip_url" -o "awscliv2.zip"
            unzip -qq awscliv2.zip
            ./aws/install
            ;;
        centos|rhel|fedora|amzn)
            yum install -y unzip curl
            curl "$aws_cli_zip_url" -o "awscliv2.zip"
            unzip -qq awscliv2.zip
            ./aws/install
            ;;
        alpine)
            apk add aws-cli
            ;;
        *)
            echo "Unsupported OS: $OS"
            exit 1
            ;;
    esac

    # Verify installation
    aws --version
}

# Run the repl script
alias repl=$REPL_EXEC
repl
state=$?
echo "Repl script exited with status $state"

echo "$UPLOAD_TO_S3 $REPL_BUCKET_NAME $REPL_APPLICATION_NAME $PULL_REQUEST_NUMBER $JOB_ID $REGION"
# If UPLOAD_TO_S3 is set to true, upload the output to S3
if [ "$UPLOAD_TO_S3" = "true" ]; then
    echo -e "\033[1;33m[=================================== POST SCRIPT ATTACHED BY DEVOPS STARTS ===================================]\033[0m"
    install_aws_cli_v2
    tar -czf $REGION.output.tar.gz output
    aws s3 cp $REGION.output.tar.gz s3://$REPL_BUCKET_NAME/REPL-$REPL_APPLICATION_NAME/$PULL_REQUEST_NUMBER-$JOB_ID/$REGION.output.tar.gz --region us-east-1
    echo -e "\033[1;33m[=================================== POST SCRIPT ATTACHED BY DEVOPS ENDS ===================================]\033[0m"
fi

exit $state