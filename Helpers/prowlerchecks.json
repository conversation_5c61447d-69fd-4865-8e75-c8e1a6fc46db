{"aws": ["accessanalyzer_enabled", "accessanalyzer_enabled_without_findings", "account_maintain_current_contact_details", "account_maintain_different_contact_details_to_security_billing_and_operations", "account_security_contact_information_is_registered", "account_security_questions_are_registered_in_the_aws_account", "acm_certificates_expiration_check", "acm_certificates_transparency_logs_enabled", "apigateway_restapi_public", "apigateway_restapi_public_with_authorizer", "appstream_fleet_default_internet_access_disabled", "appstream_fleet_maximum_session_duration", "appstream_fleet_session_disconnect_timeout", "appstream_fleet_session_idle_disconnect_timeout", "athena_workgroup_encryption", "athena_workgroup_enforce_configuration", "autoscaling_find_secrets_ec2_launch_configuration", "autoscaling_group_multiple_az", "awslambda_function_no_secrets_in_code", "awslambda_function_no_secrets_in_variables", "awslambda_function_url_cors_policy", "awslambda_function_using_supported_runtimes", "cloudformation_stack_outputs_find_secrets", "cloudformation_stacks_termination_protection_enabled", "cloudfront_distributions_field_level_encryption_enabled", "cloudfront_distributions_geo_restrictions_enabled", "cloudfront_distributions_using_deprecated_ssl_protocols", "cloudtrail_bucket_requires_mfa_delete", "cloudtrail_cloudwatch_logging_enabled", "cloudtrail_insights_exist", "cloudtrail_kms_encryption_enabled", "cloudtrail_log_file_validation_enabled", "cloudtrail_logs_s3_bucket_access_logging_enabled", "cloudtrail_logs_s3_bucket_is_not_publicly_accessible", "cloudtrail_multi_region_enabled", "cloudtrail_multi_region_enabled_logging_management_events", "cloudtrail_s3_dataevents_read_enabled", "cloudtrail_s3_dataevents_write_enabled", "cloudwatch_changes_to_network_acls_alarm_configured", "cloudwatch_changes_to_network_gateways_alarm_configured", "cloudwatch_changes_to_network_route_tables_alarm_configured", "cloudwatch_changes_to_vpcs_alarm_configured", "cloudwatch_cross_account_sharing_disabled", "cloudwatch_log_group_kms_encryption_enabled", "cloudwatch_log_group_no_secrets_in_logs", "cloudwatch_log_group_retention_policy_specific_days_enabled", "cloudwatch_log_metric_filter_and_alarm_for_aws_config_configuration_changes_enabled", "cloudwatch_log_metric_filter_and_alarm_for_cloudtrail_configuration_changes_enabled", "cloudwatch_log_metric_filter_authentication_failures", "cloudwatch_log_metric_filter_aws_organizations_changes", "cloudwatch_log_metric_filter_disable_or_scheduled_deletion_of_kms_cmk", "cloudwatch_log_metric_filter_for_s3_bucket_policy_changes", "cloudwatch_log_metric_filter_policy_changes", "cloudwatch_log_metric_filter_root_usage", "cloudwatch_log_metric_filter_security_group_changes", "cloudwatch_log_metric_filter_sign_in_without_mfa", "cloudwatch_log_metric_filter_unauthorized_api_calls", "codeartifact_packages_external_public_publishing_disabled", "codebuild_project_older_90_days", "codebuild_project_user_controlled_buildspec", "dlm_ebs_snapshot_lifecycle_policy_exists", "dynamodb_accelerator_cluster_encryption_enabled", "dynamodb_tables_kms_cmk_encryption_enabled", "ec2_ami_public", "ec2_ebs_default_encryption", "ec2_ebs_public_snapshot", "ec2_ebs_snapshots_encrypted", "ec2_ebs_volume_encryption", "ec2_ebs_volume_snapshots_exists", "ec2_elastic_ip_shodan", "ec2_elastic_ip_unassigned", "ec2_instance_detailed_monitoring_enabled", "ec2_instance_imdsv2_enabled", "ec2_instance_internet_facing_with_instance_profile", "ec2_instance_older_than_specific_days", "ec2_instance_profile_attached", "ec2_instance_public_ip", "ec2_networkacl_allow_ingress_tcp_port_3389", "ec2_securitygroup_allow_wide_open_public_ipv4", "ec2_securitygroup_default_restrict_traffic", "ec2_securitygroup_from_launch_wizard", "ec2_securitygroup_not_used", "ec2_securitygroup_with_many_ingress_egress_rules", "ecr_registry_scan_images_on_push_enabled", "ecr_repositories_lifecycle_policy_enabled", "ecr_repositories_not_publicly_accessible", "ecr_repositories_scan_images_on_push_enabled", "ecr_repositories_scan_vulnerabilities_in_latest_image", "efs_encryption_at_rest_enabled", "efs_have_backup_enabled", "efs_not_publicly_accessible", "eks_cluster_kms_cmk_encryption_in_secrets_enabled", "eks_cluster_network_policy_enabled", "eks_cluster_private_nodes_enabled", "eks_control_plane_endpoint_access_restricted", "eks_control_plane_logging_all_types_enabled", "eks_endpoints_not_publicly_accessible", "elasticache_cluster_uses_public_subnet", "elb_insecure_ssl_ciphers", "elb_internet_facing", "elb_logging_enabled", "elb_ssl_listeners", "elbv2_deletion_protection", "elbv2_desync_mitigation_mode", "elbv2_insecure_ssl_ciphers", "elbv2_internet_facing", "elbv2_listeners_underneath", "elbv2_logging_enabled", "elbv2_ssl_listeners", "elbv2_waf_acl_attached", "glacier_vaults_policy_public_access", "iam_administrator_access_with_mfa", "iam_avoid_root_usage", "iam_aws_attached_policy_no_administrative_privileges", "iam_check_saml_providers_sts", "iam_customer_attached_policy_no_administrative_privileges", "iam_customer_unattached_policy_no_administrative_privileges", "iam_inline_policy_no_administrative_privileges", "iam_no_custom_policy_permissive_role_assumption", "iam_no_expired_server_certificates_stored", "iam_no_root_access_key", "iam_password_policy_expires_passwords_within_90_days_or_less", "iam_password_policy_lowercase", "iam_password_policy_minimum_length_14", "iam_password_policy_number", "iam_password_policy_reuse_24", "iam_password_policy_symbol", "iam_password_policy_uppercase", "iam_policy_allows_privilege_escalation", "iam_policy_attached_only_to_group_or_roles", "iam_policy_no_full_access_to_cloudtrail", "iam_policy_no_full_access_to_kms", "iam_role_administratoraccess_policy", "iam_role_cross_account_readonlyaccess_policy", "iam_role_cross_service_confused_deputy_prevention", "iam_root_hardware_mfa_enabled", "iam_root_mfa_enabled", "iam_rotate_access_key_90_days", "iam_securityaudit_role_created", "iam_support_role_created", "iam_user_accesskey_unused", "iam_user_console_access_unused", "iam_user_hardware_mfa_enabled", "iam_user_mfa_enabled_console_access", "iam_user_no_setup_initial_access_key", "iam_user_two_active_access_key", "iam_user_with_temporary_credentials", "kms_cmk_are_used", "kms_cmk_rotation_enabled", "kms_key_not_publicly_accessible", "neptune_cluster_uses_public_subnet", "opensearch_service_domains_audit_logging_enabled", "opensearch_service_domains_cloudwatch_logging_enabled", "opensearch_service_domains_encryption_at_rest_enabled", "opensearch_service_domains_https_communications_enforced", "opensearch_service_domains_internal_user_database_enabled", "opensearch_service_domains_node_to_node_encryption_enabled", "opensearch_service_domains_not_publicly_accessible", "opensearch_service_domains_updated_to_the_latest_service_software_version", "organizations_account_part_of_organizations", "organizations_delegated_administrators", "organizations_scp_check_deny_regions", "organizations_tags_policies_enabled_and_attached", "rds_instance_backup_enabled", "rds_instance_deletion_protection", "rds_instance_deprecated_engine_version", "rds_instance_enhanced_monitoring_enabled", "rds_instance_integration_cloudwatch_logs", "rds_instance_minor_version_upgrade_enabled", "rds_instance_multi_az", "rds_instance_no_public_access", "rds_instance_storage_encrypted", "rds_instance_transport_encrypted", "rds_snapshots_public_access", "resourceexplorer2_indexes_found", "route53_dangling_ip_subdomain_takeover", "route53_domains_privacy_protection_enabled", "route53_domains_transferlock_enabled", "route53_public_hosted_zones_cloudwatch_logging_enabled", "s3_account_level_public_access_blocks", "s3_bucket_acl_prohibited", "s3_bucket_default_encryption", "s3_bucket_kms_encryption", "s3_bucket_level_public_access_block", "s3_bucket_no_mfa_delete", "s3_bucket_object_lock", "s3_bucket_policy_public_write_access", "s3_bucket_public_list_acl", "s3_bucket_public_write_acl", "s3_bucket_secure_transport_policy", "sagemaker_models_network_isolation_enabled", "sagemaker_models_vpc_settings_configured", "sagemaker_notebook_instance_encryption_enabled", "sagemaker_notebook_instance_root_access_disabled", "sagemaker_notebook_instance_vpc_settings_configured", "sagemaker_notebook_instance_without_direct_internet_access_configured", "sagemaker_training_jobs_intercontainer_encryption_enabled", "sagemaker_training_jobs_network_isolation_enabled", "sagemaker_training_jobs_volume_and_output_encryption_enabled", "sagemaker_training_jobs_vpc_settings_configured", "sns_topics_kms_encryption_at_rest_enabled", "sns_topics_not_publicly_accessible", "sqs_queues_not_publicly_accessible", "sqs_queues_server_side_encryption_enabled", "ssm_document_secrets", "ssm_documents_set_as_public", "ssm_managed_compliant_patching", "ssmincidents_enabled_with_plans", "trustedadvisor_errors_and_warnings", "vpc_different_regions", "vpc_endpoint_connections_trust_boundaries", "vpc_endpoint_services_allowed_principals_trust_boundaries", "vpc_peering_routing_tables_with_least_privilege", "vpc_subnet_different_az", "vpc_subnet_no_public_ip_by_default", "vpc_subnet_separate_private_public", "wellarchitected_workload_no_high_or_medium_risks"]}