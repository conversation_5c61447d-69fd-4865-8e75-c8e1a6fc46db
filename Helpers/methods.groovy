// Methods for deployment preparation
def prepInstance() {
  sh "sudo yum install jq -y"
  sh "pip3 install slack_sdk requests==2.28.2 urllib3==1.26.9 argparse pyyaml"
}

def getAssetVersionFromRepo() {
  commit = sh returnStdout: true, script: "git rev-parse HEAD | tr -d '\n'"
  assetVersion = sh returnStdout: true, script: "echo ${commit}${params.deploymenttag} | md5sum - | tr -d '\n' | cut -d' ' -f1 | tr -d '\n'"

  return assetVersion
}

def installWrangler() {
  sh "sudo npm install -g wrangler"
}

def getDeploymentParameters(stack, region) {
  def deployment = sh(script: "aws ssm get-parameter --region ${variables.DEFAULT_REGION} --name '/${region}/deployment/${stack}' --with-decryption | jq '.Parameter.Value' | tr -d '\n\"'", returnStdout: true)
  def configmap   = sh(script: "aws ssm get-parameter --region ${variables.DEFAULT_REGION} --name '/${region}/configmap/${stack}' --with-decryption | jq '.Parameter.Value' | tr -d '\n\"'", returnStdout: true)
  def app        = sh(script: "aws ssm get-parameter --region ${variables.DEFAULT_REGION} --name '/${region}/app/${stack}' --with-decryption | jq '.Parameter.Value' | tr -d '\n\"'", returnStdout: true)
  def service    = sh(script: "aws ssm get-parameter --region ${variables.DEFAULT_REGION} --name '/${region}/service/${stack}' --with-decryption | jq '.Parameter.Value' | tr -d '\n\"'", returnStdout: true)
  def version    = sh(script: "aws ssm get-parameter --region ${variables.DEFAULT_REGION} --name '/${region}/version/${stack}' --with-decryption | jq '.Parameter.Value' | tr -d '\n\"'", returnStdout: true)
  def image      = sh(script: "aws ssm get-parameter --region ${variables.DEFAULT_REGION} --name '/${region}/image/${stack}' --with-decryption | jq '.Parameter.Value' | tr -d '\n\"'", returnStdout: true)

  return [deployment: deployment, configmap: configmap, app: app, service: service, version: version, image: image]
}

def checkToInstallNodeModules() {
  packageJsonHash = sh returnStdout: true, script: "md5sum package.json | tr -d '\n' | cut -d' ' -f1 | tr -d '\n'"
  s3NodeModulesExists = sh returnStdout: true, script: "aws s3 ls s3://ss-build-artifacts-prod/node_modules/${env.JOB_NAME}/ --region ${variables.DEFAULT_REGION} | grep ${packageJsonHash} | tr -d '\n'"

  if (s3NodeModulesExists == '') {
    echo "NODE_MODULES tarball does not exist in S3. Proceeding to generate and upload to S3"
    sh """
    rm -rf node_modules
    npm cache clean --force
    npm config set strict-ssl=false
    npm install
    tar -czf node_modules.tar.gz node_modules
    aws s3 cp node_modules.tar.gz s3://ss-build-artifacts-prod/node_modules/${env.JOB_NAME}/${packageJsonHash}/node_modules.tar.gz --region ${variables.DEFAULT_REGION} --quiet
    rm -rf node_modules/
    rm -rf node_modules.tar.gz
    """
  }

  return packageJsonHash
}

// Methods to clone repo and necessary files
def setGitConfig() {
  sh "git config --global user.email ${variables.GIT_EMAIL}"
  sh "git config --global user.name ${variables.GIT_USERNAME}"
}

def cloneApp() {
  retry(5) {
    checkout scmGit(
      branches: [[name: "${params.deploymenttag}"]], 
      extensions: [
        submodule(
          reference: '',
          parentCredentials: true,
          recursiveSubmodules: true
        )
      ], 
      userRemoteConfigs: [
        [
          credentialsId: variables.BB_CREDENTIAL_ID,
          url: variables.APP_REPO
        ]
      ]
    )
  }
}

def cloneMicroServicesRepo(repoUrl, branchName) {
  retry(5) {
    checkout scmGit(
      branches: [[name: branchName]],
      extensions: [
        cloneOption(depth: 1),
        submodule(reference: '', parentCredentials: true, recursiveSubmodules: true)
      ],
      userRemoteConfigs: [
        [
          credentialsId: variables.BB_CREDENTIAL_ID,
          url: repoUrl
        ]
      ]
    )
  }
}

def cloneRepo(repoUrl, branchName="master") {
  checkout scmGit(
    branches: [[name: branchName]],
    extensions: [],
    userRemoteConfigs: [
      [
        credentialsId: variables.BB_CREDENTIAL_ID,
        url: repoUrl
      ]
    ]
  )
}

def cloneRepoWithGit(repoUrl, branchName=env.GIT_BRANCH) {
  git branch: branchName, credentialsId: variables.BB_CREDENTIAL_ID, url: repoUrl
}

def cloneConfig() {
  checkout scmGit(
    branches: [[name: variables.CONFIG_REPO_BRANCH]],
    extensions: [], 
    userRemoteConfigs: [
      [
        credentialsId: variables.BB_CREDENTIAL_ID,
        url: variables.CONFIG_REPO
      ]
    ]
  )
}

def cloneEKSYAMLRepo(branchName=env.GIT_BRANCH) {
  git branch: branchName, credentialsId: variables.BB_CREDENTIAL_ID, url: variables.KUBERNETES_YAML_REPO
}

def prepAppRepo() {
  prepInstance()
  dir ('app-v1') {
    cloneApp()
    sh "mkdir -p ./log && touch ./log/server.log"
    sh "aws s3 cp s3://ss-build-artifacts-prod/node_modules/${env.JOB_NAME}/${packageJsonHash}/node_modules.tar.gz node_modules.tar.gz --region ${variables.DEFAULT_REGION} --quiet"
    sh "tar -xzf node_modules.tar.gz --no-same-permissions"
  }

  dir ('surveysparrow-production-config') {
    cloneConfig()
    sh "jq '.version |= \"${version}\"' ${env.JOB_NAME}.json > temp.json && mv temp.json ${env.JOB_NAME}.json"
    sh "cp ${env.JOB_NAME}.json ../app-v1/config/production.json"
    sh "cp files/cassandra/${env.JOB_NAME}.pem ../app-v1/server/external/cassandra/ca.pem"
    sh "curl https://certs.secureserver.net/repository/sf-class2-root.crt -o ../app-v1/server/external/cassandra/sf-class2-root.crt"
    sh "cp files/google/${env.JOB_NAME}.json ../app-v1/config/google.json"
    sh "cp files/sentry/${env.JOB_NAME} ../app-v1/.sentryclirc"
  }
}

def commitAndPush(message, branch="master") {
  sshagent (credentials: [variables.BB_CREDENTIAL_ID]) {
    setGitConfig()
    sh "git add . && git commit -m '$message'"
    sh "git pull origin $branch --rebase"
    sh "git push origin HEAD:$branch"
  }
}

def purgeCache(zone_id, cloudflare_api, cache_type, domains, paths=[""]) {
  // Allow cache_type to be either 'hosts', 'prefixes', files'
  domains_path = []
  for (String domain in domains){
      for(String path in paths){
          if (cache_type == 'files') {
              domains_path.add("\"https://$domain$path\"")
          } else {
            domains_path.add("\"$domain$path\"")
          }
      }
  }
  domains_list = domains_path.join(", ")
  sh "curl --request POST --url https://api.cloudflare.com/client/v4/zones/${zone_id}/purge_cache --header 'Content-Type: application/json' --header 'Authorization: Bearer $cloudflare_api' --data '{\"${cache_type}\": [${domains_list}]}'"
}

def initDocker(region, repo) {
  sh '''
      #!/bin/bash
      if ! docker info &>/dev/null; then
          sudo systemctl start docker  
      fi
  '''
  sh "aws ecr get-login-password --region ${region} | docker login --username ${variables.DOCKER_USERNAME} --password-stdin ${repo}"
}

def mergeSubmodulesToProduction(submodule, branch) {
  if (sh (script: "cd ${submodule}; git ls-remote | grep ${branch}", returnStatus: true) == 0) {
    echo "${branch} exists"
    sh """
    cd ${submodule}
    git config user.email ${variables.GIT_EMAIL}
    git config user.name ${variables.GIT_USERNAME}
    git remote update
    git fetch --all
    git fetch origin ${branch}
    git checkout --track origin/${branch}
    git pull origin ${branch}
    git fetch origin ${variables.PRODUCTION_RELEASE_BRANCH}
    git checkout ${variables.PRODUCTION_RELEASE_BRANCH}
    git pull origin ${variables.PRODUCTION_RELEASE_BRANCH}
    git merge ${branch}
    git push
    git fetch origin ${variables.PRODUCTION_BRANCH}
    git checkout ${variables.PRODUCTION_BRANCH}
    git pull origin ${variables.PRODUCTION_BRANCH}
    git merge ${variables.PRODUCTION_RELEASE_BRANCH}
    git push origin ${variables.PRODUCTION_BRANCH}
    cd ..
    """
  } else {
    echo "${branch} doesn't exist in ${submodule}"
  }
}


// Methods for migrations
def checkMigrations() {
  prepAppRepo()
  dir('app-v1') {
    preMigrationExists  = sh (returnStatus: true, script: "NODE_ENV=production node server/migrate.js pending --migration-type='PRE'")
    preMigrationExists  = (preMigrationExists == '1' || preMigrationExists == 1) ? false : true
    postMigrationExists = sh (returnStatus: true, script: "NODE_ENV=production node server/migrate.js pending --migration-type='POST'")
    postMigrationExists = (postMigrationExists == '1' || postMigrationExists == 1) ? false : true
  }

  return [ preMigrationExists: preMigrationExists, postMigrationExists: postMigrationExists ]
}

def runMigrations(migrationType, microService=null) {
  def jobYaml = "app-v1/kubectl/${env.JOB_NAME}/blue-green-templates/migrations.yaml"
  def shortName = 'app-v1'
  def imageTag = params.deploymenttag

  if (microService != null) {
    shortName = microService['shortName']
    jobYaml = "app-v1/kubectl/${env.JOB_NAME}/${shortName}/migrations.yaml"
    imageTag = "latest"
    updateMigrationConfig(microService)
  } else {
    updateMigrationConfig()
  }
    
  dir(variables.EKS_REPO_FOLDER) {
    cloneRepo(variables.KUBERNETES_YAML_REPO, variables.KUBERNETES_YAML_BRANCH)

    jobSelector = "migration_code=${migrationType}-${shortName}-migration-${env.BUILD_NUMBER}"

    sh "BUILD_NUMBER=${env.BUILD_NUMBER} MIGRATION_TYPE=${migrationType} IMAGE_TAG=${imageTag} envsubst < ${jobYaml} | kubectl create -f -"
    sh "kubectl wait --for=condition=ready pod  --selector=${jobSelector} -n ${variables.DEFAULT_NAMESPACE} --timeout=1200s"
    sh "kubectl logs --follow --selector=${jobSelector} -n ${variables.DEFAULT_NAMESPACE}"
    sh "kubectl logs --selector=${jobSelector} -n ${variables.DEFAULT_NAMESPACE} --tail=-1 > ${migrationType}.txt"

    s3Uri = "s3://ss-build-artifacts-prod/migrationlogs/${env.JOB_NAME}/${params.deploymenttag}/${migrationType}.txt" 
    sh "aws s3 cp ${migrationType}.txt ${s3Uri} --region ${variables.DEFAULT_REGION} --quiet"

    migrationStatus = sh (returnStatus: true, script: "cat ${migrationType}.txt | grep 'RUNNING_MIGRATION_COMMAND_FAILED_WITH_ERRORS' && exit 1 || exit 0")
    migrationLogUrl = sh (returnStdout: true, script: "aws s3 presign ${s3Uri} --region ${variables.DEFAULT_REGION} --expires-in 7200").trim()

    postMigrationsCheck = sh (returnStatus: true, script: "cat ${migrationType}.txt | grep 'POST_MIGRATION_REQUIRED' && exit 1 || exit 0")
  }

  return [ migrationStatus: migrationStatus, postMigrationsCheck: postMigrationsCheck]
}

// Methods to build assets and docker image
def buildAssetsAndDockerImage() {
  parallel(
    "WEBPACK_ADMIN_BUILD": {
      stage('WEBPACK_ADMIN_BUILD') {
        node('DockerBuilderFleet') {
          def nodejs = tool 'Node-v14.18.2'
          withEnv(["PATH+NODE=${nodejs}/bin"]) {
            if (params.compileassets) {
              prepInstance()
              prepAppRepo()
              authenticateToCloudProvider(env.JOB_NAME)
              dir('app-v1') {
                sh "NODE_ENV=production npm run prod-build:webpack-admin-app"
                sh "NODE_ENV=production npm run publish"
              }
            }
          }
        }
      }
    },
    "WEBPACK_EUI_BUILD": {
      stage('WEBPACK_EUI_BUILD') {
        node('DockerBuilderFleet') {
          def nodejs = tool 'Node-v14.18.2'
          withEnv(["PATH+NODE=${nodejs}/bin"]) {
            if (params.compileassets) {
              prepInstance()
              prepAppRepo()
              authenticateToCloudProvider(env.JOB_NAME)
              dir('app-v1') {
                sh "NODE_ENV=production npx npm-run-all -p prod-build:webpack-classic-form prod-build:rollup-reputation-embed prod-build:sass"
                sh "NODE_ENV=production npm run publish"
              }
            }
          }
        }
      }
    },
    "WEBPACK_GENERAL_BUILD": {
      stage('WEBPACK_GENERAL_BUILD') {
        node('DockerBuilderFleet') {
          def nodejs = tool 'Node-v14.18.2'
          withEnv(["PATH+NODE=${nodejs}/bin"]) {
            if (params.compileassets) {
              prepInstance()
              prepAppRepo()
              authenticateToCloudProvider(env.JOB_NAME)
              dir('app-v1') {
                sh "NODE_ENV=production npx npm-run-all -p prod-build:webpack prod-build:webpack-widget"
                sh "NODE_ENV=production npx npm-run-all -p dev-build:vendor-js prod-build:webpack-track_cloudfront_id_sw -s copy:*"
                sh "NODE_ENV=production npx npm-run-all publish*"
              }
            }
          }
        }
      }
    },
    "DOCKER_BUILD": {
      stage('DOCKER_BUILD') {
        node('DockerBuilderFleet') {
          def nodejs = tool 'Node-v14.18.2'
          withEnv(["PATH+NODE=${nodejs}/bin"]) {
            prepInstance()
            prepAppRepo()
            
            sh "sudo service docker start"
            dockerRepo = variables.ECR_REPO_APP[env.JOB_NAME]
            authenticateToContainerRegistry(env.JOB_NAME, dockerRepo)

            dir('app-v1') {
              buildAndPushDockerImage(dockerRepo, env.JOB_NAME, params.deploymenttag)
            }
          }
        }
      }
    }
  )
}

def buildAndPushDockerImage(repo, repoName, deploymentTag) {
  echo "Building the docker image."
  sh "DOCKER_BUILDKIT=1 docker build --label 'env=${repoName}' -f Dockerfiles/production -t ${repo}/${repoName}:${deploymenttag} ."
  sh "docker tag ${repo}/${repoName}:${deploymenttag} ${repo}/${repoName}:latest"
  sh "docker push ${repo}/${repoName}:${deploymenttag}"
  sh "docker push ${repo}/${repoName}:latest"
  sh "docker rmi \$(docker images --filter 'label=env=${repoName}' -q | uniq) --force"
}

def verifyWebpackBuild(assetVersion) {
  assets_url = "${variables.ASSET_REGION_MAP[env.JOB_NAME]}/production/dist-${assetVersion}"
  for (int i = 0; i < variables.CRUCIAL_ASSETS.size(); i++) {
    statusCode = sh returnStdout:true, script: "curl -o /dev/null -s --head --write-out \"%{http_code}\" ${assets_url}/${variables.CRUCIAL_ASSETS[i]}"
    if (statusCode != "200") {
      error("Asset URL ${variables.CRUCIAL_ASSETS[i]} returned status code ${statusCode}")
    }
  }
}

def getValidTill(daysToAdd) {
  def currentDate = java.time.LocalDate.now()
  def newDate = currentDate.plusDays(daysToAdd)
  def dateFormatter = java.time.format.DateTimeFormatter.ofPattern("dd-MM-yyyy")
  def formattedDate = newDate.format(dateFormatter)
  return formattedDate
}

// Using the GCP Project as of now, will update once we grow to multiple projects
def authenticateToCloudProvider(region, accountOrProjectId = variables.GCP_PROJECT_ID) {
  switch(variables.REGIONS_CLOUD_PROVIDERS[region]) {
    case 'GCP':
      withCredentials([file(credentialsId: variables.GCP_SA_JENKINS_KEY, variable: 'GCP_KEY_FILE')]) {
        sh "gcloud auth activate-service-account --key-file=${GCP_KEY_FILE}"
        sh "gcloud config set project ${accountOrProjectId}"
        sh "gcloud config set compute/region ${region}"
      }
      break

    case 'AWS':
      // Authenticate to AWS - In case of other accounts are used in AWS, use and update this method  
      break

    case 'AZURE':
      // Authenticate to Azure - In case of other accounts are used in Azure, use and update this method
      break

    default:
      // Using AWS in same account as Jenkins
      break
  }
}

def authenticateToContainerRegistry(region, repo, accountOrProjectId = variables.GCP_PROJECT_ID) {
  authenticateToCloudProvider(region, accountOrProjectId)

  if (variables.REGIONS_CLOUD_PROVIDERS[region] == 'GCP') {
    sh "gcloud auth configure-docker ${repo}"
  }

  if (variables.REGIONS_CLOUD_PROVIDERS[region] == 'AWS') {
    sh "aws ecr get-login-password --region ${region} | docker login --username AWS --password-stdin ${repo}"
  }
}

def authenticateToK8sCluster(region, cluster, accountOrProjectId = variables.GCP_PROJECT_ID) {
  authenticateToCloudProvider(region, accountOrProjectId)

  if (variables.REGIONS_CLOUD_PROVIDERS[region] == 'GCP') {
    sh "gcloud container clusters get-credentials ${cluster} --region ${region}"
  }

  if (variables.REGIONS_CLOUD_PROVIDERS[region] == 'AWS') {
    sh "aws eks --region ${region} update-kubeconfig --name ${cluster}"
  }
}

// Other EKS Helper Methods
def restartDeployment(deploymentName, namespace) {
  if (checkIfDeploymentExists(deploymentName, namespace)) {
    sh "kubectl rollout restart deployment ${deploymentName} -n ${namespace}"
    sh "kubectl rollout status deployment/${deploymentName} -n ${namespace} --timeout=120m"
  }
}


// Methods to handle deployment
def updateAppConfig(deployment) {
  prepInstance()

  dir ('surveysparrow-production-config') {
    cloneConfig()
    sh "jq '.version |= \"${version}\"' ${env.JOB_NAME}.json > temp.json && mv temp.json ${env.JOB_NAME}.json"
  }

  sh "python3 Helpers/updatecm.py -c app -e ${env.JOB_NAME} -s ${deployment}"
  authenticateToK8sCluster(env.JOB_NAME, variables.CLUSTER_NAME_REGION_MAP[env.JOB_NAME])
  sh "kubectl apply -f Helpers/cm-template.yaml"
}


def updateMigrationConfig(microservice=null) {
  prepInstance()

  dir ('surveysparrow-production-config') {
    cloneConfig()
  }

  microservice = microservice ?: "''"

  sh "python3 Helpers/updatecm.py -c migration -e ${env.JOB_NAME} -m ${microservice}"
  authenticateToK8sCluster(env.JOB_NAME, variables.CLUSTER_NAME_REGION_MAP[env.JOB_NAME])
  sh "kubectl apply -f Helpers/cm-template.yaml"
}


def updateMicroServiceConfig(region, microservice) {
  dir(variables.CONFIG_REPO_FOLDER) {
    cloneConfig()
    sh "jq '.version |= \"${version}\"' microservices/${microservice}/${region}.json > microservices/${microservice}/temp.json && mv microservices/${microservice}/temp.json microservices/${microservice}/${region}.json"
  }

  sh "python3 Helpers/updatecm.py -c microservice -e ${region} -m ${microservice}"
  authenticateToK8sCluster(region, variables.CLUSTER_NAME_REGION_MAP[region])
  sh "kubectl apply -f Helpers/cm-template.yaml"
}

def deployToGreen(deployment) {
  authenticateToK8sCluster(env.JOB_NAME, variables.CLUSTER_NAME_REGION_MAP[env.JOB_NAME])
  appName = variables.REGION_APP_MAP[env.JOB_NAME]
  imageUrl = "556123534406.dkr.ecr.${env.JOB_NAME}.amazonaws.com/${env.JOB_NAME}:${params.deploymenttag}"

  // If we update the deployment's image directly we don't need to update the service.
  // Since this is a Green service, which will be receiving live traffic, avoiding service updates is better

  // To be on a safer side, updating worker image to check if the node server is starting properly.
  def workers = variables.SCALED_DOWN_DCS.contains(env.JOB_NAME) ? variables.SCALED_DOWN_WORKERS : variables.WORKERS
  def workerRollouts = [:]
  for (def i = 0; i < workers.size(); i++) {
    def workerDeployment = workers[i]
    workerRollouts[workerDeployment] = {
      sh "kubectl set image deployment/${appName}-${workerDeployment}-${deployment} ${appName}-${workerDeployment}=${imageUrl} -n surveysparrow"
      sh "kubectl rollout status deployment/${appName}-${workerDeployment}-${deployment} -n surveysparrow --timeout=120m"
    }
  }

  def applications = variables.SCALED_DOWN_DCS.contains(env.JOB_NAME) ? variables.SCALED_DOWN_APPLICATIONS : variables.APPLICATIONS
  def applicationRollouts = [:]
  for (def i = 0; i < applications.size(); i++) {
    def applicationDeployment = applications[i]
    applicationRollouts[applicationDeployment] = {
      sh "kubectl set image deployment/${appName}-${applicationDeployment}-${deployment} ${appName}-${applicationDeployment}=${imageUrl} -n surveysparrow"
      sh "kubectl rollout status deployment/${appName}-${applicationDeployment}-${deployment} -n surveysparrow --timeout=120m"
    }
  }

  parallel workerRollouts
  parallel applicationRollouts
}

def waitForAppDeploymentAndPods(tag) {
  sh "kubectl wait --for=condition=Available deployments -l=tag=${tag} --timeout=120m"
  sh "kubectl wait --for=condition=Ready pods -l=tag=${tag} --timeout=300m"
}

def waitForHAProxyDeploymentAndPods(tag) {
  sh "kubectl wait --for=condition=Available deployments -l=app=${tag} --timeout=120m"
  sh "kubectl wait --for=condition=Ready pods -l=app=${tag} --timeout=300m"
}

def createAppDeployments(deploymentToCreate, stack="", tag="", image="") {
  sh "STACK=${stack} TAG=${tag} IMAGE_TAG=${image} envsubst < ${deploymentToCreate}.yaml | kubectl apply -f -"
}

def deleteAppDeployments(deploymentToDelete, stack, tag, image) {
  sh "STACK=${stack} TAG=${tag} IMAGE_TAG=${image} envsubst < ${deploymentToDelete}.yaml | kubectl delete -f -"
}

def deleteHAProxyDeployment(deploymentToDelete, config) {
  if (deploymentToDelete == 'haproxy') {
    sh "STACK=${config} envsubst < haproxy.yaml | kubectl delete -f -"
  } else {
    sh "CONFIG=${config} envsubst < haproxy-forwarder.yaml | kubectl delete -f -"
  }
}

def createOrUpdateHAProxyDeployment(deploymentToCreateOrUpdate, config) {
  sh "CONFIG=${config} envsubst < ${deploymentToCreateOrUpdate}.yaml | kubectl apply -f -"
}

def updateService(service, deploymentTag) {
  sh "STACK=${service} TAG=${deploymentTag} envsubst < service.yaml | kubectl apply -f -"
}

def deployToBlue(blueDeploymentParams, greenDeploymentParams) {
  dir ('surveysparrow-production-eks') {
    cloneEKSYAMLRepo(variables.KUBERNETES_YAML_BRANCH)

    dir ("app-v1/kubectl/${env.JOB_NAME}/blue-green-templates") {
      sh "aws eks update-kubeconfig --region ${env.JOB_NAME} --name ${env.CLUSTER_NAME}"
      createAppDeployments('worker', blueDeploymentParams.deployment, params.deploymenttag, params.deploymenttag)
      waitForAppDeploymentAndPods(params.deploymenttag)
      env.CURRENT_STAGE = 'BLUE_WORKER_CREATED'

      createAppDeployments('app', blueDeploymentParams.deployment, params.deploymenttag, params.deploymenttag)
      waitForAppDeploymentAndPods(params.deploymenttag)
      env.CURRENT_STAGE = 'BLUE_APP_CREATED'

      deleteAppDeployments('worker', greenDeploymentParams.deployment, greenDeploymentParams.app, greenDeploymentParams.image)
      env.CURRENT_STAGE = 'GREEN_WORKER_DELETED'

      updateService(blueDeploymentParams.service, params.deploymenttag)
      env.CURRENT_STAGE = 'BLUE_SERVICE_UPDATED'
    }
  }

}

// Error Handling Methods
def checkIfDeploymentExists(deploymentName, namespace) {
  deploymentExists = sh( script: "kubectl get deployment ${deploymentName} -n ${namespace}", returnStatus: true )
  return !deploymentExists
}

def checkIfHPAExists(hpaName, namespace) {
  hpaExists = sh (script: "kubectl get hpa ${hpaName} -n ${namespace}", returnStatus: true)
  return !hpaExists
}

def deleteAllBlueDeployments() {
  appName = variables.REGION_APP_MAP[env.JOB_NAME]
  sh "aws eks --region ${env.JOB_NAME} update-kubeconfig --name ${env.CLUSTER_NAME}"
  def deployments = variables.SCALED_DOWN_DCS.contains(env.JOB_NAME) ? variables.SCALED_DOWN_DEPLOYMENTS : variables.DEPLOYMENTS
  for (deployment in deployments) {
    deploymentName = appName + '-' + deployment + "-${blueDeploymentParams.deployment}"
    if (checkIfDeploymentExists(deploymentName, variables.DEFAULT_NAMESPACE)) {
      sh "kubectl delete deployment ${deploymentName} -n surveysparrow"
    }
  }
}

def handleDeploymentFailure(currentStage) {
  if (currentStage == 'DEPLOY_TO_GREEN') {
    sh "aws eks --region ${env.JOB_NAME} update-kubeconfig --name ${env.CLUSTER_NAME}"

    appName        = variables.REGION_APP_MAP[env.JOB_NAME]
    runningImage   = "556123534406.dkr.ecr.${env.JOB_NAME}.amazonaws.com/${env.JOB_NAME}:${greenDeploymentParams.image}"
    attemptedImage = "556123534406.dkr.ecr.${env.JOB_NAME}.amazonaws.com/${env.JOB_NAME}:${params.deploymenttag}"

    def deployments = variables.SCALED_DOWN_DCS.contains(env.JOB_NAME) ? variables.SCALED_DOWN_DEPLOYMENTS : variables.DEPLOYMENTS

    for (deployment in deployments) {
      deploymentName = appName + '-' + deployment + "-${greenDeploymentParams.deployment}"
      deployedImage  = sh(returnStdout: true, script: "kubectl get deployment ${deploymentName} -o=jsonpath='{.spec.template.spec.containers[:1].image}' -n surveysparrow | tr -d '\n'")

      if (deployedImage != runningImage) {
        sh "kubectl set image deployment/${deploymentName} ${appName}-${deployment}=${runningImage} -n surveysparrow"
        sh "kubectl rollout status deployment/${deploymentName} -n surveysparrow --timeout=120m"
      }
    }
  } else {
    dir ('surveysparrow-production-eks') {
      cloneEKSYAMLRepo(variables.KUBERNETES_YAML_BRANCH)

      dir ("app-v1/kubectl/${env.JOB_NAME}/blue-green-templates") {
        switch (currentStage) {
          case 'DEPLOY_TO_BLUE':
            echo "[+] Only Blue ConfigMap is updated. Aborting or Failing deployment."
            break
          case 'BLUE_CONFIG_UPDATED':
            echo "[+] Blue worker created. Deleting it."
            deleteAllBlueDeployments()
            break
          case 'BLUE_WORKER_CREATED':
            echo "[+] Blue app created. Deleting it."
            deleteAllBlueDeployments()
            break
          case 'BLUE_APP_CREATED':
            echo "[+] Green Worker Deleted, recreating it."
            createAppDeployments('worker', greenDeploymentParams.deployment, greenDeploymentParams.app, greenDeploymentParams.image)
            echo "[+] Blue app created. Deleting it."
            deleteAllBlueDeployments()
            break
          case 'GREEN_WORKER_DELETED':
            echo "[+] Green Worker Deleted, recreating it."
            createAppDeployments('worker', greenDeploymentParams.deployment, greenDeploymentParams.app, greenDeploymentParams.image)
            echo "[+] Blue HAProxy created. Deleting it."
            deleteAllBlueDeployments()
            break
          case 'BLUE_HAPROXY_CREATED':
            echo "[+] Green Worker Deleted, recreating it."
            createAppDeployments('worker', greenDeploymentParams.deployment, greenDeploymentParams.app, greenDeploymentParams.image)
            updateService(blueDeploymentParams.service, greenDeploymentParams.app)
            deleteAllBlueDeployments()
            break
          case 'SWITCH_TO_BLUE_GREEN':
            echo "[-] Error while updating HAProxy to Blue Green Config"
            createOrUpdateHAProxyDeployment('haproxy-forwarder', 'green')
            waitForHAProxyDeploymentAndPods('haproxy-forwarder')
            updateService(blueDeploymentParams.service, greenDeploymentParams.app)
            createAppDeployments('worker', greenDeploymentParams.deployment, greenDeploymentParams.app, greenDeploymentParams.image)
            deleteAllBlueDeployments()
            break
          case 'PROMOTE_BLUE_TO_GREEN':
            echo "[-] Error while updating Blue Service to Green"
            updateService(greenDeploymentParams.service, greenDeploymentParams.app)
            createOrUpdateHAProxyDeployment('haproxy-forwarder', 'green')
            waitForHAProxyDeploymentAndPods('haproxy-forwarder')
            createAppDeployments('worker', greenDeploymentParams.deployment, greenDeploymentParams.app, greenDeploymentParams.image)
            deleteAllBlueDeployments()
            break
          case 'PROMOTE_BLUE_SERVICE_TO_GREEN':
            echo "[-] Error while update HAProxy to Green"
            updateService(greenDeploymentParams.service, greenDeploymentParams.app)
            createOrUpdateHAProxyDeployment('haproxy-forwarder', 'green')
            waitForHAProxyDeploymentAndPods('haproxy-forwarder')
            createAppDeployments('worker', greenDeploymentParams.deployment, greenDeploymentParams.app, greenDeploymentParams.image)
            deleteAllBlueDeployments()
            break
          case 'RUNNING_POST_MIGRATIONS':
            echo "[-] Error while running post migrations"
            echo "[-] New Pods already receiving traffic, so proceeding."
            break
          case 'CLEANUP_BLUE':
            echo "[+] Aborted while cleaning up blue. Deleting Old Green Deployments."
            break
          default:
            echo "[+] Caught - ${currentStage}"
            echo "Safe to Ignore, no actions need to be taken."
            break
        }
      }
    }
  }
}


// Post deployment methods
def updateDeploymentParameters(stack, deployedParameters) {
  sh(script: "aws ssm put-parameter --region ${variables.DEFAULT_REGION} --name '/${env.JOB_NAME}/deployment/${stack}' --value '${deployedParameters.deployment}' --type SecureString --overwrite")
  sh(script: "aws ssm put-parameter --region ${variables.DEFAULT_REGION} --name '/${env.JOB_NAME}/configmap/${stack}' --value '${deployedParameters.configmap}' --type SecureString --overwrite")
  sh(script: "aws ssm put-parameter --region ${variables.DEFAULT_REGION} --name '/${env.JOB_NAME}/app/${stack}' --value '${deployedParameters.app}' --type SecureString --overwrite")
  sh(script: "aws ssm put-parameter --region ${variables.DEFAULT_REGION} --name '/${env.JOB_NAME}/service/${stack}' --value '${deployedParameters.service}' --type SecureString --overwrite")
  sh(script: "aws ssm put-parameter --region ${variables.DEFAULT_REGION} --name '/${env.JOB_NAME}/version/${stack}' --value '${deployedParameters.version}' --type SecureString --overwrite")
  sh(script: "aws ssm put-parameter --region ${variables.DEFAULT_REGION} --name '/${env.JOB_NAME}/image/${stack}' --value '${deployedParameters.image}' --type SecureString --overwrite")
}

def updateLambdaEnvVariables(version) {
  sh(script: "aws lambda update-function-configuration --function-name ${variables.BUNDLE_MONITOR_LAMBDA} --region ${variables.DEFAULT_REGION} --environment 'Variables={VERSION=${version}}'")
}

// Other useful methods
def calculatePercentage(start, end) {
  percentage = (start/end) * 100
  percentage = String.format("%.2f", percentage)
  return percentage
}

def getLastSuccessfulBuildDuration(job_name) {
  def job = Jenkins.instance.getItemByFullName(job_name)
  def lastSuccessfulBuild = job.getLastSuccessfulBuild()
  def lastSuccessfulBuildDuration = lastSuccessfulBuild == null ? variables.AVERAGE_DEPLOYMENT_TIME : lastSuccessfulBuild?.duration
  return lastSuccessfulBuildDuration
}

// Other helper methods
def installProwler() {
  sh "python3 -m pip install prowler"
}

def getAllAccounts() {
  roleCreds       = sh script: "aws sts assume-role --role-arn ${variables.PROWLER_ORG_ROLE_ARN} --role-session-name ProwlerEC2ReadAccounts", returnStdout: true
  roleCredsJson   = readJSON(text: roleCreds)
  accountsDetails = sh script:"AWS_ACCESS_KEY_ID=${roleCredsJson.Credentials.AccessKeyId} AWS_SECRET_ACCESS_KEY=${roleCredsJson.Credentials.SecretAccessKey} AWS_SESSION_TOKEN=${roleCredsJson.Credentials.SessionToken} aws organizations list-accounts", returnStdout: true

  return readJSON(text: accountsDetails)
}

def runProwlerInParallel() {
  def threads = [:]
  def accountDetails = getAllAccounts()

  for (def account in accountDetails.Accounts) {
    def accountName = account.Name;
    def accountId = account.Id;
    def regions = variables.ACCOUNT_REGION_MAP[accountId] ? variables.ACCOUNT_REGION_MAP[accountId] : [variables.DEFAULT_REGION]
    threads[accountName] = {
      stage("RUNNING_PROWLER_IN_${accountName}") {
        script {
          sh "SLACK_CHANNEL_ID=${variables.SECURITY_SCANS_CHANNEL_NAME} prowler aws -O ${variables.PROWLER_ORG_ROLE_ARN} -R ${variables.PROWLER_ROLE_TEMPLATE.replace("{ACCOUNT_ID}", accountId)} -f ${regions.join(' ')} -z --slack --log-level DEBUG --log-file ./prowler-log-${accountId}-${new Date().format('ddMMyyyy')}.log --checks-file Helpers/prowlerchecks.json"
          sh "aws s3 cp ./output/ s3://ss-build-artifacts-prod/prowler/ --recursive --quiet"
          sh "aws s3 cp ./prowler-log-${accountId}-${new Date().format('ddMMyyyy')}.log s3://ss-build-artifacts-prod/prowler-logs/ --quiet"
        }
      }
    }
  }

  parallel(threads)
}

def verifyPRChecks(repoName, prId, token) {
  pr_checks = sh returnStdout: true, 
  script: """curl -s --request GET \
    --url 'https://api.bitbucket.org/2.0/repositories/surveysparrow/${repoName}/pullrequests/${prId}/statuses' \
    --header 'Authorization: Basic ${token}' \
    --header 'Content-Type: application/json' | jq '.values[] | select(.state != "SUCCESSFUL")'
    """
      
  if (pr_checks != '') {
    pr_failed_message = "PR Checks failed. Please check the PR link: ${pr_link}"
    currentBuild.description = "PR Checks Failed"
    error(pr_failed_message)
  }
}

def getDiffStateData(diffStateUrl, token) {
  diff_state_data = sh returnStdout: true, 
  script: """curl -s --request GET \
    --url '${diffStateUrl}' \
    --header 'Authorization: Basic ${token}' \
    --header 'Content-Type: application/json'
    """

  return diff_state_data
}

def getPRInfoData(repoName, prId, token) {
  def pr_data = sh returnStdout: true, 
  script: """curl -s --request GET \
    --url 'https://api.bitbucket.org/2.0/repositories/surveysparrow/${repoName}/pullrequests/${prId}' \
    --header 'Authorization: Basic ${token}' \
    --header 'Content-Type: application/json'
    """

  return pr_data
}

def getPRInfoSet(repoName, prId, token){
  def pr_data = getPRInfoData(repoName, prId, token)  
  parsed_pr_data = readJSON text: pr_data

  diff_state_url = parsed_pr_data.links.diffstat.href

  diff_state = getDiffStateData(diff_state_url, token)
  diff_state_data = readJSON text: diff_state

  return [
    "title": parsed_pr_data.title, 
    "author": parsed_pr_data.author.display_name, 
    "dest_branch": parsed_pr_data.destination.branch.name, 
    "src_branch": parsed_pr_data.source.branch.name, 
    "files": diff_state_data.values,
    "state": parsed_pr_data.state,
    "diff_state": diff_state_data
  ]
}

def getTicketTitle(ticketLink, token) {
  ticket_url_tokens = ticketLink.split("/")
  ticket_id = ticket_url_tokens[4]

  ticket_data = sh returnStdout: true, script: """curl -s --request GET \
  --url 'https://surveysparrow.atlassian.net/rest/api/2/issue/${ticket_id}' \
  --header 'Authorization: Basic ${token}' \
  --header 'Accept: application/json'
  """

  parsed_ticket_data = readJSON text: ticket_data

  if (parsed_ticket_data.errorMessages != null) {
    currentBuild.description = "Ticket not found"
    error("Ticket not found")
  }

  ticket_title = parsed_ticket_data.fields.summary
  
  return ["title": ticket_title, "id": ticket_id]
}

def setupScriptExecution(repoUrl, namespace, pullRequestNumber, region, replFile, srcBranch) {
  configmap = readYaml file: "Helpers/cm-template.yaml"
  dir("scripts") {
    cloneRepoWithGit(repoUrl, srcBranch)

    configmap.metadata.name = "repl-script-${pullRequestNumber}"
    configmap.metadata.namespace = namespace
    configmap.data = new HashMap()
    configmap.data["repl-script.js"] = readFile replFile
  }
  writeYaml file: "${region}.repl.configmap.yaml", data: configmap
  repl_script = readFile file: "Helpers/repl-orchestrator.sh"
  cm_yaml = readYaml file: "Helpers/cm-template.yaml"
  cm_yaml.metadata.name = "orchestrator-repl-script"
  cm_yaml.metadata.namespace = namespace
  cm_yaml.data = new HashMap()
  cm_yaml.data["orchestrator.sh"] = repl_script
  writeYaml file: "${region}.orchestrator-repl-script.yaml", data: cm_yaml               
}

def executeReplScript(application, ticket_id, configmap, postScript, namespace, jobContext, pullRequestNumber, region, cluster, bucket, uploadS3, replFile, hooks, aEnvCmd, bEnvCmd) {
  def cleanupJobs = { 
    sh "kubectl delete job --selector=pullrequest=${pullRequestNumber} -n ${namespace}"
    sh "kubectl delete -f ${configmap}"
  }

  withEnv(["KUBECONFIG=/tmp/.${cluster}-config"]) {
    try {
      script_states[region] = [:]

      sh "aws eks update-kubeconfig --name ${cluster} --region ${region}"
                                        
      sh "kubectl apply -f ${configmap}"
      sh "kubectl apply -f ${postScript}"
      // Remove old job pods
      sh "kubectl delete job --selector=pullrequest=${pullRequestNumber} -n ${namespace}"
      
      dir("ContextFiles") {
        sh """
          ENVIRONMENT=${namespace} \
          PR_NUMBER=${pullRequestNumber} \
          REGION=${region} \
          REPL_BUCKET_NAME=${bucket} \
          REPL_APPLICATION_NAME=${application} \
          JOB_ID=${env.BUILD_NUMBER} \
          CHECK_HOOKS="${hooks}" \
          KEY_A="\$(${aEnvCmd})" \
          KEY_B="\$(${bEnvCmd})" \
          UPLOAD_TO_S3=${uploadS3} envsubst < ${jobContext} | kubectl create -f -
        """
      }
      
      pod_name = sh returnStdout:true, script: "kubectl get pods -n ${namespace} --selector pullrequest=${pullRequestNumber} --output json | jq '.items[0].metadata.name' | tr -d '\"'"
      // Pipeline waits here until the job is completed
      sh "kubectl wait --for=condition=ready pod --selector=pullrequest=${pullRequestNumber} -n ${namespace} --timeout=1200s"
      
      def startTime = System.currentTimeMillis()
      sh "kubectl logs --follow --selector=pullrequest=${pullRequestNumber} -n ${namespace}"
      def endTime = System.currentTimeMillis()
      
      def duration = (endTime - startTime) / 1000
      script_states[region]["duration"] = duration
      echo "Duration: ${duration} seconds"
      
      def job_output = sh script: "kubectl logs --selector=pullrequest=${pullRequestNumber} -n ${namespace} --tail=-1", returnStdout: true
      writeFile file: "${region}.${pullRequestNumber}.log", text: job_output
      
      sh "aws s3 cp ${region}.${pullRequestNumber}.log s3://${bucket}/REPL-${application}/${pullRequestNumber}-${env.BUILD_NUMBER}/${region}.${pullRequestNumber}.log"
      notifier.sendAttachmentToJira(ticket_id, "${region}.${pullRequestNumber}.log")
      
      def presigned_log_url = sh(returnStdout: true, script: "aws s3 presign s3://${bucket}/REPL-${application}/${pullRequestNumber}-${env.BUILD_NUMBER}/${region}.${pullRequestNumber}.log --expires-in 86400").trim()
      script_states[region]["logs"] = presigned_log_url 
      
      is_job_succeeded = sh(script: "kubectl get job --selector=pullrequest=${pullRequestNumber} -n ${namespace} --output json | jq '.items[0].status.succeeded'", returnStdout: true).trim()
      if (is_job_succeeded != "1") {
        currentBuild.description = "Script Execution Failed"
        script_states[region]["status"] = "Failed"
      } else {
        script_states[region]["status"] = "Success"
      }
      
      def message_infoset = [
        "region": region,
        "status": script_states[region]["status"],
        "pr_id": pullRequestNumber,
        "file": replFile,
        "logs": presigned_log_url,
        "duration": duration
      ]

      notifier.sendBotMessage("REPL_SCRIPT_RESULT_INFO", mainthread.threadId, message_infoset)
      notifier.sendBotMessage("REPL_SCRIPT_LOGS_INFO", mainthread.threadId, message_infoset)
      
      if (uploadS3 == true) {
        def presigned_url = sh(returnStdout: true, script: "aws s3 presign s3://${bucket}/REPL-${application}/${pullRequestNumber}-${env.BUILD_NUMBER}/${region}.output.tar.gz --expires-in 86400").trim()
        script_states[region]["output"] = presigned_url
        message_infoset["output"] = presigned_url
        notifier.sendBotMessage("REPL_SCRIPT_OUTPUT_INFO", mainthread.threadId, message_infoset)
      }

      notifier.sendReplJiraComment(pullRequestNumber, ticket_id, region, script_states[region]["status"], duration, script_states[region].output, replFile, script_states[region].logs)
    } catch (Exception e) {
      currentBuild.description = "Abort Message Received"
      echo "[!] Abort Message Received. We will clean up the jobs and exit."
      script_states[region]["status"] = "Failed"
      throw e
    } finally {
      cleanupJobs()
    }
  }
}

def mergePR(repoName, prID, base64Token) {
  sh script: """curl -s --request POST \
    --url 'https://api.bitbucket.org/2.0/repositories/surveysparrow/${repoName}/pullrequests/${prID}/merge' \
    --header 'Authorization: Basic ${base64Token}' \
    --header 'Content-Type: application/json' \
    --data '{
      "close_source_branch": true
    }'
    """
}

def runAutomationSanity() {
  build job: 'Qa-Automation-Testing', parameters: [ string(name: 'TestBranch', value: "develop"), string(name: 'TestingEnvironment', value: "${variables.REGION_AUTOMATION_TEST_DC_MAP[env.JOB_NAME]}"), string(name: 'AutomationType', value: 'OTHERS'), string(name: 'TestCases', value: 'production_sanity:60') ], wait: false
}

return this
