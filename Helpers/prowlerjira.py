import os
import json
import requests
import boto3

# Jira Globals
DEFAULT_PROJECT_NAME = 'Development Ops and Security'
DEFAULT_ISSUE_TYPE = 'Task'
DEPLOYED_STATUS = ['Ready for Deployment', 'Deployed']
MAX_ISSUES_PER_REQUEST = 50

HEADERS = {
  'Authorization': 'Basic ' + os.environ['JIRA_TOKEN']
}
ID_NEEDED_FIELDS = {
  'project': [ DEFAULT_PROJECT_NAME ],
  'issuetype': [ DEFAULT_ISSUE_TYPE ],
  'priority': [ 'URGENT', 'High', 'Medium', 'Low' ],
}

# Prowler Globals
STATUS_TO_CREATE_TICKET = ['FAIL', 'MANUAL']
NEEDED_FIELDS = [
  'metadata.event_code',
  'severity',
  'status_code',
  'status_detail',
  'resources.0.uid',
  'remediation.desc',
  'risk_details',
  'finding_info.desc'
]

DYNAMODB_TABLE_NAME = 'prowler_issues'


# Helper Methods
def filter_necessary_fields(finding):
  return { k: finding[k] for k in NEEDED_FIELDS if k in finding }

def flatten_dict(d, parent_key='', sep='.'):
  items = []
  if isinstance(d, dict):
    for k, v in d.items():
      new_key = f"{parent_key}{sep}{k}" if parent_key else k
      if isinstance(v, dict):
        items.extend(flatten_dict(v, new_key, sep=sep).items())
      elif isinstance(v, list):
        for i, item in enumerate(v):
          items.extend(flatten_dict(item, f"{new_key}{sep}{i}", sep=sep).items())
      else:
        items.append((new_key, v))
  elif isinstance(d, list):
    for i, v in enumerate(d):
      new_key = f"{parent_key}{sep}{i}" if parent_key else str(i)
      if isinstance(v, (dict, list)):
        items.extend(flatten_dict(v, new_key, sep=sep).items())
      else:
        items.append((new_key, v))
  else:
    items.append((parent_key, d))
  return dict(items)

def get_findings_from_file(findings_dir):
  dir_content = os.listdir(findings_dir)

  prowler_findings = []
  for file in dir_content:
    file_path = os.path.join(findings_dir, file)
    if file_path.endswith('.json'):
      with open(file_path) as f:
        json_file = json.load(f)
        prowler_findings.extend(json_file)

  return prowler_findings

def get_filtered_findings(prowler_findings):
  REFERENCE_FIELD_PATTERN = "remediation.references."
  findings = {}
  for prowler_finding in prowler_findings:
    status_code = prowler_finding['status_code']
    if status_code not in STATUS_TO_CREATE_TICKET:
      continue
    else:
      finding = flatten_dict(prowler_finding)
      findings.setdefault(finding['metadata.event_code'], {})
      findings[finding['metadata.event_code']]['severity']      = finding['severity'] if 'severity' in finding else ''
      findings[finding['metadata.event_code']]['status_code']   = finding['status_code'] if 'status_code' in finding else ''
      findings[finding['metadata.event_code']]['status_detail'] = finding['status_detail'] if 'status_detail' in finding else ''
      findings[finding['metadata.event_code']]['remediation']   = finding['remediation.desc'] if 'remediation.desc' in finding else ''
      findings[finding['metadata.event_code']]['risk_details']  = finding['risk_details'] if 'risk_details' in finding else ''
      findings[finding['metadata.event_code']]['finding_desc']   = finding['finding_info.desc'] if 'finding_info.desc' in finding else ''
      findings[finding['metadata.event_code']].setdefault('resources', []).append(finding['resources.0.uid'])
      references = [value for key, value in finding.items() if key.startswith(REFERENCE_FIELD_PATTERN)]
      findings[finding['metadata.event_code']]['references'] = '\n'.join(references)

  return findings


def get_id(field_info_arr, value):
  for field in field_info_arr:
    if 'scope' in field:
      continue
    if field['name'] == value:
      return field['id']
    
def chunk_list(list_of_issues, chunk_size):
  for chunk in range(0, len(list_of_issues), chunk_size):
    yield list_of_issues[chunk:chunk + chunk_size]

# JIRA Methods
def get_field_info(field_name):
  jira_url = f"https://surveysparrow.atlassian.net/rest/api/2/{field_name}"
  response = requests.request("GET", jira_url, headers=HEADERS)
  return response.json()

def get_current_epic_id():
  jira_url = "https://surveysparrow.atlassian.net/rest/api/2/search?jql=project=\"SSOPS\"AND type=Epic AND status!=Deployed"
  response = requests.request("GET", jira_url, headers=HEADERS)
  return response.json()['issues'][0]['id']

def get_field_id(field_name, value):
  field_info = get_field_info(field_name)
  return get_id(field_info, value)

def get_all_necessary_ids():
  ids = {}
  for field_name, values in ID_NEEDED_FIELDS.items():
    ids[field_name] = {}
    for value in values:
      ids[field_name][value] = get_field_id(field_name, value)
  return ids

def get_issue_payload(project_id, issuetype_id, priority_id, summary, description, parent_id=None):
  return {
    "fields": {
      "project": {
        "id": project_id
      },
      "issuetype": {
        "id": issuetype_id
      },
      "priority": {
        "id": priority_id
      },
      "parent": {
        "id": parent_id
      },
      "summary": summary,
      "description": description
    }
  }

def create_issue(project_id, issuetype_id, priority_id, summary, description, parent_id=None):
  url = "https://surveysparrow.atlassian.net/rest/api/2/issue"
  payload = get_issue_payload(project_id, issuetype_id, priority_id, summary, description, parent_id)
  response = requests.request("POST", url, headers=HEADERS, json=payload)
  return response.json()

def edit_issue(issue_id, update_obj):
  url = f"https://surveysparrow.atlassian.net/rest/api/2/issue/{issue_id}"
  payload = {
    "fields": update_obj
  }
  response = requests.request("PUT", url, headers=HEADERS, json=payload)
  return response.json()

def create_multiple_issues(issues_list):
  chunked_issues = list(chunk_list(issues_list, MAX_ISSUES_PER_REQUEST))
  issues_created = []
  for chunk in chunked_issues:
    url = "https://surveysparrow.atlassian.net/rest/api/2/issue/bulk"
    payload = {
      "issueUpdates": chunk
    }
    response = requests.request("POST", url, headers=HEADERS, json=payload)
    issues_created.extend(response.json()['issues'])
  return issues_created

def get_issue(issue_id):
  url = f"https://surveysparrow.atlassian.net/rest/api/2/issue/{issue_id}"
  response = requests.request("GET", url, headers=HEADERS)
  return response.json()

def get_aggregated_issue_payload(findings):
  issue_payload = []
  issue_array = []
  jira_field_ids = get_all_necessary_ids()
  project_id = jira_field_ids['project'][DEFAULT_PROJECT_NAME]
  issuetype_id = jira_field_ids['issuetype'][DEFAULT_ISSUE_TYPE]
  parent_id = get_current_epic_id()
  for check in findings:
    issue_array.append(check)
    priority = 'URGENT' if findings[check]['severity'] == 'Critical' else findings[check]['severity']
    priority_id = jira_field_ids['priority'][priority]
    summary = f"[PROWLER] - {findings[check]['finding_desc']} - {check}" if findings[check]['status_code'] == 'FAIL' else f"[PROWLER - MANUAL] - {findings[check]['finding_desc']} - {check}"
    finding_resources = '\n'.join(findings[check]['resources'])
    description = f"*Description:* {findings[check]['finding_desc']}\n\n*Details:* {findings[check]['status_detail']}\n\n*Remediation:* {findings[check]['remediation']}\n\n*Resources:* \n {{code}} {finding_resources} {{code}} \n\n*Reference:* {findings[check]['references']}\n*Risk Details:* {findings[check]['risk_details']}"
    issue_payload.append(get_issue_payload(project_id, issuetype_id, priority_id, summary, description, parent_id))
  return issue_payload, issue_array


# DynamoDB Methods
def get_issues_in_dynamodb():
  dynamodb = boto3.resource('dynamodb', region_name='us-east-1')
  table = dynamodb.Table(DYNAMODB_TABLE_NAME)
  response = table.scan()
  return response['Items']

def delete_item_from_dynamodb(item):
  dynamodb = boto3.resource('dynamodb', region_name='us-east-1')
  table = dynamodb.Table(DYNAMODB_TABLE_NAME)
  table.delete_item(Key=item)

def create_item_in_dynamodb(item):
  dynamodb = boto3.resource('dynamodb', region_name='us-east-1')
  table = dynamodb.Table(DYNAMODB_TABLE_NAME)
  table.put_item(Item=item)

def bulk_create_items_in_dynamodb(issue_arr, issues):
  dynamodb = boto3.resource('dynamodb', region_name='us-east-1')
  table = dynamodb.Table(DYNAMODB_TABLE_NAME)
  with table.batch_writer() as batch:
    for index, issue in enumerate(issues):
      item = { 'check': issue_arr[index], 'ticket_id': issue['key'] }
      response = batch.put_item(Item=item)
  return response

def filter_issues_with_tickets(filtered_findings):
  updated_findings = {}
  issues = get_issues_in_dynamodb()
  ticketed_findings = { issue['check']: issue['ticket_id'] for issue in issues }
  for check in filtered_findings:
    if check in ticketed_findings:
      issue = get_issue(ticketed_findings[check])
      if (issue['fields']['status']['name'] in DEPLOYED_STATUS):
        delete_item_from_dynamodb({ 'check': check, 'ticket_id': ticketed_findings[check]})
        updated_findings[check] = filtered_findings[check]
      else:
        continue
    else:
      updated_findings[check] = filtered_findings[check]
  return updated_findings


if __name__ == '__main__':
  findings_dir              = './output'
  findings                  = get_findings_from_file(findings_dir)
  filtered_findings          = get_filtered_findings(findings)
  filter_ticketed_issues    = filter_issues_with_tickets(filtered_findings)
  issue_payload, issue_arr = get_aggregated_issue_payload(filter_ticketed_issues)
  issues_created_response  = create_multiple_issues(issue_payload)
  bulk_create_items_in_dynamodb(issue_arr, issues_created_response)