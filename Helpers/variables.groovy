ASSET_REGION_MAP = [
  "us-east-1": "https://asset.surveysparrow.com",
  "eu-central-1": "https://asset-eu-ff.surveysparrow.com",
  "ap-south-1": "https://assets-ap-mi.surveysparrow.com",
  "me-central-1": "https://assets-me-uae.surveysparrow.com",
  "eu-west-2": "https://assets-eu-ln.surveysparrow.com",
  "ca-central-1": "https://assets-ca.surveysparrow.com",
  "ap-southeast-2": "https://assets-ap-sy.surveysparrow.com",
  "me-central2-b": "https://assets-me-ksa.surveysparrow.com"
]

REGION_ENTITY_GUID_MAP = [
  "me-central-1": "MTg2Mzc0OXxBUE18QVBQTElDQVRJT058NTQ4OTI1NzE4",
  "ap-south-1": "MTg2Mzc0OXxBUE18QVBQTElDQVRJT058NTMwMDc5NjQ1",
  "us-east-1": "MTg2Mzc0OXxBUE18QVBQTElDQVRJT058NDE5OTA2Njc",
  "eu-central-1": "MzY4MTM0MHxBUE18QVBQTElDQVRJT058NDU5MTQ4NDEx",
  "eu-west-2": "MzY4MTM0MHxBUE18QVBQTElDQVRJT058NTE5MTI4MjY4"
]

REGION_DC_NAME_MAP = [
  'us-east-1': 'US',
  'eu-central-1': 'EU',
  'eu-west-2': 'UK',
  'ap-south-1': 'AP',
  'me-central-1': 'ME',
  'ap-southeast-2': 'SY',
  'ca-central-1': 'CA',
  'me-central2-b': 'KSA'
]

DATA_CENTER_REGION_MAP = [
  "US-VIRGINIA": "us-east-1",
  "EU-FRANKFURT": "eu-central-1",
  "AP-MUMBAI": "ap-south-1",
  "ME-UAE": "me-central-1",
  "UK-LONDON": "eu-west-2",
  "AU-SYDNEY": "ap-southeast-2",
  "CA-CANADA": "ca-central-1"
]

REGION_CODE_DC_NAME_MAP = [
  'US': 'us-east-1',
  'EU': 'eu-central-1',
  'UK': 'eu-west-2',
  'AP': 'ap-south-1',
  'ME': 'me-central-1',
  'SY': 'ap-southeast-2',
  'CA': 'ca-central-1',
  'KSA': 'me-central2-b'
]

REGION_APP_MAP = [
  'us-east-1': 'ss-us-vi',
  'eu-central-1': 'ss-eu-ff',
  'ap-south-1': 'ss-ap-mi',
  'me-central-1': 'ss-me-uae',
  'eu-west-2': 'ss-eu-ln',
  'ap-southeast-2': 'ss-ap-sy',
  'ca-central-1': 'ss-ca',
  'me-central2-b': 'ss-me-ksa'
]

ECR_REPO_APP = [
  'us-east-1': '************.dkr.ecr.us-east-1.amazonaws.com',
  'eu-central-1': '************.dkr.ecr.eu-central-1.amazonaws.com',
  'ap-south-1': '************.dkr.ecr.ap-south-1.amazonaws.com',
  'me-central-1': '************.dkr.ecr.me-central-1.amazonaws.com',
  'eu-west-2': '************.dkr.ecr.eu-west-2.amazonaws.com',
  'ca-central-1': '************.dkr.ecr.ca-central-1.amazonaws.com',
  'ap-southeast-2': '************.dkr.ecr.ap-southeast-2.amazonaws.com',
  'me-central2-b': 'me-central2-docker.pkg.dev/surveysparrow-production/me-central2-b'
]

PLATFORM_ECR_REPO_APP = [
  'us-east-1': '************.dkr.ecr.us-east-1.amazonaws.com',
  'ap-south-1': '************.dkr.ecr.ap-south-1.amazonaws.com',
  'eu-central-1': '************.dkr.ecr.eu-central-1.amazonaws.com',
  'eu-west-2': '************.dkr.ecr.eu-west-2.amazonaws.com',
  'me-central-1': '************.dkr.ecr.me-central-1.amazonaws.com'
]

APPNEST_ECR_REPO_APP = [
  'us-east-1':    '************.dkr.ecr.us-east-1.amazonaws.com',
  'eu-central-1': '************.dkr.ecr.eu-central-1.amazonaws.com',
  'ap-south-1':   '************.dkr.ecr.ap-south-1.amazonaws.com',
  'me-central-1': '************.dkr.ecr.me-central-1.amazonaws.com',
  'eu-west-2': '************.dkr.ecr.eu-west-2.amazonaws.com',
  'ca-central-1': '************.dkr.ecr.ca-central-1.amazonaws.com',
  'ap-southeast-2': '************.dkr.ecr.ap-southeast-2.amazonaws.com'
]

REGION_DYNAMO_MAP = [
  'us-east-1': 'eui_cache_info',
  'eu-central-1': 'eui_cache_info_eu',
  'ap-south-1': 'eui_cache_info_ap',
  'me-central-1': 'eui_cache_info_me',
  'eu-west-2': 'eui_cache_info_eu_ln'
]

GREEN_JOB_NAME_REGION_MAP = [
  'App-v1/GreenDeployments/US-Green': 'us-east-1',
  'App-v1/GreenDeployments/EU-Green': 'eu-central-1',
  'App-v1/GreenDeployments/AP-Green': 'ap-south-1',
  'App-v1/GreenDeployments/ME-Green': 'me-central-1',
  'App-v1/GreenDeployments/UK-Green': 'eu-west-2',
  'App-v1/GreenDeployments/SY-Green': 'ap-southeast-2',
  'App-v1/GreenDeployments/CA-Green': 'ca-central-1',
  'App-v1/GreenDeployments/KSA-Green': 'me-central2-b'
]

BLUEGREEN_JOBNAME_REGION_MAP = [
  'App-v1/BlueGreenDeployments/US-BlueGreen': 'us-east-1',
  'App-v1/BlueGreenDeployments/EU-BlueGreen': 'eu-central-1',
  'App-v1/BlueGreenDeployments/AP-BlueGreen': 'ap-south-1',
  'App-v1/BlueGreenDeployments/ME-BlueGreen': 'me-central-1',
  'App-v1/BlueGreenDeployments/UK-BlueGreen': 'eu-west-2',
  'App-v1/BlueGreenDeployments/SY-BlueGreen': 'ap-southeast-2',
  'App-v1/BlueGreenDeployments/CA-BlueGreen': 'ca-central-1',
  'App-v1/BlueGreenDeployments/KSA-BlueGreen': 'me-central2-b'
]

CLUSTER_NAME_REGION_MAP = [
  'me-central-1': 'ss-production-me',
  'ap-south-1': 'ss-production-ap',
  'us-east-1': 'ss-production-us',
  'eu-central-1': 'ss-production-eu',
  'eu-west-2': 'ss-production-eu-ln',
  'ap-southeast-2': 'ss-production-ap-sy',
  'ca-central-1': 'ss-production-ca',
  'me-central2-b': 'ss-production-me-ksa'
]

COLOR_REGION_MAP = [ 
  "us-east-1": "#B31942",
  "eu-central-1": "#003776",
  "ap-south-1": "#00C0F0",
  "me-central-1": "#4900AD",
  "eu-west-2": "#CF142B",
  "ap-southeast-2": "#012169",
  "ca-central-1": "#D80621",
  "me-central2-b": "#005430"
]

CRUCIAL_ASSETS = [
  "admin.app.bundle.js", "admin.helpers.app.bundle.js", "admin.vendors.app.bundle.js", "Auth.app.bundle.js", "CameraInput.app.bundle.js",
  "catalog.app.bundle.js", "classic_form.app.bundle.js", "Consent.app.bundle.js", "ConstantSum.app.bundle.js", "ContactForm.app.bundle.js",
  "Dashboard.app.bundle.js", "DateTime.app.bundle.js", "Dropdown.app.bundle.js", "EmailInput.app.bundle.js", "employee360_portal.app.bundle.js",
  "employee360_report.app.bundle.js", "eui.app.bundle.js", "FileInput.app.bundle.js", "GroupRank.app.bundle.js", "helpers.app.bundle.js",
  "LabelledOpinionScale.app.bundle.js", "Matrix.app.bundle.js", "Message.app.bundle.js", "MultiChoice.app.bundle.js", "MultiChoicePicture.app.bundle.js",
  "nps_widget.app.bundle.js", "nps-widget-builder.app.bundle.js", "nps.app.bundle.js", "NPSFeedback.app.bundle.js", "NPSScore.app.bundle.js",
  "NumberInput.app.bundle.js", "offline.app.bundle.js", "OpinionScale.app.bundle.js", "PaymentQuestion.app.bundle.js", "PhoneNumber.app.bundle.js",
  "QRCodePrint.app.bundle.js", "QuestionReports.app.bundle.js", "RankOrder.app.bundle.js", "Rating.app.bundle.js", "ResponsesDetailPage.app.bundle.js",
  "Signature.app.bundle.js", "SingleQuestion.app.bundle.js", "Slider.app.bundle.js", "super_admin.app.bundle.js", "SurveyForm.app.bundle.js",
  "TextInput.app.bundle.js", "URLInput.app.bundle.js", "vendors.app.bundle.js", "widget.app.bundle.js","YesNo.app.bundle.js"
]

REGION_AUTOMATION_TEST_DC_MAP = [
  'us-east-1': 'surveysparrow_us_dc',
  'eu-central-1': 'surveysparrow_eu_dc',
  'ap-south-1': 'surveysparrow_ap_dc',
  'me-central-1': 'surveysparrow_me_dc',
  'eu-west-2': 'surveysparrow_uk_dc',
  'ap-southeast-2': 'surveysparrow_sy_dc',
  'ca-central-1': 'surveysparrow_ca_dc'
]

SOC_JOB_NAMES_MAP = [
  'ReputationManagement': [
    'US': 'MicroServices/ReputationManagement/Deployments/US',
    'EU': 'MicroServices/ReputationManagement/Deployments/EU',
  ],
  'TicketManagement': [
    'US': 'MicroServices/TicketManagement/Deployments/US',
    'EU': 'MicroServices/TicketManagement/Deployments/EU',
    'AP': 'MicroServices/TicketManagement/Deployments/AP',
    'ME': 'MicroServices/TicketManagement/Deployments/ME',
    'UK': 'MicroServices/TicketManagement/Deployments/UK',
    'SY': 'MicroServices/TicketManagement/Deployments/SY',
    'CA': 'MicroServices/TicketManagement/Deployments/CA',
    'KSA': 'MicroServices/TicketManagement/Deployments/KSA'
  ],
  'Integrations': [
    'US': 'MicroServices/Integrations/Deployments/US',
    'EU': 'MicroServices/Integrations/Deployments/EU',
    'AP': 'MicroServices/Integrations/Deployments/AP',
    'ME': 'MicroServices/Integrations/Deployments/ME',
    'UK': 'MicroServices/Integrations/Deployments/UK',
    'CA': 'MicroServices/Integrations/Deployments/CA',
    'SY': 'MicroServices/Integrations/Deployments/SY',
    'KSA': 'MicroServices/Integrations/Deployments/KSA'
  ]
]

SUBMODULES = [
  'app-v1': [ 'surveysparrow-analyze', 'surveysparrow-billing', 'surveysparrow-integrations', 'surveysparrow-reputation-management', 'surveysparrow-ticket-management', 'surveysparrow-external-events', 'platform-services', 'surveysparrow-db-models', 'surveysparrow-operations' ],
  'ss-reputation-backend': [ 'surveysparrow-db-models' ],
  'ss-ticket-backend': [ 'surveysparrow-db-models' ],
  'ss-integrations-backend': [ 'surveysparrow-db-models' ]
]

DEFAULT_SOC_DEPLOYMENT = 'backend'
SOC = [
  'ReputationManagement': [
    'backend': [
      'repoUrl': '*****************:surveysparrow/ss-reputation-backend.git',
      'folderName': 'ss-reputation-backend',
      'shortName': 'reputation-backend',
      'service': 'ss-reputation-backend',
      'dcLayerMap': [
        'us-east-1': [
          'appLayers': [ 'application' ],
          'workerLayers': [ 'worker' ]
        ],
        'eu-central-1': [
          'appLayers': [ 'application' ],
          'workerLayers': [ 'worker' ]
        ],
        'ap-south-1': [
          'appLayers': [ 'application' ],
          'workerLayers': [ 'worker' ]
        ],
        'me-central-1': [
          'appLayers': [ 'application' ],
          'workerLayers': [ 'worker' ]
        ],
        'eu-west-2': [
          'appLayers': [ 'application' ],
          'workerLayers': [ 'worker' ]
        ]
      ]
    ]
  ],
  'TicketManagement': [
    'backend': [
      'repoUrl': '*****************:surveysparrow/ss-ticket-backend.git',
      'folderName': 'ss-ticket-backend',
      'shortName': 'ticket-backend',
      'service': 'ss-ticket-backend',
      'dcLayerMap': [
        'us-east-1': [
          'appLayers': [ 'application' ],
          'workerLayers': [ 'worker' ]
        ],
        'eu-central-1': [
          'appLayers': [ 'application' ],
          'workerLayers': [ 'worker' ]
        ],
        'ap-south-1': [
          'appLayers': [ 'application' ],
          'workerLayers': [ 'worker' ]
        ],
        'me-central-1': [
          'appLayers': [ 'application' ],
          'workerLayers': [ 'worker' ]
        ],
        'eu-west-2': [
          'appLayers': [ 'application' ],
          'workerLayers': [ 'worker' ]
        ],
        'ap-southeast-2': [
          'appLayers': [ 'application' ],
          'workerLayers': [ 'worker' ]
        ],
        'ca-central-1': [
          'appLayers': [ 'application' ],
          'workerLayers': [ 'worker' ]
        ]
      ]
    ]
  ],
  'Integrations': [
    'backend': [
      'repoUrl': '*****************:surveysparrow/ss-integrations-backend.git',
      'folderName': 'ss-integrations-backend',
      'shortName': 'integrations-backend',
      'service': 'ss-integrations-backend',
      'dcLayerMap': [
        'us-east-1': [
          'appLayers': [ 'application', 'api' ],
          'workerLayers': [ 'worker' ]
        ],
        'eu-central-1': [
          'appLayers': [ 'application', 'api' ],
          'workerLayers': [ 'worker' ]
        ],
        'ap-south-1': [
          'appLayers': [ 'application', 'api' ],
          'workerLayers': [ 'worker' ]
        ],
        'me-central-1': [
          'appLayers': [ 'application' ],
          'workerLayers': [ 'worker' ]
        ],
        'eu-west-2': [
          'appLayers': [ 'application' ],
          'workerLayers': [ 'worker' ]
        ],
        'ap-southeast-2': [
          'appLayers': [ 'application' ],
          'workerLayers': [ 'worker' ]
        ],
        'ca-central-1': [
          'appLayers': [ 'application' ],
          'workerLayers': [ 'worker' ]
        ]
      ]
    ]
  ]
]

SPARROWPAY_SERVICENAME_MAPPING = [
  'Payments': 'payments',
  'Webhooks': 'webhooks',
  'Gateway': 'gateway',
  'Admin Backend': 'admin-backend',
  'Frontend': 'frontend',
  'Notifier': 'notifier'
]

SPARROWPAY_CLUSTER_NAME = [
  "us-east-1": "platform-production-cluster",
]

SPARROWPAY_SERVICE = [
  "role": "JenkinsWorker",
  "namespace": "sparrowpay",
  "account": "************"
]

BLINK_SERVICE = [
  "role": "JenkinsWorker",
  "namespace": "blink",
  "account": "************"
]

SS_NEXTJS_WEBSITE = [
  "app": "website-nextjs-argocd",
  "namespace": "ss-website",
  "configmap": "nextjs-website-configmap",
  "ecr": "next-client",
  "eksPath": "SurveysparrowWebsite/NextjsWebsite",
  "configFile": "website/nextjs-website.env"
]

SERVICES = [
  "App-v1": [
    "role": "-",
    "namespace": "surveysparrow",
    "account": "************"
  ],
  "Payments": [
    "role": "JenkinsWorker",
    "namespace": "sparrowpay",
    "account": "************"
  ],
  "SparrowPay": [
    "role": "JenkinsWorker",
    "namespace": "sparrowpay",
    "account": "************"
  ],
  "Edith": [
    "role": "JenkinsWorker",
    "namespace": "edith",
    "account": "************"
  ],
]

REPL_APPLICATION = [
  "App-v1": [
    "us-east-1": [
      "jobcontext": "app-v1/kubectl/us-east-1/blue-green-templates/repl.yaml",
      "cluster": "ss-production-us",
      "bucket": "ss-build-artifacts-prod",
      "var_cmd_a": "aws ssm get-parameter --name \"/us-east-1/configmap/green\" --with-decryption --query \"Parameter.Value\" --region us-east-1 --output text",
      "var_cmd_b": ""
    ],
    "ap-south-1": [
      "jobcontext": "app-v1/kubectl/ap-south-1/blue-green-templates/repl.yaml",
      "cluster": "ss-production-ap",
      "bucket": "ss-build-artifacts-prod",
      "var_cmd_a": "aws ssm get-parameter --name \"/ap-south-1/configmap/green\" --with-decryption --query \"Parameter.Value\" --region us-east-1 --output text",
      "var_cmd_b": ""
    ],
    "eu-central-1": [
      "jobcontext": "app-v1/kubectl/eu-central-1/blue-green-templates/repl.yaml",
      "cluster": "ss-production-eu",
      "bucket": "ss-build-artifacts-prod",
      "var_cmd_a": "aws ssm get-parameter --name \"/eu-central-1/configmap/green\" --with-decryption --query \"Parameter.Value\" --region us-east-1 --output text",
      "var_cmd_b": ""
    ],
    "me-central-1": [
      "jobcontext": "app-v1/kubectl/me-central-1/blue-green-templates/repl.yaml",
      "cluster": "ss-production-me",
      "bucket": "ss-build-artifacts-prod",
      "var_cmd_a": "aws ssm get-parameter --name \"/me-central-1/configmap/green\" --with-decryption --query \"Parameter.Value\" --region us-east-1 --output text",
      "var_cmd_b": ""
    ],
    "eu-west-2": [
      "jobcontext": "app-v1/kubectl/eu-west-2/blue-green-templates/repl.yaml",
      "cluster": "ss-production-eu-ln",
      "bucket": "ss-build-artifacts-prod",
      "var_cmd_a": "aws ssm get-parameter --name \"/eu-west-2/configmap/green\" --with-decryption --query \"Parameter.Value\" --region us-east-1 --output text",
      "var_cmd_b": ""
    ],
    "ca-central-1": [
      "jobcontext": "app-v1/kubectl/ca-central-1/blue-green-templates/repl.yaml",
      "cluster": "ss-production-ca",
      "bucket": "ss-build-artifacts-prod",
      "var_cmd_a": "aws ssm get-parameter --name \"/ca-central-1/configmap/green\" --with-decryption --query \"Parameter.Value\" --region us-east-1 --output text",
      "var_cmd_b": ""
    ],
    "ap-southeast-2": [
      "jobcontext": "app-v1/kubectl/ap-southeast-2/blue-green-templates/repl.yaml",
      "cluster": "ss-production-ap-sy",
      "bucket": "ss-build-artifacts-prod",
      "var_cmd_a": "aws ssm get-parameter --name \"/ap-southeast-2/configmap/green\" --with-decryption --query \"Parameter.Value\" --region us-east-1 --output text",
      "var_cmd_b": ""
    ],
  ],
  "Payments": [
    "us-east-1": [
      "jobcontext": "sparrowpay/us-east-1/payments/11-payments-repl.yaml",
      "cluster": "platform-production-cluster",
      "bucket": "ss-build-artifacts-prod",
      "var_cmd_a": "",
      "var_cmd_b": ""
    ]
  ]
]

STATUS_COLOR_MAP = [
  "SUCCESS": "#44FF44",
  "FAILURE": "#FF4444",
  "ABORTED": "#FFA500",
  "INFO": "#FFAA66",
  "STARTED": "#4444FF",
  "FAILED": "#FF4444",
]

STATUS_EMOJI_MAP = [
  'STARTED': ':rocket:',
  'SUCCESS': ':pirate_flag:',
  'FAILED': ':bomb:',
  'ABORTED': ':dizzy_face:',
  'INFO': ':scroll:'
]

CLOUDFLARE_DOMAINS = [
    "www.surveysparrow.com": [
        "zoneid": "69056ad8651a3bdc1e8464bcfa55650d",
        "ruleid": "ad420a405c544dca94987cd7e1807a9d"
    ],
    "surveysparrow.com": [
        "zoneid": "69056ad8651a3bdc1e8464bcfa55650d",
        "ruleid": "ad420a405c544dca94987cd7e1807a9d"
    ],
    "br.surveysparrow.com": [
        "zoneid": "69056ad8651a3bdc1e8464bcfa55650d",
        "ruleid": "ad420a405c544dca94987cd7e1807a9d"
    ],
    "surveysparrow.ae": [
        "zoneid": "bc116b8fc67ee5bbfe6bea301b37c531",
        "ruleid": "6a9f14c6cc4344b385d41c84ae10a5b4"
    ]
]

REGIONS_CLOUD_PROVIDERS  = [
  'us-east-1': 'AWS',
  'eu-central-1': 'AWS',
  'ap-south-1': 'AWS',
  'me-central-1': 'AWS',
  'eu-west-2': 'AWS',
  'ca-central-1': 'AWS',
  'ap-southeast-2': 'AWS',
  'me-central2-b': 'GCP'
]

SCALED_DOWN_DCS          = [ 'eu-west-2', 'me-central2-b', 'ca-central-1', 'ap-southeast-2' ]
SCALED_DOWN_DEPLOYMENTS  = [ 'worker', 'application' ]
SCALED_DOWN_WORKERS      = [ 'worker' ]
SCALED_DOWN_APPLICATIONS = [ 'application' ]

DEPLOYMENTS  = [ 'generic-worker', 'cron-worker', 'notification-worker', 'hulk-worker', 'api', 'application', 'eui', 'reports' ]
WORKERS      = [ 'generic-worker', 'cron-worker', 'notification-worker', 'hulk-worker' ]
APPLICATIONS = [ 'api', 'application', 'eui', 'reports' ]

ORGANIZATIONS_ACCOUNT_NUMBER   = "************"
SECURITY_POLICY_MANAGING_ROLE  = "SecurityPolicyManagerRole"
POLICY_TYPES = [
  "iam": "IDENTITY_POLICY",
  "scp": "SERVICE_CONTROL_POLICY" 
]

APP_REPO                   = "*****************:surveysparrow/app-v1.git"
SPARROWPAY_REPO            = "*****************:surveysparrow/sparrow-pay.git"
BLINK_REPO                 = "*****************:surveysparrow/blink.git"
SECURITY_POLICY_REPO       = "*****************:surveysparrow/ss-security-policies.git"
CONFIG_REPO                = "*****************:surveysparrow/surveysparrow-production-config.git"
KUBERNETES_YAML_REPO       = "*****************:surveysparrow/surveysparrow-production-eks.git"
BULLBOARD_REPO_URL         = "*****************:surveysparrow/bullboard-dashboard.git"
CLOUDFLARE_CODE_REPOSITORY = '*****************:surveysparrow/cloudflare-workers.git'
OFFLINE_APP_REPO           = "*****************:surveysparrow/surveysparrow-offline-app.git"
EDITH_MAILER_REPO          = "*****************:surveysparrow/emailservice-backend.git"

WEBSITE_REPO               = "*****************:surveysparrow/surveysparrow-website.git"
SS_SCRIPT_REPO = "*****************:surveysparrow/ss-scripts.git"

ASSET_BUCKET = "asset.surveysparrow.com"

GIT_EMAIL    = "<EMAIL>"
GIT_USERNAME = "saasdevops"

BASTION_USER = "ec2-user"

OFFLINE_RELEASE_SSM_PATH = "/Android/Version"
OFFLINE_BUNDLE_SSM_PATH = "/Android/bundleVersion"

BASTION_NODES = [
    "US-VIRGINIA": "*************",
    "EU-FRANKFURT": "*************",
    "AP-MUMBAI": "************",
    "ME-UAE": "************",
    "UK-LONDON": "*************",
    "CA-CANADA": "************",
    "AU-SYDNEY": "***********"
]

BASTION_NODES_MARKETPLACE = [
    "us-east-1": "**************",
    "eu-central-1": "***********",
    "ap-south-1": "************",
    "me-central-1": "************",
    "eu-west-2": "************"
]

EKS_REPO_FOLDER    = "surveysparrow-production-eks"
CONFIG_REPO_FOLDER = "surveysparrow-production-config"
APP_REPO_FOLDER    = "app-v1"

FROM_EMAIL           = "<EMAIL>"
REPLY_TO_EMAIL       = "<EMAIL>"

CONFIG_REPO_BRANCH        = "master"
KUBERNETES_YAML_BRANCH    = "master"
PRODUCTION_RELEASE_BRANCH = "production"
PRODUCTION_BRANCH         = "master"

BB_CREDENTIAL_ID  = "BitBucketPrivateKey"
DEFAULT_REGION    = "us-east-1"
DEFAULT_NAMESPACE = "surveysparrow"
WEBSITE_NAMESPACE ="ss-website"
DOCKER_USERNAME   = "AWS"

BASTION_SSH_CREDENTIAL_ID = "BastionSSHSecret"

WEBSITE_STRAPI_ECR_REPO = "strapi"
WEBSITE_NEXTJS_ECR_REPO = "next-client"

SURVEYSPARROW_ARGOCD_DOMAIN   = "argocd.surveysparrow.com"
PLATFORM_ARGOCD_DOMAIN        = "platform-argocd.surveysparrow.com"
SURVEYSPARROW_ARGOCD_USERNAME = "admin"
PLATFORM_ARGOCD_USERNAME      = "admin"

BUNDLE_MONITOR_LAMBDA = "bundleMonitor"

DEFAULT_NOTIFICATION_TOKEN_ID     = "SlackIntegrationToken"
DEFAULT_NOTIFICATION_CHANNEL      = "production-deployments"
REPL_SCRIPT_NOTIFICATION_CHANNEL  = [
  "App-v1": "ss-production-repl-script-notifications",
  "Payments": "ss-production-repl-script-sp-notifications"
]
WEBSITE_NOTIFICATION_CHANNEL       = "production-deployment-website"
PRODUCTION_ACCESS_REQUEST_CHANNEL  = "ss-production-devops-requests"
BLINK_DEPLOYMENT_CHANNEL           = "blink-deployments"
SECURITY_POLICY_CHANNEL            = "security-policies-updates"
EDITH_DEPLOYMENT_CHANNEL           = "edith-production-deployment"
OFFLINE_ANDROID_DEPLOYMENT_CHANNEL = "offline-prod-release"
PRODUCTION_DEPLOYMENTS_CHANNEL_ID  = "C03L60RCN0L"
AVERAGE_DEPLOYMENT_TIME            = 3000000 // 50Mins
PAUSE_QUEUES_DYNAMODB_TABLE        = "pause-queues"

PROWLER_ROLE_TEMPLATE       = "arn:aws:iam::{ACCOUNT_ID}:role/ProwlerScannerRole"
PROWLER_ORG_ROLE_ARN        = "arn:aws:iam::************:role/ProwlerEC2RoleReadOrg"
SECURITY_SCANS_CHANNEL_NAME = "security-scans"

WEBSITE_NEXTJS_ENV_FILE_NAME = "website/nextjs-website.env"
WEBSITE_STRAPI_ENV_FILE_NAME = "website/strapi-website.yaml"

WEBSITE_NEXTJS_BUNDLE_PARAMETER  = '/website/BundleVersion'
WEBSITE_NEXTJS_VERSION_PARAMETER = '/website/version'
WEBSITE_STRAPI_PARAMETER         = '/strapi/imageid'
WEBSITE_STRAPI_VERSION_PARAMETER = '/strapi/version'

ADMIN_BACKEND_SECRET_ID          = "sparrowpay-admin-backend-production"

WEBSITE_NEXTJS_CONFIGMAP_NAME       = "nextjs-website-configmap"
WEBSITE_EKS_CONFIGMAP_NEXTJS_PATH   = "SurveysparrowWebsite/NextjsWebsite/02-nextjs-website-configmap.yaml"
WEBSITE_EKS_NEXTJS_DEPLOYMENT_PATH  = "SurveysparrowWebsite/NextjsWebsite/01-nextjs-website-deployment.yaml"
WEBSITE_EKS_STRAPI_DEPLOYMENT_PATH  = "SurveysparrowWebsite/StrapiWebsite/01-strapi-website-deployment.yaml"


ANDROID_DEFAULT_LANE = "android production"
ANDROID_DEFAULT_BUILD_ENVIRONMENT = "production"

ACCOUNT_REGION_MAP = [
  "************": [
    "us-east-1", "eu-central-1", "ap-south-1", "me-central-1", "eu-west-2", "ap-southeast-2", "ca-central-1"
  ],
  "************": [
    "us-east-1"
  ],
  "************": [
    "us-east-1", "ap-south-1", "us-east-2"
  ],
  "************": [
    "us-east-1", "ap-south-1", "eu-central-1", "me-central-1"
  ],
  "************": [
    "us-east-1", "ap-south-1", "eu-central-1", "me-central-1"
  ],
  "************": [
    "us-east-1", "us-east-2"
  ],
  "************": [
    "us-east-1", "eu-central-1"
  ],
  "************": [
    "us-east-1"
  ],
  "************": [
    "us-east-1"
  ],
  "************": [
    "us-east-1"
  ],
  "************": [
    "us-east-1"
  ]
]

CLOUDFLARE_ACCOUNT_ID = "bc116b8fc67ee5bbfe6bea301b37c531"
GCP_PROJECT_ID        = "surveysparrow-production"

DEFAULT_JIRA_PROJECT_KEY = "SSE"
return this
