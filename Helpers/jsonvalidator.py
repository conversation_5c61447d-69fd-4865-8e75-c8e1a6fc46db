import argparse
import json

def is_valid_json(job_conditions):
	for job_condition in job_conditions:
		connector = job_condition.get('connector', '').lower()
		queue = job_condition.get('queue', {})
		name = queue.get('name', '')
		type_val = queue.get('type')
		conditions = queue.get('conditions', [])
		invalid_function = 'Invalid function type'
		invalid_condition_value = 'Invalid condition value'
		invalid_condition = 'Invalid condition'

		if connector not in ['and', 'or']:
			print('Invalid connector')
			return False
		if not name or not isinstance(name, str):
			print('Invalid queue name')
			return False
		if type_val == '' or (isinstance(type_val, list) and not type_val):
			print(invalid_function)
			return False
		if type_val:
				if isinstance(type_val, list):
						for value in type_val:
								if not value or not isinstance(value, str):
										print(invalid_function)
										return False
				elif not isinstance(type_val, str):
						print(invalid_function)
						return False
		if not conditions or not isinstance(conditions, list):
				print(invalid_condition)
				return False
		for condition in conditions:
				if not isinstance(condition, dict) or not condition:
						print(invalid_condition)
						return False
				for key, value in condition.items():
						if isinstance(value, list):
								if not value:
									print(invalid_condition_value)
									return False
								for element in value:
										if not element:
												print(invalid_condition_value)
												return False
										if not (isinstance(element, str) or isinstance(element, int)):
												print(invalid_condition_value)
												return False
						elif not (isinstance(value, str) or isinstance(value, int)):
								print(invalid_condition_value)
								return False
		return True

def main():
	try:
		parser = argparse.ArgumentParser(description="Utility to validate json")
		parser.add_argument('-j',
												'--json',
												help="Validate json",
												type=str,
												required=True)
		args = parser.parse_args()
		print(args.json)
		job_conditions = json.loads(args.json)
		if is_valid_json(job_conditions):
			print('Valid json')
			exit(0)
		else:
			print('Invalid json')
			exit(1)
	except Exception as e:
		print(e)
		exit(1)

if __name__ == "__main__":
	main()