from requests import request
import argparse
import traceback

def expression_start_fragment(host):
    return f"(http.host in {{\"{host}\" \"www.{host}\"}} and http.request.uri.path matches \"^/("

def expression_end_fragment():
    return f")(?:/)?$\")"

def expression_gen(start_fragement, path_regex, end_fragment):
    i = 0
    for path in path_regex:
        path_regex[i] = f"({path})"
        i+=1

    full_expression = f"{start_fragement}{'|'.join(path_regex)}{end_fragment}"
    return full_expression

def get_origin_rules(zone_id, api_key):
    url = f"https://api.cloudflare.com/client/v4/zones/{zone_id}/rulesets/phases/http_request_origin/entrypoint"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    response = request("GET", url, headers=headers)
    return response.json()

def search_rule_by_id(rule_id, rules):
    origin_rules = rules['result']['rules']
    origin_rule = {}
    for rule in origin_rules:
        if rule['id'] == rule_id:
            origin_rule = rule
            origin_rules.remove(rule)
            break
    return origin_rule, origin_rules

def update_origin_rule(zone_id, ruleset_id, expression, api_key, old_rule, other_rules):
    url = f"https://api.cloudflare.com/client/v4/zones/{zone_id}/rulesets/phases/http_request_origin/entrypoint"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    new_rule = {
        "action": "route",
        "action_parameters": old_rule["action_parameters"],
        "ref": ruleset_id,
        "expression": expression,
        "description": old_rule["description"]
    }
    other_rules.append(new_rule)
    payload = {
        "rules": other_rules
    }
    response = request("PUT", url, headers=headers, json=payload)
    return response.json()

def merge_path_list(existing_path, paths, action, old_path =[]):
    path_list = [value for value in existing_path]
    if action == "add":
        for path in paths:
            if path not in path_list: path_list.append(path)
            else: exit(f"Path already exists in the rule {path}")
    elif action == "delete":
        for path in paths:
            if path in path_list: path_list.remove(path)
            else: exit(f"Can't find the specified path in Cloudflare: {path}")
    elif action == "replace":
        for path in old_path:
            if path in path_list: path_list.remove(path)
            else: exit(f"Can't find the specified path in Cloudflare: {path}")
        for path in paths:
            if path not in path_list: path_list.append(path)
            else: exit(f"Path already exists in the rule {path}")
    return path_list

def generate_path_expression(path_list, mode, action):
    _path_list_expression = []
    if mode == "prefix" and (action == "add" or action == "replace"):
        for path in path_list:
            _path_list_expression.append(f"{path}.*")
    elif mode == "fixed" or action == "delete":
        return path_list
    return _path_list_expression

def verify_path(mode, action, path_list, old_path = []):
    if action == "add" or action == "replace":
        for path in path_list:
            if path[0] == "/" or path[-1] == "/" or path[-2:] == ".*" or path[-1] == "*": return False
            if mode == "prefix" and path+".*" in old_path: exit(0)
            elif mode == "fixed" and path in old_path: exit(0)
    if action == "delete":
        for path in path_list:
            if path[0] == "/" or path[-1] == "/": return False
    if action == "replace":
        if not old_path: return False
        for path in old_path:
            if path[0] == "/" or path[-1] == "/": return False
    return True

def main(args):
    try:
        if not verify_path(args.mode, args.action, args.path, args.oldpath): exit("Invalid path syntax")

        res_rules = get_origin_rules(args.zoneid, args.apikey)
        rule, other_rules = search_rule_by_id(args.ruleid, res_rules)

        start_fragement = expression_start_fragment(args.host)
        end_fragment = expression_end_fragment()

        rule_expression = rule["expression"]
        cropped_rule_expressions = rule_expression.replace(start_fragement, '').replace(end_fragment, '')
        rule_expressions_sanitized = cropped_rule_expressions.replace("(", "").replace(")", "").split("|")

        new_path_list = generate_path_expression(args.path, args.mode, args.action)
        update_expression_rule = merge_path_list(rule_expressions_sanitized, new_path_list, args.action, args.oldpath)

        new_expression = expression_gen(start_fragement, update_expression_rule, end_fragment)

        update_response = update_origin_rule(args.zoneid, args.ruleid, new_expression, args.apikey, rule, other_rules)
        update_rule, all_rules = search_rule_by_id(args.ruleid, update_response)
        print(update_rule["expression"])
    except Exception as e:
        print("Error: ", e)
        print(traceback.format_exc())
        exit(1)

parser = argparse.ArgumentParser(description="Utility to update origin rule in cloudflare")
parser.add_argument('-z', '--zoneid', help="Zone ID", type=str, required=True)
parser.add_argument('-H', '--host', help="Host", type=str, required=True)
parser.add_argument('-r', '--ruleid', help="Ruleset ID", type=str, required=True)
parser.add_argument('-A', '--action', help="Action type", type=str, required=True, choices=["add", "delete", "replace"])
parser.add_argument('-a', '--apikey', help="API Key", type=str, required=True)
parser.add_argument('-op', '--oldpath', help="Old path to be replaced", nargs='+', type=str, default=[])
parser.add_argument('-p', '--path', help="Path to be added", nargs='+', type=str, required=True)
parser.add_argument('-m', '--mode', help="Regex type", type=str, choices=["fixed", "prefix"])

args = parser.parse_args()

main(args)