import time
import os
import argparse
import boto3

code_deploy_client = boto3.client('codedeploy', region_name=os.environ['REGION'])
ec2_client = boto3.client('ec2', region_name=os.environ['REGION'])

def wait_till_deployment_completes(deployment_id):
  try:
    # get the deployment status
    while True:
      response = code_deploy_client.get_deployment(
          deploymentId=deployment_id
      )

      # print the deployment status
      print('Deployment Status -> ', response['deploymentInfo']['status'])
      if response['deploymentInfo']['status'] == 'Succeeded':
        break
      
      if response['deploymentInfo']['status'] == 'Failed':
        raise ValueError("Deployment Failed")
    
      else:
        time.sleep(20)

  except Exception as e:
    print(e)
    return

def deploy_application(application_name, deployment_group_name, bucket_name, key_name):
  try:
    # create a deployment
    response = code_deploy_client.create_deployment(
      applicationName=application_name,
      deploymentGroupName=deployment_group_name,
      autoRollbackConfiguration={
        'enabled': True,
        'events': [
            'DEPLOYMENT_FAILURE',
        ]
      },
      revision={
          'revisionType': 'S3',
          's3Location': {
              'bucket': bucket_name,
              'key': key_name,
              'bundleType': 'zip'
          }
      },
      fileExistsBehavior='OVERWRITE'
    )

    # get the deployment id
    deployment_id = response['deploymentId']
    wait_till_deployment_completes(deployment_id)

  except Exception as e:
    print(e)
    return

def main():
    try:
        parser = argparse.ArgumentParser(description="Utility to deploy to CodeDeploy")
        parser.add_argument("-d", "--deploy", help="Deploy the code", action="store_true")
        parser.add_argument("-a", "--application", help="Application Name to deploy to", type=str)
        parser.add_argument("-g", "--group", help="Deployment Group Name to deploy to", type=str)
        parser.add_argument("-b", "--bucket", help="S3 Bucket Name to deploy from", type=str)
        parser.add_argument("-k", "--key", help="S3 Key Name to deploy from", type=str)
        args = parser.parse_args()

        if args.deploy and args.application and args.group and args.bucket and args.key:
          deploy_application(args.application, args.group, args.bucket, args.key)

    except Exception as e:
        print(e)
        return

if __name__ == "__main__":
    main()