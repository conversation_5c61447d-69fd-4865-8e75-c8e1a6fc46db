
// Check difference between config files and return status
def checkFileDiff(String currentConfig , String incomingConfig) 
{
    // This script tag is needed 
    script 
    {
        def currentConfigContent = readFile(currentConfig).trim()
        def incomingConfigContent = readFile(incomingConfig).trim()

        try 
        {
            echo "Checking the Diff between the current and incoming file"
            def diffOutput = sh(script: "diff -uw $currentConfig $incomingConfig", returnStdout: true ).trim()
            echo "Diff Output: ${diffOutput}"
            
            // Check if there is a difference
            if ( diffOutput.isEmpty() ) 
            {
                echo "No File Changes $diffOutput"
                return true
            }
        } catch (Exception diffException) {
            echo "Files has some config Changes"
            return false
        }
    }
}

// Getting the current cloudwatch group
def getCloudWatchLogGroup( String filePath ) 
{
    // 
    echo "Read all lines from the file"
    def lines = readFile(filePath).trim().split('\n')

    //
    echo "Find the line containing 'cloudwatchLogGroup:'"
    def cloudwatchLogGroupLine = lines.find { line ->
        line.trim().startsWith('CLOUDWATCH_GROUP_NAME:')
    }

    echo "The CloudwatchGroupLine: ${cloudwatchLogGroupLine}"

    // 
    echo "Return the line, or null if not found"
    return cloudwatchLogGroupLine
}

// Creating a new cloudwatch group
def createCloudWatchLogStream(String logGroupName , String logStreamName) 
{
    // 
    echo "Creating a the CloudWatch log stream"
    def createStreamCommand = "aws logs create-log-stream --region ${env.AWS_DEFAULT_REGION} --log-group-name $logGroupName --log-stream-name $logStreamName"

    // 
    echo "Executing command and capture the exit status"
    def createStreamStatus = sh(script: createStreamCommand, returnStatus: true)

    // Check if the exit status is 0 (i.e) => success
    // Not checking "returnStdout" of createStreamStatus becaue
    // this command is not returning any status code output so using "returnStatus"
    echo "Checking Create Stream Status: ${createStreamStatus}"
    if (createStreamStatus == 0) {
        echo "CloudWatch log stream '$logStreamName' created successfully in log group '$logGroupName'"
    } else {
        error("Failed to create CloudWatch log stream..")
        currentBuild.result = 'FAILURE'
    }
}

return this
