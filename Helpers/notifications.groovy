color_region_map = [ 
  "us-east-1": "#B31942",
  "eu-central-1": "#003776",
  "ap-south-1": "#00C0F0",
  "me-central-1": "#4900AD",
  "eu-west-2": "#CF142B",
  "ap-southeast-2": "#012169",
  "ca-central-1": "#D80621",
]

def getSlackMessageF2(message_type="", data_obj = [:]) {
  message = ""
  wrap([$class: 'BuildUser']) {
    switch (message_type) {
      // Marketplace Sparrowapps Deployment
      case 'MARKETPLACE_SPARROWAPPS_DEPLOYMENT_STARTED':
        message = ":airplane_departure: *APPNEST PRODUCTION SPARROWAPPS DEPLOYMENT* \n\n\nRegion: *`${env.AWS_DEFAULT_REGION}`* \nDeployment of Branch Name: `${params.BranchName}` \nStarted By: `${env.BUILD_USER}` \nhas *STARTED* !! \n\n<${env.BUILD_URL}|Click here for more details>"
        break

      case 'MARKETPLACE_SPARROWAPPS_DEPLOYMENT_SUCCESS':
        message = ":airplane_arriving: *APPNEST PRODUCTION SPARROWAPPS DEPLOYMENT* \n\n\nRegion: *`${env.AWS_DEFAULT_REGION}`* \nDeployment of Branch Name:  `${params.BranchName}` \nStarted By: `${env.BUILD_USER}` \n!!! is `Successfully Completed` \n\n<${env.BUILD_URL}|Click here for more details>"
        break

      case 'MARKETPLACE_SPARROWAPPS_DEPLOYMENT_FAILURE':
        message = ":red_circle: *APPNEST PRODUCTION SPARROWAPPS DEPLOYMENT* \n\n\nRegion: *`${env.AWS_DEFAULT_REGION}`* \nDeployment of Branch Name:  `${params.BranchName}` \nStarted By: `${env.BUILD_USER}` \n has `Failed` !!! \n\n<${env.BUILD_URL}|Click here for more details>"
        break

      case 'MARKETPLACE_SPARROWAPPS_DEPLOYMENT_ABORTED':
        message = ":construction: *APPNEST PRODUCTION SPARROWAPPS DEPLOYMENT* \n\n\nRegion: *`${env.AWS_DEFAULT_REGION}`* \nDeployment of Branch Name: `${params.BranchName}` \nStarted By: `${env.BUILD_USER}` \nhas been `ABORTED` \n\n<${env.BUILD_URL}|Click here for more details>"
        break
      
      case 'MARKETPLACE_SPARROWAPPS_DOCKER_BUILD_STARTED':
        message = ":rocket: *APPNEST PRODUCTION SPARROWAPPS DOCKER BUILD* \n\n\nRegion: *`${env.AWS_DEFAULT_REGION}`* \nDeployment of Branch Name: `${params.BranchName}` \nStarted By: `${env.BUILD_USER}` \nhas been `STARTED` \n\n<${env.BUILD_URL}|Click here for more details>"
        break

      case 'MARKETPLACE_SPARROWAPPS_DOCKER_BUILD_SUCCESS':
        message = ":sparrow_love: *APPNEST PRODUCTION SPARROWAPPS DOCKER BUILD* \n\n\nRegion: *`${env.AWS_DEFAULT_REGION}`* \nDeployment of Branch Name: `${params.BranchName}` \nStarted By: `${env.BUILD_USER}` \nhas been `Successfully Completed` \n\n<${env.BUILD_URL}|Click here for more details>"
        break

      // SOC Deployment Notifications
      case 'SOC_DEPLOYMENT_TRIGGERED':
        message = ":anchor: *${env.JOB_NAME} deployment triggered!*\n\nRelease Branch - `${params.releasebranch}`\nStarted by - `${env.BUILD_USER}`"
        break
      case 'SOC_TAG_CREATED':
        message = ":canoe: *${env.JOB_NAME} PR Merged, deployment tag created.*\n\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${GIT_TAG_FULL}`\nStarted by - `${env.BUILD_USER}`"
        break
      case 'SOC_DEPLOYMENT_STARTED_ALL_DCS':
        message = ":speedboat: *${env.JOB_NAME} Deployment started in all DCs!*\n\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${GIT_TAG_FULL}`\nStarted by - `${env.BUILD_USER}`"
        break
      case 'SOC_DEPLOYMENT_SUCCESSFUL_ALL_DCS':
        message = ":pirate_flag: *${env.JOB_NAME} Deployment done in all DCs!!*\n\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${GIT_TAG_FULL}`\nStarted by - `${env.BUILD_USER}`"
        break
      case 'SOC_DEPLOYMENT_STARTED':
        message = ":anchor: *${env.JOB_NAME} Deployment Triggered!*\n\nDC - `${regionName}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\nStarted by - `${env.BUILD_USER}`"
        break
      case 'SOC_BUILD_STARTED':   
        message = ":speedboat: *${env.JOB_NAME} Build started!*\n\nDC - `${regionName}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\nStarted by - `${env.BUILD_USER}`"
        break
      case 'SOC_BUILD_FAILED':
        message = ":bomb: *${env.JOB_NAME} Build failed!*\n\nDC - `${regionName}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\nStarted by - `${env.BUILD_USER}`\n\n<${env.BUILD_URL}console|Check logs here>"
        break
      case 'SOC_DEPLOYMENT_APPROVAL':
        message = ":ship: *${env.JOB_NAME} Deployment started!*\n\nDC - `${regionName}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\nStarted by - `${env.BUILD_USER}`\n\nAwaiting approval to proceed with the deployment. <${env.BUILD_URL}input|Click here to approve>"
        break
      case 'SOC_DEPLOYMENT_SUCCESS':
        message = ":pirate_flag: *${env.JOB_NAME} Deployment successful!!*\n\nDC - `${regionName}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\nStarted by - `${env.BUILD_USER}`"
        break
      case 'SOC_DEPLOYMENT_FAILED':
        message = ":bomb: *${env.JOB_NAME} Deployment failed!*\n\nDC - `${regionName}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\nStarted by - `${env.BUILD_USER}`\n\n<${env.BUILD_URL}console|Check logs here>"
        break
      case 'SOC_DEPLOYMENT_ABORTED':
        message = ":dizzy_face: *${env.JOB_NAME} Deployment aborted!*\n\nDC - `${regionName}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\nStarted by - `${env.BUILD_USER}`"
        break
      case 'SOC_CONFIG_UPDATE_STARTED':
        message = ":gear: *${microService} Config Update started!*\n\nDC - `${params.region}`\nStarted by - `${env.BUILD_USER}`\nReason - `${params.reason}`"
        break
      case 'SOC_CONFIG_UPDATE_SUCCESSFUL':
        message = ":pirate_flag: *${microService} Config Update successful!*\n\nDC - `${params.region}`\nStarted by - `${env.BUILD_USER}`\nReason - `${params.reason}`"
        break
      case 'SOC_CONFIG_UPDATE_FAILED':
        message = ":bomb: *${microService} Config Update failed!*\n\nDC - `${params.region}`\nStarted by - `${env.BUILD_USER}`\nReason - `${params.reason}`"
        break
      case 'SOC_CONFIG_UPDATE_ABORTED':
        message = ":dizzy_face: *${microService} Config Update aborted!*\n\nDC - `${params.region}`\nStarted by - `${env.BUILD_USER}`\nReason - `${params.reason}`"
        break
      case 'SOC_WORKER_CONFIG_UPDATE_STARTED':
        message = ":gear: *${microService} Worker Config Update started!*\n\nDC - `${params.region}`\nStarted by - `${env.BUILD_USER}`\nReason - `${params.reason}`"
        break
      case 'SOC_WORKER_CONFIG_UPDATE_SUCCESSFUL':
        message = ":pirate_flag: *${microService} Worker Config Update successful!*\n\nDC - `${params.region}`\nStarted by - `${env.BUILD_USER}`\nReason - `${params.reason}`"
        break
      case 'SOC_WORKER_CONFIG_UPDATE_FAILED':
        message = ":bomb: *${microService} Worker Config Update failed!*\n\nDC - `${params.region}`\nStarted by - `${env.BUILD_USER}`\nReason - `${params.reason}`"
        break
      case 'SOC_WORKER_CONFIG_UPDATE_ABORTED':
        message = ":dizzy_face: *${microService} Worker Config Update aborted!*\n\nDC - `${params.region}`\nStarted by - `${env.BUILD_USER}`\nReason - `${params.reason}`"
        break

      case 'BASTION_USER_SUCCESS':
        message = ":pirate_flag: *Bastion User Created Successfully!*\n\nUsername - `${username}`\nValid Till - `${bastion_valid_till}`\nRegion - `${params.region}` \nCreated by - `${env.BUILD_USER}`\nAccess Approved by - `${approved_by}`"
        break
      case 'BASTION_USER_UPDATE_INFO':
        message = ":gear: *SSH Key Updated!*\n\nUsername - `${username}`\nRegion - `${params.region}` \nUpdated by - `${env.BUILD_USER}`"
        break
      case 'BASTION_USER_DELETE_INFO':
        message = ":bomb: *Bastion User Deleted!*\n\nUsername - `${username}`\nRegion - `${params.region}` \nDeleted by - `${env.BUILD_USER}`"
        break

        //Multi Region Notifications
        case 'BASTION_USER_SUCCESS_MULTI':
        message = ":pirate_flag: *Bastion User Created Successfully!*\n\nUsername - `${username}`\nValid Till - `${bastion_valid_till}`\nRegions - `${env.regionsList}` \nCreated by - `${env.BUILD_USER}`\nAccess Approved by - `${approved_by}`"
        break
        case 'BASTION_USER_UPDATE_INFO_MULTI':
        message = ":gear: *SSH Key Updated!*\n\nUsername - `${username}`\nRegions - `${env.regionsList}` \nUpdated by - `${env.BUILD_USER}`"
        break
        case 'BASTION_USER_DELETE_INFO_MULTI':
        message = ":bomb: *Bastion User Deleted!*\n\nUsername - `${username}`\nRegions - `${env.regionsList}` \nDeleted by - `${env.BUILD_USER}`"
        break
      
      case 'BASTION_POSTGRES_USER_SUCCESS':
        message = ":pirate_flag: *Postgres User Created Successfully!*\n\nUsername - `${username}`\nValid Till - `${valid_till}`\nRegion - `${params.region}` \nCreated by - `${env.BUILD_USER}`\nTarget Shards - `${hosts}`\nAccess Approved by - `${approved_by}`"
        break
      case 'BASTION_POSTGRES_USER_UPDATE_INFO':
        message = ":gear: *Postgres User Password Updated!*\n\nUsername - `${username}`\nRegion - `${params.region}` \nUpdated by - `${env.BUILD_USER}`\nTarget Shards - `${hosts}`"
        break
      case 'BASTION_POSTGRES_USER_DELETE_INFO':
        message = ":bomb: *Postgres User Deleted!*\n\nUsername - `${username}`\nRegion - `${params.region}` \nDeleted by - `${env.BUILD_USER}`\nTarget Shards - `${hosts}`"
        break
      
      //Multi Region postgres User Notifications
      case 'BASTION_POSTGRES_USER_SUCCESS_MULTI':
        message = ":pirate_flag: *Postgres User Created Successfully!*\n\nUsername - `${username}`\nValid Till - `${valid_till}`\nRegions - `${env.regionsList}` \nCreated by - `${env.BUILD_USER}`\nTarget Shards - `${hosts}`\nAccess Approved by - `${approved_by}`"
        break
      case 'BASTION_POSTGRES_USER_UPDATE_INFO_MULTI':
        message = ":gear: *Postgres User Password Updated!*\n\nUsername - `${username}`\nRegions - `${env.regionsList}` \nUpdated by - `${env.BUILD_USER}`\nTarget Shards - `${hosts}`"
        break
      case 'BASTION_POSTGRES_USER_DELETE_INFO_MULTI':
        message = ":bomb: *Postgres User Deleted!*\n\nUsername - `${username}`\nRegions - `${env.regionsList}` \nDeleted by - `${env.BUILD_USER}`\nTarget Shards - `${hosts}`"
        break

      case 'BASTION_OPENSEARCH_USER_SUCCESS':
        message = ":pirate_flag: *OpenSearch User Created Successfully!*\n\nUsername - `${username}`\nValid Till - `${valid_till}`\nRegions - `${env.regionsList}` \nCreated by - `${env.BUILD_USER}`\nTarget Domains - `${domains}`\nAccess Approved by - `${approved_by}`"
        break
      case 'BASTION_OPENSEARCH_USER_UPDATE_INFO':
        message = ":gear: *OpenSearch User Password Updated!*\n\nUsername - `${username}`\nRegions - `${env.regionsList}` \nUpdated by - `${env.BUILD_USER}`\nTarget Domains - `${domains}`"
        break
      case 'BASTION_OPENSEARCH_USER_DELETE_INFO':
        message = ":bomb: *OpenSearch User Deleted!*\n\nUsername - `${username}`\nRegions - `${env.regionsList}` \nDeleted by - `${env.BUILD_USER}`\nTarget Domains - `${domains}`"
        break

      //Multi Region OpenSearch User Notifications
      case 'BASTION_OPENSEARCH_USER_SUCCESS_MULTI':
        message = ":pirate_flag: *OpenSearch User Created Successfully!*\n\nUsername - `${username}`\nValid Till - `${valid_till}`\nRegions - `${env.regionsList}` \nCreated by - `${env.BUILD_USER}`\nTarget Domains - `${domains}`\nAccess Approved by - `${approved_by}`"
        break
      case 'BASTION_OPENSEARCH_USER_UPDATE_INFO_MULTI':
        message = ":gear: *OpenSearch User Password Updated!*\n\nUsername - `${username}`\nRegions - `${env.regionsList}` \nUpdated by - `${env.BUILD_USER}`\nTarget Domains - `${domains}`"
        break
      case 'BASTION_OPENSEARCH_USER_DELETE_INFO_MULTI':
        message = ":bomb: *OpenSearch User Deleted!*\n\nUsername - `${username}`\nRegions - `${env.regionsList}` \nDeleted by - `${env.BUILD_USER}`\nTarget Domains - `${domains}`"
        break

      case 'BASTION_RABBITMQ_USER_SUCCESS':
        message = ":pirate_flag: *RabbitMQ User Created Successfully!*\n\nUsername - `${username}`\nValid Till - `${valid_till}`\nRegion - `${params.region}` \nCreated by - `${env.BUILD_USER}`\nTarget Shards - `${hosts}`\nAccess Approved by - `${approved_by}`"
        break
      case 'BASTION_RABBITMQ_USER_UPDATE_INFO':
        message = ":gear: *RabbitMq User Password Updated!*\n\nUsername - `${username}`\nRegion - `${params.region}` \nUpdated by - `${env.BUILD_USER}`\nTarget Shards - `${hosts}`"
        break
      case 'BASTION_RABBITMQ_USER_DELETE_INFO':
        message = ":bomb: *RabbitMq User Deleted!*\n\nUsername - `${username}`\nRegion - `${params.region}` \nDeleted by - `${env.BUILD_USER}`\nTarget Shards - `${hosts}`"
        break

      case 'WEBSITE_ORIGIN_REDIRECTION_STARTED':
        message = ":gear: *Origin Redirection initiated!*\n\nStarted by - `${env.BUILD_USER}`\nAction - `${params.Action}`\nPath - `${path}`\nPath Effect: `${patheffect}`\nReplacing Path - `${oldpath}`"
        break
      case 'WEBSITE_ORIGIN_REDIRECTION_SUCCESS':
        message = ":pirate_flag: *Origin Redirection Successful!*\n\nStarted by - `${env.BUILD_USER}`\nAction - `${params.Action}`\nPath - `${path}`\nPath Effect: `${patheffect}`\nReplaced Path - `${oldpath}`"
        break
      case 'WEBSITE_ORIGIN_REDIRECTION_ABORTED':
        message = ":dizzy_face: *Origin Redirection Aborted!*\n\nAborted by - `${env.BUILD_USER}`\nAction - `${params.Action}`\nPath - `${path}`\nPath Effect: `${patheffect}`\nReplacing Path - `${oldpath}`"
        break
      case 'WEBSITE_ORIGIN_REDIRECTION_FAILURE':
        message = ":bomb: *Origin Redirection Failed!*\n\nStarted by - `${env.BUILD_USER}`\n\nAction - `${params.Action}`\nPath - `${path}`\nPath Effect: `${patheffect}`\nReplacing Path - `${oldpath}`\n**Reason:**\n```${command_output}```"
        break
      

      case 'REPL_SCRIPT_STARTED':
        message = "*:rocket: Pipeline started for PR Number: <${params.pullRequestLink}|${data_obj.pr_id}>*\n\n*Region(s):* `${data_obj.region}`\n*Application:* `${data_obj.app}`\n*Execution Type:* `${data_obj.execution_type}`\n*Description* ```${data_obj.ticket_title}\n```\n*Purpose for this execution* ```${params.purpose}```\n*Hooks Option:* `${data_obj.hooks}`\n*Ticket:* <${params.ticketLink}|${data_obj.ticket_id}>\n*Triggered by:* `${env.BUILD_USER}`"
        break
      case 'REPL_SCRIPT_ABORTED':
        message = "*:construction: Pipeline aborted for PR Number: <${params.pullRequestLink}|${data_obj.pr_id}>*\n\n*Region(s):* `${data_obj.region}`\n*Application:* `${data_obj.app}`\n*Execution Type:* `${data_obj.execution_type}`\n*Description* ```${data_obj.ticket_title}\n```\n*Purpose for this execution* ```${params.purpose}```\n*Hooks Option:* `${data_obj.hooks}`\n*Ticket:* <${params.ticketLink}|${data_obj.ticket_id}>\n*Triggered by:* `${env.BUILD_USER}`"
        break
      case 'REPL_SCRIPT_SUCCESS':
        message = "*:pirate_flag: Pipeline completed successfully for PR Number: <${params.pullRequestLink}|${data_obj.pr_id}>*\n\n*Region(s):* `${data_obj.region}`\n*Application:* `${data_obj.app}`\n*Execution Type:* `${data_obj.execution_type}`\n*Description* ```${data_obj.ticket_title}\n```\n*Purpose for this execution* ```${params.purpose}```\n*Hooks Option:* `${data_obj.hooks}`\n*Ticket:* <${params.ticketLink}|${data_obj.ticket_id}>\n*Triggered by:* `${env.BUILD_USER}`"
        break
      case 'REPL_SCRIPT_FAILURE':
        message = "*:bomb: Pipeline failed for PR Number: <${params.pullRequestLink}|${data_obj.pr_id}>*\n\n*Region(s):* `${data_obj.region}`\n*Application:* `${data_obj.app}`\n*Execution Type:* `${data_obj.execution_type}`\n*Description* ```${data_obj.ticket_title}\n```\n*Purpose for this execution* ```${params.purpose}```\n*Hooks Option:* `${data_obj.hooks}`\n*Ticket:* <${params.ticketLink}|${data_obj.ticket_id}>\n*Triggered by:* `${env.BUILD_USER}`\n<${env.BUILD_URL}console|Check logs here>"
        break
      case 'REPL_SCRIPT_RESULT_INFO':
        message = "*Results for `${data_obj.region}`* [`${data_obj.status}`]\nDuration: `${data_obj.duration}` seconds\nExecuted Repl Relative Path: `${data_obj.file}`"
        break
      case 'REPL_SCRIPT_LOGS_INFO':
        message = "*PR-${data_obj.pr_id} `${data_obj.region}`*\n:page_with_curl: Your REPL script's logs are here: <${data_obj.logs}|Script Logs>"
        break
      case 'REPL_SCRIPT_OUTPUT_INFO':
        message = "*PR-${data_obj.pr_id} `${data_obj.region}`*\n:package: Your output is packed and zipped: <${data_obj.output}|Output File Archive>"
        break

      case 'SS_WEBSITE_DEPLOYMENT_STARTED':
        message = "*Deployment Started* :airplane_departure: [#${env.BUILD_NUMBER}] \n\nBranch: `master`\nInitiated By: `${env.BUILD_USER}`"
        break
      case 'SS_WEBSITE_DEPLOYMENT_SUCCESS':
        message = "*Deployed* :sparrow_love: [#${env.BUILD_NUMBER}]\n\nBranch: `master`\nApp Version: `${latestAppVersion}` \nBundle Version: `${FULL_ASSET_ID}`"
        break
      case 'SS_WEBSITE_DEPLOYMENT_FAILURE':
        message = ":boom: *Deployment Failed* [#${env.BUILD_NUMBER}]\n\nBranch: `master`\n<${env.BUILD_URL}pipeline-console|Build Logs>"
        break
      case 'SS_WEBSITE_DEPLOYMENT_ABORTED':
        message = ":construction: *Deployment Aborted*[#${env.BUILD_NUMBER}]\n\nBranch: `master`"
        break
  
      default:
        message = ":boom: *Deployment status unknown!*\n\nDC - `${env.JOB_NAME}`\n Started by - `${env.BUILD_USER}`\n\n<${env.BUILD_URL}console|Check logs here>"
        break
    }

    return message;
  }
}

def getSlackMessage(message_type="", data_obj = [:]) {
  message = ""
  wrap([$class: 'BuildUser']) {
    switch (message_type) {
      case 'DEPLOYMENT_TRIGGERED':
        message = ":anchor: *Deployment triggered!*\n\nRelease Branch - `${params.branchname}`\nStarted by - `${env.BUILD_USER}`"
        break
      case 'TAG_CREATED':
        message = ":canoe: *PR MERGED, Deployment Tag Created.*\n\nRelease Branch - `${params.branchname}`\nDeployment Tag - `${GIT_TAG_FULL}`\nStarted by - `${env.BUILD_USER}`"
        break
      case 'DEPLOYMENT_STARTED':
        message = ":anchor: *Deployment Triggered!*\n\nDC - `${env.JOB_NAME}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\nStarted by - `${env.BUILD_USER}`"
        break
      case 'DEPLOYMENT_STARTED_ALL_DCS':
        message = ":speedboat: *Deployment started in all DCs!*\n\nRelease Branch - `${params.branchname}`\nDeployment Tag - `${GIT_TAG_FULL}`\nStarted by - `${env.BUILD_USER}`"
        break
      case 'DEPLOYMENT_SUCCESSFUL_ALL_DCS':
        message = ":pirate_flag: *Deployment done in all DCs!!*\n\nRelease Branch - `${params.branchname}`\nDeployment Tag - `${GIT_TAG_FULL}`\nStarted by - `${env.BUILD_USER}`"
        break
      case 'BUILD_STARTED':
        message = ":speedboat: *Build started!*\n\nDC - `${env.JOB_NAME}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\nStarted by - `${env.BUILD_USER}`"
        break
      case 'BUILD_FAILED':
        message = ":bomb: *Build failed!*\n\nDC - `${env.JOB_NAME}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\nStarted by - `${env.BUILD_USER}`\n\n<${env.BUILD_URL}console|Check logs here>"
        break
      case 'WEBPACK_BUILD_FAILED':
        message = ":bomb: *Webpack build failed!*\n\nDC - `${env.JOB_NAME}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\nStarted by - `${env.BUILD_USER}`\n\n<${env.BUILD_URL}console|Check logs here>"
        break
      case 'PRE_MIGRATION_STARTED':
        message = ":gear: *Pre migration started!*\n\nDC - `${env.JOB_NAME}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\nStarted by - `${env.BUILD_USER}`\n\nAwaiting approval to run pre migrations. <${env.BUILD_URL}input|Click here to approve>"
        break
      case 'PRE_MIGRATION_SUCCESSFUL':
        message = ":motor_boat: *Pre migration successful!*\n\nDC - `${env.JOB_NAME}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\nStarted by - `${env.BUILD_USER}`\n\n<${migrationLogUrl}|Click here to check migration logs> (url valid for 2 hours)"
        break
      case 'PRE_MIGRATION_FAILED':
        message = ":bomb: *Pre migration failed!*\n\nDC - `${env.JOB_NAME}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\nStarted by - `${env.BUILD_USER}`\n\n<${env.BUILD_URL}console|Check build logs here>\n\n<${migrationLogUrl}|Click here to check migration logs> (url valid for 2 hours)"
        break
      case 'DOCKER_IMAGE_BUILD_FAILED':
        message = ":bomb: *Docker build failed!*\n\nDC - `${env.JOB_NAME}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\nStarted by - `${env.BUILD_USER}`\n\n<${env.BUILD_URL}console|Check logs here>"
        break
      case 'DEPLOYMENT_TO_GREEN_STARTED':
        message = ":ship: *Deployment started!*\n\nDC - `${env.JOB_NAME}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\nStarted by - `${env.BUILD_USER}`\n\nAwaiting approval to proceed with the deployment. <${env.BUILD_URL}input|Click here to approve>"
        break
      case 'DEPLOYMENT_TO_BLUE_STARTED':
        message = ":ship: *Deployment started!*\n\nDC - `${env.JOB_NAME}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\nStarted by - `${env.BUILD_USER}`"
        break
      case 'DEPLOYMENT_TO_BLUE_FAILED':
        message = ":bomb: *Deployment failed!*\n\nDC - `${env.JOB_NAME}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\nStarted by - `${env.BUILD_USER}`\n\n<${env.BUILD_URL}console|Check logs here>"
        break
      case 'SWITCH_TO_BLUE_GREEN':
        message = ":repeat: Deployment done, Approve to Route Internal Accounts Traffic to new deployment.\n\nDC - `${env.JOB_NAME}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\n\nAwaiting approval for routing internal traffic. <${env.BUILD_URL}input|Click here to approve>"
        break
      case 'SWITCH_TO_BLUE_GREEN_FAILED':
        message = ":bomb: *Deployment failed!*\n\nDC - `${env.JOB_NAME}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\n\n<${env.BUILD_URL}console|Check logs here>"
        break
      case 'SWITCH_TO_GREEN':
        message = ":repeat: Once the Sanity is done, Approve to Route All Traffic to new deployment.\n\nDC - `${env.JOB_NAME}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\n\nAwaiting approval for routing all traffic. <${env.BUILD_URL}input|Click here to approve>"
        break
      case 'SWITCH_TO_GREEN_FAILED':
        message = ":bomb: *Deployment failed!*\n\nDC - `${env.JOB_NAME}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\n\n<${env.BUILD_URL}console|Check logs here>"
        break
      case 'POST_MIGRATION_STARTED':
        message = ":gear: *Post migration started!*\n\nDC - `${env.JOB_NAME}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\nStarted by - `${env.BUILD_USER}`\n\nAwaiting approval to run post migrations. <${env.BUILD_URL}input|Click here to approve>"
        break
      case 'POST_MIGRATION_SUCCESSFUL':
        message = ":motor_boat: *Post migration successful!*\n\nDC - `${env.JOB_NAME}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\nStarted by - `${env.BUILD_USER}`\n\n<${migrationLogUrl}|Click here to check migration logs> (url valid for 2 hours)"
        break
      case 'POST_MIGRATION_FAILED':
        message = ":bomb: *Post migration failed!*\n\nDC - `${env.JOB_NAME}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\nStarted by - `${env.BUILD_USER}`\n\n<${env.BUILD_URL}console|Check build logs here>\n\n<|${migrationLogUrl}|Click here to check migration logs> (url valid for 2 hours)"
        break
      case 'DEPLOYMENT_SUCCESSFUL':
        message = ":pirate_flag: *Deployment successful!!*\n\nDC - `${env.JOB_NAME}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\nStarted by - `${env.BUILD_USER}`"
        break
      case 'DEPLOYMENT_FAILED':
        message = ":bomb: *Deployment failed!*\n\nDC - `${env.JOB_NAME}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\nStarted by - `${env.BUILD_USER}`\n\n<${env.BUILD_URL}console|Check logs here>"
        break
      case 'DEPLOYMENT_ABORTED':
        message = ":dizzy_face: *Deployment aborted!*\n\nDC - `${env.JOB_NAME}`\nRelease Branch - `${params.releasebranch}`\nDeployment Tag - `${params.deploymenttag}`\nStarted by - `${env.BUILD_USER}`"
        break
      case 'WORKER_CONFIG_UPDATE_STARTED':
        message = ":gear: *Worker config update started!*\n\nDC - `${params.region} - ${params.stack}`\nStarted by - `${env.BUILD_USER}`\nReason - `${params.reason}`"
        break
      case 'WORKER_CONFIG_UPDATE_SUCCESSFUL':
        message = ":gear: *Worker config update successful!*\n\nDC - `${params.region} - ${params.stack}`\nStarted by - `${env.BUILD_USER}`\nReason - `${params.reason}`"
        break
      case 'WORKER_CONFIG_UPDATE_FAILED':
        message = ":gear: *Worker config update failed!*\n\nDC - `${params.region} - ${params.stack}`\nStarted by - `${env.BUILD_USER}`\nReason - `${params.reason}`"
        break
      case 'WORKER_CONFIG_UPDATE_ABORTED':
        message = ":gear: *Worker config update aborted!*\n\nDC - `${params.region} - ${params.stack}`\nStarted by - `${env.BUILD_USER}`\nReason - `${params.reason}`"
        break
      case 'APP_RESTART_STARTED':
        message = ":arrows_counterclockwise: *Application restart started!*\n\nDC - `${params.region} - ${params.stack}`\nLayer - `${params.layer}`\nStarted by - `${env.BUILD_USER}`\nReason - `${params.reason}`"
        break
      case 'APP_RESTART_SUCCESSFUL':
        message = ":pirate_flag: *Application restarted successfully!*\n\nDC - `${params.region}  - ${params.stack}`\nLayer - `${params.layer}`\nStarted by - `${env.BUILD_USER}`\nReason - `${params.reason}`"
        break
      case 'APP_RESTART_FAILED':
        message = ":bomb: *Application restart failed!*\n\nDC - `${params.region}  - ${params.stack}`\nLayer - `${params.layer}`\nStarted by - `${env.BUILD_USER}`\nReason - `${params.reason}`\n\n<${env.BUILD_URL}console|Check logs here>"
        break
      case 'APP_RESTART_ABORTED':
        message = ":dizzy_face: *Application restart aborted!*\n\nDC - `${params.region}  - ${params.stack}`\nLayer - `${params.layer}`\nStarted by - `${env.BUILD_USER}`\nReason - `${params.reason}`"
        break
      case 'CONFIG_UPDATE_STARTED':
        message = ":gear: *Config Update started!*\n\nUpdating Config - `${params.config}`\nStarted by - `${env.BUILD_USER}`\nEnvironment - `${params.region} - ${params.stack}`\nReason - `${params.reason}`"
        break
      case 'CONFIG_UPDATE_SUCCESSFUL':
        message = ":pirate_flag: *Config Update successful!!*\n\nUpdated Config - `${params.config}`\nStarted by - `${env.BUILD_USER}`\nEnvironment - `${params.region} - ${params.stack}`\nReason - `${params.reason}`"
        break
      case 'CONFIG_UPDATE_FAILED':
        message = ":bomb: *Config Update failed!*\n\nUpdating Config - `${params.config}`\nStarted by - `${env.BUILD_USER}`\nEnvironment - `${params.region} - ${params.stack}`\nReason - `${params.reason}`\n<${env.BUILD_URL}console|Check logs here>"
        break
      case 'CONFIG_UPDATE_ABORTED':
        message = ":dizzy_face: *Config Update aborted!*\n\nUpdating Config - `${params.config}`\nStarted by - `${env.BUILD_USER}`\nEnvironment - `${params.region} - ${params.stack}`\nReason - `${params.reason}`"
        break
      case 'SCALING_CONFIG_UPDATE_STARTED':
        message = ":gear: *Scaling Config Update started!*\n\nEnvironment - `${params.region}`\nLayer - `${params.layer}`\nConfig - `Min: ${params.minreplicas}, Max: ${params.maxreplicas}`\nStarted by - `${env.BUILD_USER}`\nReason - `${params.reason}`"
        break
      case 'SCALING_CONFIG_UPDATE_SUCCESSFUL':
        message = ":pirate_flag: *Scaling Config Update successful!!*\n\nEnvironment - `${params.region}`\nLayer - `${params.layer}`\nConfig - `Min: ${params.minreplicas}, Max: ${params.maxreplicas}`\nStarted by - `${env.BUILD_USER}`\nReason - `${params.reason}`"
        break
      case 'SCALING_CONFIG_UPDATE_FAILED':
        message = ":bomb: *Scaling Config Update failed!*\n\nEnvironment - `${params.region}`\nLayer - `${params.layer}`\nConfig - `Min: ${params.minreplicas}, Max: ${params.maxreplicas}`\nStarted by - `${env.BUILD_USER}`\nReason - `${params.reason}`\n<${env.BUILD_URL}console|Check logs here>"
        break
      case 'SCALING_CONFIG_UPDATE_ABORTED':
        message = ":dizzy_face: *Scaling Config Update aborted!*\n\nEnvironment - `${params.region}`\nLayer - `${params.layer}`\nConfig - `Min: ${params.minreplicas}, Max: ${params.maxreplicas}`\nStarted by - `${env.BUILD_USER}`\nReason - `${params.reason}`"
        break
      case 'PAUSE_QUEUES_STARTED':
        message = ":gear: *Pause Queues started!*\n\nEnvironment - `${params.region}`\nStarted by - `${env.BUILD_USER}`\nReason - `${params.reason}`"
        break
      case 'PAUSE_QUEUES_SUCCESSFUL':
        message = ":pirate_flag: *Pause Queues successful!!*\n\nEnvironment - `${params.region}`\nStarted by - `${env.BUILD_USER}`\nReason - `${params.reason}`\n\nPlease review the PR and perform a config update."
        break
      case 'PAUSE_QUEUES_FAILED':
        message = ":bomb: *Pause Queues failed!*\n\nEnvironment - `${params.region}`\nStarted by - `${env.BUILD_USER}`\nReason - `${params.reason}`\n<${env.BUILD_URL}console|Check logs here>"
        break
      case 'PAUSE_QUEUES_ABORTED':
        message = ":dizzy_face: *Pause Queues aborted!*\n\nEnvironment - `${params.region}`\nStarted by - `${env.BUILD_USER}`\nReason - `${params.reason}`"
        break
      
      // Bullboard Messages
      case 'BULLBOARD_DEPLOYMENT_STARTED':
        message = ":rocket: *BullBoard Deployment started!*\n\nStarted by - `${env.BUILD_USER}`\nDeploying Branch - `${params.branchname}`\nRegion - `${params.region}`"
        break
      case 'BULLBOARD_DEPLOYMENT_FAILED':
        message = ":bomb: *BullBoard Deployment failed!*\n\nStarted by - `${env.BUILD_USER}`\nDeploying Branch - `${params.branchname}`\nRegion - `${params.region}`\n<${env.BUILD_URL}console|Check logs here>"
        break
      case 'BULLBOARD_DEPLOYMENT_SUCCESSFUL':
        message = ":pirate_flag: *BullBoard Deployment successful!!*\n\nStarted by - `${env.BUILD_USER}`\nDeploying Branch - `${params.branchname}`\nRegion - `${params.region}`"
        break
      case 'BULLBOARD_DEPLOYMENT_ABORTED':
        message = ":dizzy_face: *BullBoard Deployment aborted!*\n\nStarted by - `${env.BUILD_USER}`\nDeploying Branch - `${params.branchname}`\nRegion - `${params.region}`"
        break

      // Bullboard Config Update Messages
      case 'BULLBOARD_CONFIGUPDATE_STARTED':
          message = ":rocket: *BullBoard Config Update started!*\n\nStarted by - `${env.BUILD_USER}`\nRegion - `${params.region}"
          break
      case 'BULLBOARD_CONFIGUPDATE_FAILED':
          message = ":bomb: *BullBoard Config Update failed!*\n\nStarted by - `${env.BUILD_USER}`\nRegion - `${params.region}\n<${env.BUILD_URL}console|Check logs here>"
          break
      case 'BULLBOARD_CONFIGUPDATE_SUCCESSFUL':
          message = ":pirate_flag: *BullBoard Config Update successful!*\n\nStarted by - `${env.BUILD_USER}`\nRegion - `${params.region}"
          break
      case 'BULLBOARD_CONFIGUPDATE_ABORTED':
          message = ":dizzy_face: *BullBoard Config Update pipeline aborted!*\n\nStarted by - `${env.BUILD_USER}`\nRegion - `${params.region}"
          break
      case 'BULLBOARD_RESTART_STARTED':
          message = ":rocket: *BullBoard Restart started!*\n\nStarted by - `${env.BUILD_USER}`\nRegion - `${params.region}"
          break
      case 'BULLBOARD_RESTART_SUCCESSFUL':
          message = ":pirate_flag: *BullBoard Restart successful!*\n\nStarted by - `${env.BUILD_USER}`\nRegion - `${params.region}"
          break
      case 'BULLBOARD_RESTART_FAILED':
          message = ":dizzy_face: *BullBoard Restart Failed!*\n\nStarted by - `${env.BUILD_USER}`\nRegion - `${params.region}\n<${env.BUILD_URL}console|Check logs here>"
          break
      case 'BULLBOARD_RESTART_ABORTED':
          message = ":dizzy_face: *BullBoard Restart aborted!*\n\nStarted by - `${env.BUILD_USER}`\nRegion - `${params.region}"
          break
      
      // Wrangler Deployment Messages
      case 'CLOUDFLARE_DEPLOYMENT_STARTED':
        message = ":tada: *Wrangler Deployment Started!*\n\nApplication - *${params.application}*\n\nStarted by - *${env.BUILD_USER}*\nDescription - *${params.description}*"
        break
      case 'CLOUDFLARE_DEPLOYMENT_SUCCESSFUL':
        message = ":star-struck: *Wrangler Deployment Successful!*\n\nApplication - *${params.application}*\n\nStarted by - *${env.BUILD_USER}*\nDescription - *${params.description}*"
        break
      case 'CLOUDFLARE_DEPLOYMENT_FAILED':
        message = ":bomb: *Wrangler Deployment Failed!*\n\nApplication - *${params.application}*\n\nStarted by - *${env.BUILD_USER}*\nDescription - *${params.description}*\n<${env.BUILD_URL}console|Check logs here>"
        break
      case 'CLOUDFLARE_DEPLOYMENT_ABORTED':
        message = "::star-struck:: *Wrangler Deployment Aborted!*\n\nApplication - *${params.application}*\n\nStarted by - *${env.BUILD_USER}*\nDescription - *${params.description}*"
        break

      case 'CLOUDFLARE_CACHE_PURGE_STARTED':
        message = ":tada: *Cloudflare Cache Purge Started!*\n\nPurge Type: `${params.cache_type}`\nPATHS: `${paths}`\nDomains:`${params.domain}`\n\nStarted by - `${env.BUILD_USER}`\nDescription - *${job_description}*"
        break
      case 'CLOUDFLARE_CACHE_PURGE_SUCCESS':
        message = ":star-struck: *Cloudflare Cache Purge Successful!*\n\nPurge Type: `${params.cache_type}`\nPATHS: `${paths}`\nDomains:`${params.domain}`\n\nStarted by - `${env.BUILD_USER}`\nDescription - *${job_description}*"
        break
      case 'CLOUDFLARE_CACHE_PURGE_FAILURE':
        message = ":bomb: *Cloudflare Cache Purge Failed!*\n\nPurge Type: `${params.cache_type}`\nPATHS: `${paths}`\nDomains:`${params.domain}`\n\nStarted by - `${env.BUILD_USER}`\nDescription - *${job_description}*\n<${env.BUILD_URL}console|Check logs here>"
        break
      case 'CLOUDFLARE_CACHE_PURGE_ABORTED':
        message = ":star-struck: *Cloudflare Cache Purge Aborted!*\n\nPurge Type: `${params.cache_type}`\nPATHS: `${paths}`\nDomains:`${params.domain}`\n\nStarted by - `${env.BUILD_USER}`\nDescription - *${job_description}*"
        break

      // WEBSITE ROLLBACK
      case 'WEBSITE_ROLLBACK_STARTED':
        message = ":tada: *Website Rollback Started!*\n\nStarted by - `${env.BUILD_USER}`\nApplication - *${params.application}* \nWaiting for approval to proceed with the deployment. <${env.BUILD_URL}input|Click here to open build URL>"
        break
      case 'WEBSITE_ROLLBACK_APPROVED_INFO':
        message = ":star-struck: *Website Rollback Approved!*\n\nApproved by - `${approver}`\nApplication - *${params.application}*"
        break
      case 'WEBSITE_ROLLBACK_SUCCESS':
        message = ":star-struck: *Website Rollback Successful!*\n\nStarted by - `${env.BUILD_USER}`\nApplication - *${params.application}*\nApproved by - `${approver}`\nVersion - *${downgrade_version} (downgraded)*"
        break
      case 'WEBSITE_ROLLBACK_FAILURE':
        message = ":bomb: *Website Rollback Failed!*\n\nStarted by - `${env.BUILD_USER}`\nApplication - *${params.application}*\n<${env.BUILD_URL}console|Check logs here>"
        break
      case 'WEBSITE_ROLLBACK_ABORTED':
        message = ":star-struck: *Website Rollback Aborted!*\n\nStarted by - `${env.BUILD_USER}`\nApplication - *${params.application}*"
        break

      // All Deployment Service Notifications
      case "SPARROWPAY_DEPLOYMENT_STARTED":
          message = ":airplane_departure: :rocket: *SPARROWPAY DEPLOYMENT PRODUCTION* \n\nService: *`${service}`* \nDeployment of Branch Name: `production` \nStarted By: `${env.BUILD_USER}` \nhas *STARTED* !! \n<${env.BUILD_URL}|Click here for more details>"
          break

      case "SPARROWPAY_DEPLOYMENT_SUCCESS":
          message = ":airplane_arriving: :small_airplane: *SPARROWPAY DEPLOYMENT PRODUCTION* \n\nService: *`${service}`* \nDeployment of Branch Name:  `production` \nStarted By: `${env.BUILD_USER}` \n!!! is `Successfully Completed`"
          break

      case "SPARROWPAY_ARGO_SYNC":
          message = "Syncing latest Changes has started Service: *`${service}`* \nDeployment of Branch Name: `production`"
          break
      
      case "SPARROWPAY_DEPLOYMENT_FAILURE":
          message = ":red_circle: *SPARROWPAY DEPLOYMENT PRODUCTION* \n\nService: *`${service}`* \nDeployment of Branch Name:  `production` \nStarted By: `${env.BUILD_USER}` \n has `Failed` !!! \n<${env.BUILD_URL}|Click here for more details>"
          break

      case "SPARROWPAY_DEPLOYMENT_ABORTED":
          message = ":construction: *SPARROWPAY DEPLOYMENT PRODUCTION* \n\nService: *`${service}`* \nDeployment of Branch Name: `production` \nStarted By: `${env.BUILD_USER}` \nhas been `ABORTED` \n<${env.BUILD_URL}|Click here for more details>"
          break
            

      // All Update Config Service Notifications
      case "SPARROWPAY_UPDATECONFIG_STARTED":
          message = ":repeat: *SPARROWPAY UPDATE CONFIG PRODUCTION* \n\nService: *`${params.service}`* \nStarted By: `${env.BUILD_USER}`"
          break

      case "SPARROWPAY_UPDATECONFIG_SUCCESS":
          message = ":fireworks: *SPARROWPAY UPDATE CONFIG PRODUCTION* \n\nService: *`${params.service}`* \nStarted By: `${env.BUILD_USER}` \n!!! is `Successfully Completed`"
          break

      case "SPARROWPAY_UPDATECONFIG_FAILED":
          message = ":octagonal_sign: *SPARROWPAY UPDATE CONFIG PRODUCTION* \n\nService: *`${params.service}`* \nStarted By: `${env.BUILD_USER}` \n!!! is `has FAILED` \n<${env.BUILD_URL}|Click here for more details>"
          break
      
      case "SPARROWPAY_UPDATECONFIG_NO_CHANGE":
          message = ":man-shrugging: *SPARROWPAY UPDATE CONFIG PRODUCTION* \n\nService: *`${params.service}`* \nStarted By: `${env.BUILD_USER}` \n!!! is `UNCHANGED` \n<${env.BUILD_URL}|Click here for more details>"
          break

      case "SPARROWPAY_UPDATECONFIG_ABORTED":
          message = ":anchor: *SPARROWPAY UPDATE CONFIG PRODUCTION* \n\nService: *`${params.service}`* \nStarted By: `${env.BUILD_USER}` \n!!! is `has been Aborted` \n<${env.BUILD_URL}|Click here for more details>"
          break
            
      // All Restart Service Notifications
      case "SPARROWPAY_SERVICE_RESTART_STARTED":
          message = ":arrows_counterclockwise: *SPARROWPAY SERVICE RESTART PRODUCTION* \n\nService: *`${params.service}`* \nStarted By: `${env.BUILD_USER}` \n!!! is `has STARTED`"
          break
      
      case "SPARROWPAY_SERVICE_RESTART_SUCCESS":
          message = ":sparkler: *SPARROWPAY SERVICE RESTART PRODUCTION* \n\nService: *`${params.service}`* \nStarted By: `${env.BUILD_USER}` \n!!! is `Successfully Completed`"
          break
      
      case "SPARROWPAY_SERVICE_RESTART_FAILED":
          message = ":no_entry_sign: *SPARROWPAY SERVICE RESTART PRODUCTION* \n\nService: *`${params.service}`* \nStarted By: `${env.BUILD_USER}` \n!!! has `FAILED` \n<${env.BUILD_URL}|Click here for more details>"
          break

      case "SPARROWPAY_SERVICE_RESTART_ABORTED":
          message = ":small_airplane: *SPARROWPAY SERVICE RESTART PRODUCTION* \n\nService: *`${params.service}`* \nStarted By: `${env.BUILD_USER}` \n!!! is `has been Aborted` \n<${env.BUILD_URL}|Click here for more details>"
          break

      // All Restart Bull Dashboards
      case "SPARROWPAY_BULL_DASHBOARD_STARTED":
          message = ":arrows_counterclockwise: *ETL SPARROWPAY DASHBOARD PRODUCTION* \n\nStarted By: `${env.BUILD_USER}` \n!!! is `has STARTED`"
          break
      
      case "SPARROWPAY_BULL_DASHBOARD_SUCCESS":
          message = ":sparkler: *ETL SPARROWPAY DASHBOARD PRODUCTION* \n\nStarted By: `${env.BUILD_USER}` \n!!! is `Successfully Completed`"
          break
      
      case "SPARROWPAY_BULL_DASHBOARD_FAILED":
          message = ":no_entry_sign: *ETL SPARROWPAY DASHBOARD PRODUCTION* \n\nStarted By: `${env.BUILD_USER}` \n!!! has `FAILED` \n<${env.BUILD_URL}|Click here for more details>"
          break

      case "SPARROWPAY_BULL_DASHBOARD_ABORTED":
          message = ":small_airplane: *ETL SPARROWPAY DASHBOARD PRODUCTION* \n\nStarted By: `${env.BUILD_USER}` \n!!! is `has been Aborted` \n<${env.BUILD_URL}|Click here for more details>"
          break
        
      // All Marketplace SDK Website Deployment
      case 'MARKETPLACE_SDK_DEPLOYMENT_STARTED':
          message = ":airplane_departure: *APPNEST SDK DOC DEPLOYMENT* \n\n\nRegion: *`${env.AWS_DEFAULT_REGION}`* \nDeployment of Branch Name: `${params.branchName}` \nStarted By: `${env.BUILD_USER}` \nhas *STARTED* !!"
          break
      case 'MARKETPLACE_SDK_DEPLOYMENT_SUCCESS':
          message = ":airplane_arriving: *APPNEST SDK DOC DEPLOYMENT* \n\n\nRegion: *`${env.AWS_DEFAULT_REGION}`* \nDeployment of Branch Name:  `${params.branchName}` \nStarted By: `${env.BUILD_USER}` \n!!! is `Successfully Completed`"
          break
      case 'MARKETPLACE_SDK_DEPLOYMENT_FAILURE':
          message = ":red_circle: *APPNEST SDK DOC DEPLOYMENT* \n\n\nRegion: *`${env.AWS_DEFAULT_REGION}`* \nDeployment of Branch Name:  `${params.branchName}` \nStarted By: `${env.BUILD_USER}` \n has `Failed` !!! \n\n<${env.BUILD_URL}|Click here for more details>"
          break
      case 'MARKETPLACE_SDK_DEPLOYMENT_ABORTED':
          message = ":construction: *APPNEST SDK DOC DEPLOYMENT* \n\n\nRegion: *`${env.AWS_DEFAULT_REGION}`* \nDeployment of Branch Name: `${params.branchName}` \nStarted By: `${env.BUILD_USER}` \nhas been `ABORTED` \n\n<${env.BUILD_URL}|Click here for more details>"
          break
      case 'MARKETPLACE_SDK_DEPLOYMENT_COMPLETED':
          message = ":mountain_cabelway: *APPNEST SDK DOC DEPLOYMENT* \n\n\nRegion: *`${env.AWS_DEFAULT_REGION}`* \nDeployment of Branch Name:  `${params.branchName}` \nStarted By: `${env.BUILD_USER}` \n!!! is `DEPLOYMENT Completed` \n\n<${env.BUILD_URL}|Click here for more details>"
          break
      case 'MARKETPLACE_SDK_CACHE_CLEAR_STARTED':
          message = ":dash: *APPNEST SDK DOC CLEARING CACHE* \n\n\nRegion: *`${env.AWS_DEFAULT_REGION}`* \nStarted By: `${env.BUILD_USER}`\nClearing Cache has Started !!!"
          break
      case 'MARKETPLACE_SDK_CACHE_CLEAR_COMPLETED':
          message = ":leaves: *APPNEST SDK DOC CLEARING CACHE* \n\n\nRegion: *`${env.AWS_DEFAULT_REGION}`* \nStarted By: `${env.BUILD_USER}` Clearing Cache has Been Completed !!! \n\n<${env.BUILD_URL}|Click here for more details>"
          break

      // Marketplace Developer Portal Notifictions
      case 'MARKETPLACE_DEV_PORTAL_DEPLOYMENT_STARTED':
        message = ":airplane_departure: *APPNEST PRODUCTION DEVELOPER PORTAL DEPLOYMENT* \n\n\nRegion: *`${env.AWS_DEFAULT_REGION}`* \nDeployment of Branch Name: `${params.branchName}` \nStarted By: `${env.BUILD_USER}` \nhas *STARTED* !! \n\n<${env.BUILD_URL}|Click here for more details>"
        break

      case 'MARKETPLACE_DEV_PORTAL_DEPLOYMENT_SUCCESS':
          message = ":airplane_arriving: *APPNEST PRODUCTION DEVELOPER PORTAL DEPLOYMENT* \n\n\nRegion: *`${env.AWS_DEFAULT_REGION}`* \nDeployment of Branch Name:  `${params.branchName}` \nStarted By: `${env.BUILD_USER}` \n!!! is `Successfully Completed` \n\n<${env.BUILD_URL}|Click here for more details>"
          break

      case 'MARKETPLACE_DEV_PORTAL_DEPLOYMENT_FAILURE':
          message = ":red_circle: *APPNEST PRODUCTION DEVELOPER PORTAL DEPLOYMENT* \n\n\nRegion: *`${env.AWS_DEFAULT_REGION}`* \nDeployment of Branch Name:  `${params.branchName}` \nStarted By: `${env.BUILD_USER}` \n has `Failed` !!! \n\n<${env.BUILD_URL}|Click here for more details>"
          break

      case 'MARKETPLACE_DEV_PORTAL_DEPLOYMENT_ABORTED':
          message = ":construction: *APPNEST PRODUCTION DEVELOPER PORTAL DEPLOYMENT* \n\n\nRegion: *`${env.AWS_DEFAULT_REGION}`* \nDeployment of Branch Name: `${params.branchName}` \nStarted By: `${env.BUILD_USER}` \nhas been `ABORTED` \n\n<${env.BUILD_URL}|Click here for more details>"
          break
      
      // Marketplace Admin Portal Notifications
      case 'MARKETPLACE_ADMIN_PORTAL_DEPLOYMENT_STARTED':
          message = ":airplane_departure: *APPNEST PRODUCTION ADMIN PORTAL DEPLOYMENT* \n\n\n\nRegion: *`${env.AWS_DEFAULT_REGION}`* \nDeployment of Branch Name: `${params.branchName}` \nStarted By: `${env.BUILD_USER}` \nhas *STARTED* !! \n\n<${env.BUILD_URL}|Click here for more details>"
          break

      case 'MARKETPLACE_ADMIN_PORTAL_DEPLOYMENT_SUCCESS':
          message = ":airplane_arriving: *APPNEST PRODUCTION ADMIN PORTAL DEPLOYMENT* \n\n\n\nRegion: *`${env.AWS_DEFAULT_REGION}`* \nDeployment of Branch Name:  `${params.branchName}` \nStarted By: `${env.BUILD_USER}` \n!!! is `Successfully Completed` \n\n<${env.BUILD_URL}|Click here for more details>"
          break

      case 'MARKETPLACE_ADMIN_PORTAL_DEPLOYMENT_FAILURE':
          message = ":red_circle: *APPNEST PRODUCTION ADMIN PORTAL DEPLOYMENT* \n\n\n\nRegion: *`${env.AWS_DEFAULT_REGION}`* \nDeployment of Branch Name:  `${params.branchName}` \nStarted By: `${env.BUILD_USER}` \n has `Failed` !!! \n\n<${env.BUILD_URL}|Click here for more details>"
          break

      case 'MARKETPLACE_ADMIN_PORTAL_DEPLOYMENT_ABORTED':
          message = ":construction: *APPNEST PRODUCTION ADMIN PORTAL DEPLOYMENT* \n\n\n\nRegion: *`${env.AWS_DEFAULT_REGION}`* \nDeployment of Branch Name: `${params.branchName}` \nStarted By: `${env.BUILD_USER}` \nhas been `ABORTED` \n\n<${env.BUILD_URL}|Click here for more details>"
          break
     
      default:
        message = getSlackMessageF2(message_type, data_obj)
        break

    }

    return message;
  }
}

def constructOpsRelatedMessage(status) {
  if (notificationParams.pipeline == null) {
    notificationParams.pipeline = "${env.JOB_NAME.split('/').last()}"
  }

  def message = variables.STATUS_EMOJI_MAP[status] + " " + notificationParams.pipeline + " " + status.toLowerCase() + "\n"
  notificationParams.each {
    if (it.key != 'pipeline') {
      message += it.key + " - `" + it.value + "`\n"
    }
  }

  return message
}

def sendOpsRelatedNotifcation(status) {
  def message = constructOpsRelatedMessage(status)
  def color   = variables.STATUS_COLOR_MAP[status]

  if (notificationParams.region != null) {
    color = variables.COLOR_REGION_MAP[notificationParams.region]
  }

  sendMessage(color, message, variables.DEFAULT_NOTIFICATION_CHANNEL)
}

def sendOpsRelatedNotifcationToChannel(status, channel) {
  def message = constructOpsRelatedMessage(status)
  def color   = variables.STATUS_COLOR_MAP[status]

  if (notificationParams.region != null) {
    color = variables.COLOR_REGION_MAP[notificationParams.region]
  }

  sendMessage(color, message, channel)
}

def sendMessage(color, message, channel) {
  slackSend (
    color: color,
    message: message,
    teamDomain: 'surveysparrow',
    channel: channel,
    tokenCredentialId: variables.DEFAULT_NOTIFICATION_TOKEN_ID
  )
}

def getDeploymentProgressSlackMessage(message_type) {
  dc = variables.REGION_DC_NAME_MAP[env.JOB_NAME]
  if (dc == null) {
    return dc
  }

  switch (message_type) {
    case 'DEPLOYMENT_STARTED':
      progress_message = "Deployment started in ${dc} DC"
      break
    case 'DEPLOYMENT_SUCCESSFUL':
      progress_message = "Deployment successful in ${dc} DC"
      break
    case 'DEPLOYMENT_FAILED':
      progress_message = "Deployment failed in ${dc} DC"
      break
    case 'DEPLOYMENT_ABORTED':
      progress_message = "Deployment aborted in ${dc} DC"
      break
    default:
      progress_message = "Deployment in progress in ${dc} DC"
      break
  }

  return progress_message
}

def sendBotMessage(message_type, channel, data_obj = [:]) {
  message = getSlackMessage(message_type, data_obj)
  status  = message_type.split('_').last()
  color   = variables.STATUS_COLOR_MAP[status]

  def message_res = slackSend (
    color: color,
    message: message,
    teamDomain: 'surveysparrow', 
    channel: channel,
    tokenCredentialId: 'SlackBotToken',
    botUser: true
  )

  return message_res
}

def sendReplJiraComment(pr_id, ticket_id, region, state, duration, output, replfile, logs) {
   wrap([$class: 'BuildUser']) {
    json_data = sh (script: "REGION=\"${region}\" PR_ID=${pr_id} STATE=${state} DURATION=\"${duration}\" CODE_PATH=\"${replfile}\" LOGS_URL=\"${logs}\" OUTPUT_URL=\"${output}\" USER=\"${env.BUILD_USER}\" JOB_ID=${env.BUILD_NUMBER} envsubst < Helpers/repljiracomment.json", returnStdout: true).trim()
   }
  sh """curl --location 'https://surveysparrow.atlassian.net/rest/api/3/issue/${ticket_id}/comment' \
--header 'Authorization: Basic ${jiraCredentials}' \
--header 'Accept: application/json' \
--header 'Content-Type: application/json' \
--data '${json_data}'"""
}

def sendAttachmentToJira(ticket_id, attachment) {
  sh """curl --location --request POST 'https://surveysparrow.atlassian.net/rest/api/3/issue/${ticket_id}/attachments' \
      -H 'Authorization: Basic ${jiraCredentials}' \
      -H 'X-Atlassian-Token: no-check' \
      --form 'file=@"${attachment}"'"""
}

def getSOCDeploymentProgressSlackMessage(message_type) {
  dc = regionName
  application = env.JOB_NAME
  if (dc == null) {
    return dc
  }
  switch (message_type) {
    case 'DEPLOYMENT_STARTED':
      progress_message = "${application} Deployment started in ${dc} DC"
      break
    case 'DEPLOYMENT_SUCCESSFUL':
      progress_message = "${application} Deployment successful in ${dc} DC"
      break
    case 'DEPLOYMENT_FAILED':
      progress_message = "${application} Deployment failed in ${dc} DC"
      break
    case 'DEPLOYMENT_ABORTED':
      progress_message = "${application} Deployment aborted in ${dc} DC"
      break
    default:
      progress_message = "${application} Deployment in progress in ${dc} DC"
      break
  }
  return progress_message
}

def sendSOCDeploymentProgressNotification(percentage, etc, message_timestamp = null, message_type = null) {
  progress_message  = getSOCDeploymentProgressSlackMessage(message_type)
  msg_timestamp = sh (script: "SLACK_TOKEN=${env.SLACK_TOKEN} python3 Helpers/slackprogressnotification.py -m '${progress_message}' -p ${percentage} -s '${env.STAGE_NAME}' -t ${message_timestamp} -c ${variables.PRODUCTION_DEPLOYMENTS_CHANNEL_ID} -e ${etc} | tr -d '\n'", returnStdout: true)
  return msg_timestamp
}

def sendDeploymentProgressNotification(percentage, etc, message_timestamp = null, message_type = null) {
  progress_message  = getDeploymentProgressSlackMessage(message_type)
  msg_timestamp = sh (script: "SLACK_TOKEN=${env.SLACK_TOKEN} python3 Helpers/slackprogressnotification.py -m '${progress_message}' -p ${percentage} -s '${env.STAGE_NAME}' -t ${message_timestamp} -c ${variables.PRODUCTION_DEPLOYMENTS_CHANNEL_ID} -e ${etc} | tr -d '\n'", returnStdout: true)
  return msg_timestamp
}

def sendDeploymentNotification(message_type) {
  message = getSlackMessage(message_type)
  color = color_region_map[env.JOB_NAME]
  release_branch = params.releasebranch == null ? params.branchname : params.releasebranch
  release_channel = release_branch.replaceAll('/', '-')

  slackSend (
    color: "${color}",
    message: "${message}",
    teamDomain: 'surveysparrow',
    channel: release_channel,
    tokenCredentialId: 'SlackIntegrationToken'
  )
}

def sendSlackNotification(message_type) {
  message = getSlackMessage(message_type)
  color = color_region_map[params.region]

  slackSend (
    color: "${color}",
    message: "${message}",
    teamDomain: 'surveysparrow',
    channel: '#production-deployments',
    tokenCredentialId: 'SlackIntegrationToken'
  )
}

def sendTestSlackNotification(message_type) {
  message = getSlackMessage(message_type)
  color = color_region_map[params.region]

  slackSend (
    color: "${color}",
    message: "${message}",
    teamDomain: 'surveysparrow',
    channel: '#production-jenkins-hangar',
    tokenCredentialId: 'SlackIntegrationToken'
  )
}

def sendStatusBasedNotification(messageType, channelName) {
  message = getSlackMessage(messageType)
  status  = messageType.split('_').last()
  color   = variables.STATUS_COLOR_MAP[status]

  sendMessage(color, message, "#${channelName}")
}

def sendSlackNotificationMarketplace(color, message_type) 
{
  message = getSlackMessage(message_type)
  
  slackSend (
    color: "${color}",
    message: "${message}",
    teamDomain: 'surveysparrow',
    tokenCredentialId: 'SlackIntegrationToken',
    channel: 'appnest-deployment-production'
  )

  slackSend (
    color: "${color}",
    message: "${message}",
    teamDomain: 'surveysparrow',
    tokenCredentialId: 'SlackIntegrationToken',
    channel: 'sse-squad-appnest'
  )
}


def sendSlackNotificationSparrowpay(color, message_type)
{
  message = getSlackMessage(message_type)
  slackSend (
    color: "${color}",
    message: "${message}",
    teamDomain: 'surveysparrow',
    tokenCredentialId: 'SlackIntegrationToken',
    channel: 'sparrowpay-deployments'
  )
}

def sendSlackNotificationWebsite(color, message_type) {
  message = getSlackMessage(message_type)
  slackSend (
    color: "${color}",
    message: "${message}",
    teamDomain: 'surveysparrow',
    tokenCredentialId: 'SlackIntegrationToken',
    channel: 'production-deployment-website'
  )
}

def sendSlackNofiticationMicroservices(message_type) {
  message = getSlackMessage(message_type)
  color   = params.region == null ? "#000000" : color_region_map[params.region]

  if (params.releasebranch != null) {
    releaseChannel = params.releasebranch.replaceAll('/', '-')
    sendMessage(color, message, releaseChannel)
  }
}

return this