import os
from slack_sdk import WebClient
import argparse

SLACK_TOKEN = os.environ['SLACK_TOKEN']
BAR_LENGTH = 66
BLOCK_CHAR = '■'
SPACE_CHAR = '□'
# BLOCK_CHAR = '▓'
# SPACE_CHAR = '░'

MESSAGE_TEMPLATE = [
  {
    "type": "header",
    "text": {
      "type": "plain_text",
      "text": "[MESSAGE]",
      "emoji": True
    }
	},
  {
    "type": "divider"
  },
  {
    "type": "section",
    "text": {
      "type": "mrkdwn",
      "text": "[PROGRESS]"
    }
  }
]

def get_flagged_message(message):
  if ("failed" in message):
    return ":bomb: " + message
  if ("US" in message):
    return ":flag-us: " + message
  if ("EU" in message):
    return ":flag-eu: " + message
  if ("ME" in message):
    return ":flag-ae: " + message
  if ("AP" in message):
    return ":flag-in: " + message
  if ("UK" in message):
    return ":flag-gb: " + message
  if ("SY" in message):
    return ":flag-au: " + message
  if ("CA" in message):
    return ":flag-ca: " + message

def get_emoji_for_percentage(percentage):
  if (percentage >= 0 and percentage <= 16):
    return ":canoe:"
  if (percentage >= 17 and percentage <= 32):
    return ":speedboat:"
  if (percentage >= 33 and percentage <= 48):
    return ":motor_boat:"
  if (percentage >= 49 and percentage <= 64):
    return ":ferry:"
  if (percentage >= 65 and percentage <= 80):
    return ":passenger_ship:"
  if (percentage >= 80 and percentage < 100):
    return ":ship:"
  if (percentage == 100):
    return ":pirate_flag:"

def get_progress_bar(percentage):
  num_blocks = int(BAR_LENGTH * percentage / 100)
  num_spaces = BAR_LENGTH - num_blocks

  return f'`[{BLOCK_CHAR * num_blocks}{SPACE_CHAR * num_spaces}] {percentage}%`'

def get_estimated_time_to_complete(etc, percentage, message):
  duration_in_minutes = etc / (1000 * 60)
  if (duration_in_minutes >= 60):
    duration_in_hours = duration_in_minutes / 60
    if (percentage == 100):
      return f"Time taken to complete - `{duration_in_hours:.2f} hours`"
    if ("failed" in message  or "aborted" in message):
      return f"Time taken - `{duration_in_hours:.2f} hours`"
    return f"Estimated time to complete - `{duration_in_hours:.2f} hours`"
  else:
    if (percentage == 100):
      return f"Time taken to complete - `{duration_in_minutes:.2f} minutes`"
    if ("failed" in message  or "aborted" in message):
      return f"Time taken - `{duration_in_hours:.2f} hours`"
    return f"Estimated time to complete - `{duration_in_minutes:.2f} minutes`"

def get_progress_message(percentage, stage, etc, message):
  emoji         = get_emoji_for_percentage(percentage)
  progress_bar  = get_progress_bar(percentage)
  etc_message   = get_estimated_time_to_complete(etc, percentage, message)
  stage_message = f"Stage - `{stage}`"

  return emoji + " " + progress_bar + "\n\n" + stage_message + "\n" + etc_message

def main():
  try:
    parser = argparse.ArgumentParser(description="Utility to send progress bar as slack message")
    parser.add_argument('-m',
                        '--message',
                        help="Message to update",
                        type=str,
                        required=True)
    parser.add_argument('-p',
                        '--percentage',
                        help="Percentage to update",
                        type=float,
                        required=True)
    parser.add_argument('-t',
                        '--timestamp',
                        help='Timestamp of the message to update',
                        type=str)
    parser.add_argument('-s',
                        '--stage',
                        help="The current deployment stage",
                        type=str,
                        required=True)
    parser.add_argument('-c',
                        '--channel',
                        help="Channel to post message to",
                        type=str,
                        required=True)
    parser.add_argument('-e',
                        '--etc',
                        help="Estimated time to complete",
                        type=str,
                        required=True)
    args = parser.parse_args()

    client = WebClient(token=SLACK_TOKEN)

    flagged_message   = get_flagged_message(args.message)
    progress_message = get_progress_message(args.percentage, args.stage, float(args.etc), args.message)
    
    for object in MESSAGE_TEMPLATE:
      if('text' in object.keys()):
        object['text']['text'] = object['text']['text'].replace("[MESSAGE]", flagged_message)
        object['text']['text'] = object['text']['text'].replace("[PROGRESS]", progress_message)

    if (args.timestamp == None or args.timestamp == 'null'):
      response = client.chat_postMessage(
        channel=args.channel,
        blocks=MESSAGE_TEMPLATE,
      )
    else:
      response = client.chat_update(
        channel=args.channel,
        blocks=MESSAGE_TEMPLATE,
        ts=args.timestamp
      )
    
    print(response['ts'])


  except Exception as e:
    print(e)
    exit(1)

if (__name__ == "__main__"):
    main()