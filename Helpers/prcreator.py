import requests
import json
import argparse
import os


BB_TOKEN = os.environ['BB_TOKEN']

def main():
  parser = argparse.ArgumentParser(description="Utility to create pull request")
  parser.add_argument('-r', '--repo', help='Repository name', required=True, type=str)
  parser.add_argument('-b', '--branch', help='Branch name', required=True, type=str)
  parser.add_argument('-t', '--title', help='Title of pull request', required=True, type=str)
  args = parser.parse_args()

  url = f'https://api.bitbucket.org/2.0/repositories/surveysparrow/{args.repo}/pullrequests'
  headers = {
    'Content-Type': 'application/json',
    'Authorization': f"Basic {BB_TOKEN}"
  }

  data = {
    "title": args.title,
    "source": {
      "branch": {
        "name": args.branch
      }
    },
    "destination": {
      "branch": {
        "name": "master"
      }
    }
  }

  response = requests.post(url, headers=headers, data=json.dumps(data))
  if (response.status_code == 201):
    print('Pull request created successfully')
    exit(0)
  else:
    print('Failed to create pull request')
    exit(1)

if __name__ == '__main__':
  main()