import requests
import base64
import json
import os
import argparse

CREDENTIALS = os.environ['JIRA_TOKEN']
PROJECT_KEY_1 = os.environ['PROJECT_KEY_1']
PROJECT_KEY_2 = os.environ['PROJECT_KEY_2']
VERSION = os.environ['VERSION']
CLOUD_ID = "5105c137-e612-4c03-9cb9-f113906a655c"
ACTIVATION_ID = "bd28778f-cc9e-40b0-85f6-219a7abcd61b"
DEPLOYMENT_TAG = os.environ['DEPLOYMENT_TAG']

SLACK_URL = "*******************************************************************************"
# SLACK_URL = "*******************************************************************************" # Oliver <PERSON> Webhook for testing purpose

MD_VIEW = "customfield_10169"
PMA_VIEW = "customfield_10170" 


auth_token = base64.b64encode(CREDENTIALS.encode('utf-8')).decode('utf-8')
headers = {
  'Authorization': 'Basic ' + auth_token
}

def get_transition_id( ticket_id ):

  url = f"https://surveysparrow.atlassian.net/rest/api/2/issue/{ticket_id}/transitions?expand=transitions.fields"

  response = requests.get(url, headers=headers)
  response=response.json()
  for trans in response["transitions"]:
    if trans["name"] == "Done":
      return trans["id"]

def update_ticket_status(ticket_id):
  url = f"https://surveysparrow.atlassian.net/rest/api/2/issue/{ticket_id}/transitions?expand=transitions.fields"

  payload = {
    "transition": {
      "id": f"{get_transition_id( ticket_id )}"
    }
  }

  response = requests.post(url, json=payload, headers=headers)
  if response.status_code != 204:
    print("Error changing status of ticket :",ticket_id , response.status_code)

def get_specific_version(version_list, release_version):  
  for version in version_list:
    if (version['name'] == release_version):
      return version['id']
  message = f"No version ID found for {release_version}"

  print(message)
  return False


def get_versions(PROJECT_KEY):
  get_version_url = f"https://surveysparrow.atlassian.net/rest/api/latest/project/{PROJECT_KEY}/versions"
  response = requests.request("GET", get_version_url, headers=headers)
  return response.json()

def get_issues_on_version(version_id):
  get_issues_url = f"https://surveysparrow.atlassian.net/rest/api/2/search?jql=fixVersion={version_id}"
  response = requests.request("GET", get_issues_url, headers=headers)
  return response.json()

def filter_necessary_fields_from_issues(issue_type_issue_dict , issues_list_p1 , view_p1 ):

  # print(issues_list_p1)

  for issue in issues_list_p1:
    if issue['fields']['issuetype']['name'] not in issue_type_issue_dict:
      issue_type_issue_dict[issue['fields']['issuetype']['name']] = []
    
    issue_type_issue_dict[issue['fields']['issuetype']['name']].append({
      'key': issue['key'],
      'link': f"https://surveysparrow.atlassian.net/browse/{issue['key']}",
      'summary': issue['fields']['summary'],
      'view': None if view_p1 not in issue['fields'] else issue['fields'][view_p1]
    })
  
  print(issue_type_issue_dict)
  return issue_type_issue_dict

def get_version_ari(version_id):
  version_ari = "ari:cloud:jira:" + CLOUD_ID + ":version/activation/" + ACTIVATION_ID + "/" + version_id
  return version_ari

def fetch_related_work(version_id):
    try:
      graphql_url = "https://surveysparrow.atlassian.net/gateway/api/graphql"
      version_ari = get_version_ari(version_id)
      graphql_payload = {
          "query": "query getRelatedWork($id: ID!){jira{version(id: $id){...on JiraVersion {id,name,relatedWorkV2{edges{cursor,node {...on JiraVersionRelatedWorkGenericLink{relatedWorkId,category,title,url}}}}}}}}",
          "variables": {
            "id": version_ari
          }
      }
      headers['Content-Type'] = "application/json"
      headers['X-Experimentalapi'] = "ariGraph,boardCardMove,deleteCard,JiraJqlBuilder,SoftwareCardTypeTransitions,jira-releases-v0,createCustomFilter,updateCustomFilter,deleteCustomFilter,customFilters,PermissionScheme,JiraIssue,projectStyle,startSprintPrototype,AddIssuesToFixVersion,JiraVersionResult,JiraIssueConnectionJql,JiraFieldOptionSearching,JiraIssueFieldMutations,JiraIssueDevInfoDetails,JiraIssueDevSummaryCache,JiraVersionWarningConfig,JiraReleaseNotesConfiguration,JiraUpdateReleaseNotesConfiguration,ReleaseNotes,ReleaseNotesOptions,DeploymentsFeaturePrecondition,UpdateVersionWarningConfig,UpdateVersionName,UpdateVersionDescription,UpdateVersionStartDate,UpdateVersionReleaseDate,VersionsForProject,RelatedWork,SuggestedRelatedWorkCategories,setIssueMediaVisibility,toggleBoardFeature,virtual-agent-beta,JiraProject,RoadmapsMutation,RoadmapsQuery,JiraApplicationProperties,JiraIssueSearch,JiraFilter,featureGroups,setBoardEstimationType,softwareBoards,name,AddRelatedWorkToVersion,RemoveRelatedWorkFromVersion,admins,canAdministerBoard,jql,globalCardCreateAdditionalFields,GlobalTimeTrackingSettings,ReleaseNotesOptions,search-experience,MoveOrRemoveIssuesToFixVersion,compass-beta,JiraIssueSearchStatus,DevOpsSummarisedDeployments,DevOpsSummarisedEntities,JiraDevOps,devOps,DevOpsProvider,JiraDevOpsProviders"
      
      response = requests.request(
        "POST", graphql_url, headers=headers, json=graphql_payload
      )
      
      json_response = response.json()
      related_work = json_response['data']['jira']['version']['relatedWorkV2']['edges']
      
      related_work_message = ""
      for report in related_work:
          related_work_message += f"<{report['node']['url']}|{report['node']['title']}>" + ", "
      
      related_work_message = related_work_message[:-2]
    except Exception as e:
      print("Exception while fetching related work - ", e)
      return None
    return related_work_message

def construct_slack_message(issue_key_desc, message_to_post ):

  issues_order = ['Story', 'Task', 'Sub-Task', 'Defect' ,'Bug']
  for issue_type in issues_order:
    if (issue_type in issue_key_desc):
      message_to_post += "*" + issue_type + "*\n"
      for issue in issue_key_desc[issue_type]:
        update_ticket_status(issue['key'])
        if issue['view'] != None:
          message_to_post += f"<{issue['link']}|{issue['key']}> {issue['summary']} - <{issue['view']}|view>\n"
        else:
          message_to_post += f"<{issue['link']}|{issue['key']}> {issue['summary']}\n"
      message_to_post += "\n"
      print(message_to_post)
  return message_to_post


def send_slack_message(message):
  
  headers['Content-Type'] = 'application/json'
  response = requests.request("POST", SLACK_URL, headers=headers, data=json.dumps(message))
  print(response.text)

def release_version_in_jira(version_id):
  # print("inside", version_id)
  release_version_url = f"https://surveysparrow.atlassian.net/rest/api/2/version/{version_id}"
  payload = { 'released': True }
  headers['Content-Type'] = 'application/json'
  response = requests.request("PUT", release_version_url, headers=headers, data=json.dumps(payload))
  # print(response.text)

def main():
    try:
      parser = argparse.ArgumentParser(description="Utility to fetch jira issue keys")
      parser.add_argument('-k',
                          '--keys',
                          help="Fetch Issue Keys",
                          action='store_true')
      
      parser.add_argument('-p',
                          '--postmessage',
                          help="Post Deployment message in slack",
                          action='store_true')
      
      parser.add_argument('-r',
                          '--releaseversion',
                          help="Release Version in Jira",
                          action='store_true')
      args = parser.parse_args()

      # Fetching data from the Product Marketing Project 1 Jira Board
      #
      # print("[+] Fetching version for release branch - ", os.environ['RELEASE_BRANCH'])
      version_list_PMA = get_versions(PROJECT_KEY_1)
      version_PMA = get_specific_version(version_list_PMA, os.environ['RELEASE_BRANCH'])

      issues_list_PMA = []
      issues_list_MD  = []
      if version_PMA != False:
        # print("[+] Release Version ID - ", version_PMA)
        issues_list_PMA = get_issues_on_version(version_PMA)
      #

      # Fetching data from theMarketing Dev Project 2 Jira Board
      #
      # print("[+] Fetching version for release branch - ", os.environ['RELEASE_BRANCH'])
      version_list_MD = get_versions(PROJECT_KEY_2)
      version_MD = get_specific_version(version_list_MD, os.environ['RELEASE_BRANCH'])
      
      if version_MD != False:
        # print("[+] Release Version ID - ", version_MD)
        issues_list_MD = get_issues_on_version(version_MD)
      #

      # print(f"PMD: {version_PMA} MD: {version_MD}")


      if (args.keys):
        issues = []
        if issues_list_MD != []:
          for issue_md in issues_list_MD['issues']:
            issues.append(issue_md['key'])
        if issues_list_PMA != []:
          for issue_pma in issues_list_PMA['issues']:
            issues.append(issue_pma['key'])
        print(issues)

      if (args.postmessage):
        message_to_post = f"Deployment tag - `{DEPLOYMENT_TAG}` || {VERSION}\n\n"

        issue_key_desc = {}

        if version_PMA != False and version_MD != False:
          # Get the Product Marketing and Marketing Dev Issues
          issue_key_desc = filter_necessary_fields_from_issues( issue_key_desc,  issues_list_MD['issues'] , PMA_VIEW )
          issue_key_desc = filter_necessary_fields_from_issues( issue_key_desc,  issues_list_PMA['issues']  , MD_VIEW )
          
        elif version_PMA == False and version_MD != False:
          issue_key_desc = filter_necessary_fields_from_issues( issue_key_desc,  issues_list_MD['issues'] , MD_VIEW )
        
        elif version_PMA != False and version_MD == False:
          issue_key_desc = filter_necessary_fields_from_issues( issue_key_desc,  issues_list_PMA['issues'] , PMA_VIEW )

        elif version_PMA == False and version_MD == False:
          exit("No release Present in Both the Boards")
        
        # Construct the message from the issues
        message_to_post = construct_slack_message( issue_key_desc, message_to_post )

        print("[+] Posting Message - ", message_to_post)
        send_slack_message({ "text" : message_to_post })
      
      if (args.releaseversion):
        if version_PMA != False and version_MD != False:
          release_version_in_jira(version_PMA)
          release_version_in_jira(version_MD)
          
        elif version_PMA == False and version_MD != False:
          release_version_in_jira(version_MD)
        
        elif version_PMA != False and version_MD == False:
          release_version_in_jira(version_PMA)

        elif version_PMA == False and version_MD == False:
          exit("No release Present in Both the Boards")

    except Exception as e:
      print(e)
      exit(1)

if (__name__ == "__main__"):
    main()
