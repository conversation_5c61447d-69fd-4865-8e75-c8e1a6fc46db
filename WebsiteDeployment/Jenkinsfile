def sendSlackNotification(color, message) {
    wrap([$class: 'BuildUser'])
    {
        slackSend (
            color: "${color}",
            message: "${message}",
            teamDomain: 'surveysparrow',
            tokenCredentialId: 'SlackIntegrationToken',
            channel: 'production-deployment-website'
        )
    }
}
pipeline 
{

    // Worker node in which the test will run
    agent { label 'website-deployment-testing' }

    // Pipeline Environment Variable
    environment
    {
        BUCKET_NAME = 'elasticbeanstalk-us-east-1-556123534406'
        APPLICATION = 'ss-website'
        ENVIRONMENT = 'ss-website-prod'
        REGION = 'us-east-1'
        CODE_BRANCH = 'master'
        GIT_URL_DEPLOYMENT = '*****************:surveysparrow/surveysparrow-site.git'
        PROJECT_KEY_1 = 'MD'
        PROJECT_KEY_2 = 'PMA'
        JIRA_TOKEN = credentials('JiraAuthenticationToken')
        DEFAULT_CREDENTIALS = 'BitBucketPrivateKey'
    }

    // Jenkins Build Parameters
    parameters
    {
        booleanParam ( 
            name: 'release_deployment', 
            defaultValue: true, 
            description: 'By DEFAULT it will TRUE as by default it will be a RELEASE process deployment. UNCHECK !!! the box if you want to perform a NON RELEASE DEPLOYMENT or Urgent deployment' 
        )
    }

    stages 
    {
        stage("Git Clone")
        {
            steps
            {
                sh "sudo rm -rf /home/<USER>/.ssh/known_hosts"
                dir('surveysparrow-site')
                {
                    git branch: "${CODE_BRANCH}", credentialsId: "${env.DEFAULT_CREDENTIALS}", url: "${env.GIT_URL_DEPLOYMENT}"
                }
            }
        }
        stage("Create Deployment Tag")
        {
            when { expression { params.release_deployment == true } }
            steps 
            {
                script
                {
                    sshagent (credentials: ["${env.DEFAULT_CREDENTIALS}"]) 
                    {
                        sh "pip3 install requests==2.28.2 urllib3==1.26.9 argparse"

                        dir('surveysparrow-site')
                        {

                            script 
                            {
                                echo "[+] Adding Git Config"
                                echo "[+] Git Merge Release Branch to Master"
                                
                                sh """
                                    git config user.email "<EMAIL>"
                                    git config user.name "Oliver Paul"
                                    git checkout --track origin/master
                                    git pull
                                    git merge ${CODE_BRANCH}
                                    git push origin master
                                """

                                echo "[+] Creating a Git Tag and Pushing the Tag"
                                GIT_TAG = "production-${new Date().format('dd-MM-yyyy')}"
                                GIT_TAG_COUNT = sh(script: "git tag --list '${GIT_TAG}-*' | wc -l | tr -d '[:space:]'", returnStdout: true).trim().toInteger() + 1
                                GIT_TAG_FULL = "${GIT_TAG}-${GIT_TAG_COUNT}"

                                sh """
                                    git tag ${GIT_TAG_FULL}
                                    git push origin ${GIT_TAG_FULL}
                                """
                                
                                sendSlackNotification(
                                    "#000000",
                                    ":rocket: *Deployment Triggered !!!* \n Release Branch: `${CODE_BRANCH}` \n STARTED by: `${env.BUILD_USER}` \n For More details - ${env.BUILD_URL}"
                                )
                            
                                sendSlackNotification(
                                    "#F8DE7E",
                                    ":white_check_mark: *PR MERGED & Deployment Tag Created !!!* \n Release Branch: `${CODE_BRANCH}` \n Deployment Tag: `${GIT_TAG_FULL}` \n STARTED by: `${env.BUILD_USER}`"
                                )
                                
                            }

                        }
                    
                        echo "[+] Merging ${CODE_BRANCH} to master in surveysparrow-site Repo and creating a Deployment Tag."
                        issuekeys = sh(returnStdout: true, script: 'DEPLOYMENT_TAG=${GIT_TAG_FULL} VERSION=${CODE_BRANCH} RELEASE_BRANCH=${CODE_BRANCH} JIRA_TOKEN=${JIRA_TOKEN} PROJECT_KEY_1=${PROJECT_KEY_1} PROJECT_KEY_2=${PROJECT_KEY_2} python3 Helpers/website-jira-helper.py -k').trim()
                        issueKeysArr = "${issuekeys.replace('[','')}"
                        issueKeysArr = "${issueKeysArr.replace(']', '')}"

                        issueList = []
                        for(issue in issueKeysArr.split(',')) {
                            issueList.add(issue.replace("'", '').trim())
                        }

                        jiraSendDeploymentInfo environmentId: 'us-east-1', environmentName: 'us-east-1', environmentType: 'production', issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'in_progress'
                    }
                }
            }
        }
        stage('Compiling the Project')
        {
            steps
            {
                dir('surveysparrow-site')
                {
                    script
                    {
                        // Changing the build name to version param
                        workspace_path = sh (script: "pwd | tr -d '\n'", returnStdout: true)

                        // Creating a unique application deployment version
                        echo "[+] Making a new version of the application. For Environment: Surveysparrow Production"
                        version_number = sh (script: "echo 'Surveysparrow-v30.6.${currentBuild.number}' | tr -d '\n'", returnStdout: true)


                        echo "[+] The Next Production Version: ${version_number}"
                        currentBuild.displayName = "${version_number}"

                        
                        if ( params.release_deployment )
                        {
                            echo "Release Deployment Slack message"
                            
                            sendSlackNotification(
                                "#0066CC",
                                ":airplane_departure: *Stating Deployment !!!*\n Release Branch Name: `${CODE_BRANCH}` \n Deployment Tag: `${GIT_TAG_FULL}` \n Version: `${version_number}` \n has STARTED by: `${env.BUILD_USER}`"
                            )
                            
                        }
                        else 
                        {
                            echo "Non Release Deployment Slack message"
                            
                            sendSlackNotification(
                                "#0066CC",
                                ":rocket: Deployment of Branch Name: `${CODE_BRANCH}` \n has STARTED by: `${env.BUILD_USER}` \nApplication: `${env.APPLICATION}` !! \n Version: `${version_number}`"
                            )
                            
                        }
                    }
                    echo "Compiling the Project"
                    echo "$workspace_path"

                    dir('wp-content/themes/survey-sparrow/assets')
                    {
                        nodejs('Node-v10.20.1')
                        {
                            echo "[+] Running NPM Install"
                            sh "npm install"
                            sh "npm install --only=dev"

                            echo "[+] Running Gulp Prod"
                            sh """
                                npm install -g gulp-cli@2.2.1
                                gulp prod
                            """
                        }
                    }
                }
            }
        }
        stage('Making a Zip file')
        {
            steps
            {
                dir('surveysparrow-site')
                {
                    echo "[+] Running bash build.sh"
                    sh "bash build.sh"
                }
            }
        }
        stage('Uploading Zip to Elatic Beanstalk')
        {
            steps
            {
                dir('surveysparrow-site')
                {
                    echo "[+] Renaming the Zip file"
                    sh "mv wordpress-4.8.zip ${version_number}.zip"

                    echo "[+] Pushing the Zip file to survey-sparrow-site-source-code bucket"

                    echo "[+] aws s3 cp ${workspace_path}/${version_number}.zip s3://${env.BUCKET_NAME}"
                    sh "aws s3 cp ${workspace_path}/${version_number}.zip s3://${env.BUCKET_NAME}"

                    script
                    {
                        echo "[+] Making a new version of the application. Version: ${version_number} in Environment: ${env.ENVIRONMENT}"
                        sh "aws elasticbeanstalk create-application-version --region ${env.REGION} --application-name ${env.APPLICATION} --version-label ${version_number} --source-bundle S3Bucket=${env.BUCKET_NAME},S3Key='${version_number}.zip'"
                    }
                }
            }
        }
        stage('Deploy to Elastic-Beanstalk')
        {
            steps
            {

                echo "[+] Deploying to Elastic-Beanstalk"

                dir('surveysparrow-site')
                {
                    script
                    {
                        echo "[+] Deploying the Apllication version which was created in Environment:${env.ENVIRONMENT} in application:${env.APPLICATION}"
                        sh "aws elasticbeanstalk update-environment --region ${env.REGION} --application-name ${env.APPLICATION} --environment-name ${env.ENVIRONMENT} --version-label ${version_number}"

                        echo "Waiting for the application to get Deployed"
                        while(true)
                        {
                            STG_DEPLOYMENT_SUMMARY = sh (
                                script: "aws elasticbeanstalk describe-environments --region ${env.region} --application-name ${env.APPLICATION}  --environment-name ${env.ENVIRONMENT}",
                                returnStdout: true
                            ).trim()

                            def stgDeploySummaryObj = readJSON text: "${STG_DEPLOYMENT_SUMMARY}"
                            def stgDeployStatus = stgDeploySummaryObj.Environments[0].Status

                            echo "$stgDeployStatus"

                            if(stgDeployStatus == "Updating")
                            {
                                echo "$stgDeployStatus - Still updating to latest version"
                                sleep(10)
                                continue
                            }
                            else
                            {
                                if(stgDeployStatus == "Ready") {
                                    echo "$stgDeployStatus Deployment of Version: ${version_number} is done !!!"
                                }
                                else
                                {
                                    error "Staging Deployment of Version: ${version_number} Failed! Exiting Pipeline $stgDeployStatus"
                                }
                                break
                            }
                        }
                    }
                }
            }
        }
    }
    post 
    {
        always 
        {
            script
            {
                if ( params.release_deployment )
                {
                    jiraSendDeploymentInfo environmentId: 'us-east-1', environmentName: 'us-east-1', environmentType: 'production', issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'successful'
                    input(message: 'Post message to #marketind-dev ?')
                    sh "VERSION=${version_number} RELEASE_BRANCH=${CODE_BRANCH} DEPLOYMENT_TAG=${GIT_TAG_FULL} JIRA_TOKEN=${JIRA_TOKEN} PROJECT_KEY_1=${PROJECT_KEY_1} PROJECT_KEY_2=${PROJECT_KEY_2} python3 Helpers/website-jira-helper.py -p"
                    sh "VERSION=${version_number} RELEASE_BRANCH=${CODE_BRANCH} DEPLOYMENT_TAG=${GIT_TAG_FULL} JIRA_TOKEN=${JIRA_TOKEN} PROJECT_KEY_1=${PROJECT_KEY_1} PROJECT_KEY_2=${PROJECT_KEY_2} python3 Helpers/website-jira-helper.py -r"
                }
                currentBuild.description = "Deployment of Branch Name: ${CODE_BRANCH}"
                cleanWs()
            }
        }
        success 
        {
            script 
            {
                if ( params.release_deployment )
                {
                    sendSlackNotification (
                    "#2ECC71",
                    ":sparrow_love: *Deployment SUCCESSFUL !!!*\n Release Branch Name: `${CODE_BRANCH}` \n Deployment Tag: `${GIT_TAG_FULL}` \n STARTED by: `${env.BUILD_USER}` \n Current Version: `${version_number}`"
                    )
                    
                }
                else
                {
                    sendSlackNotification (
                    "#2ECC71",
                    ":sparrow_love: Deployment Branch Name: `${CODE_BRANCH}` \n STARTED by: `${env.BUILD_USER}` \nis SUCCESSFUL Done :tada: \nApplication: `${env.APPLICATION}` !! \n Current Version: `${version_number}`"
                    )
                }
            }
        }

        failure 
        {
            
            script
            {
                if ( params.release_deployment )
                {
                    sendSlackNotification (
                    "#E74C3C",
                    ":red_circle: Deployment of Branch Name: `${CODE_BRANCH}` \n STARTED by: `${env.BUILD_USER}` has FAILED \nApplication: `${env.APPLICATION}` !! \n For More details - ${env.BUILD_URL}"
                    )
                }
                else
                {
                    sendSlackNotification (
                    "#E74C3C",
                    ":red_circle: Deployment of Branch Name: `${CODE_BRANCH}` \n STARTED by: `${env.BUILD_USER}` has FAILED \nApplication: `${env.APPLICATION}` !! \n For More details - ${env.BUILD_URL}"
                    )
                }
            }
        }
        aborted 
        {
            
            script
            {
                if ( params.release_deployment )
                {
                    sendSlackNotification (
                    "#898989",
                    ":construction: Deployment of Branch Name: `${CODE_BRANCH}` \n STARTED by: `${env.BUILD_USER}` has Been ABORTED \nApplication: `${env.APPLICATION}` !! \n For More details - ${env.BUILD_URL}"
                    )
                    
                }
                else
                {
                    sendSlackNotification (
                    "#898989",
                    ":construction: Deployment of Branch Name: `${CODE_BRANCH}` \n STARTED by: `${env.BUILD_USER}` has Been ABORTED \nApplication: `${env.APPLICATION}` !! \n For More details - ${env.BUILD_URL}"
                    )
                }
            }
        }
    }
}
