pipeline {
    agent {
      label "ArmDockerBuilderFleet"
    }

    environment {
        ARGOCD_CRED      = credentials('SurveySparrowArgoCD')
        CLOUDFLARE_API_TOKEN = credentials('cloudflare-token')
    }

    stages {
        stage("Git Clone") {
            steps{
                script {
                    variables = load 'Helpers/variables.groovy'
                    notifier = load 'Helpers/notifications.groovy'
                    methods = load 'Helpers/methods.groovy'
                    
                    sh "sudo yum install -y gcc-c++"
                    
                    notifier.sendSlackNotificationWebsite("#0066CC", "SS_WEBSITE_DEPLOYMENT_STARTED")
                    
                    dir("nextjs") {
                        methods.cloneRepo(variables.WEBSITE_REPO)
                    }
                    dir(variables.CONFIG_REPO_FOLDER) {
                        methods.cloneConfig()
                    }
                    dir(variables.EKS_REPO_FOLDER) {
                        methods.cloneEKSYAMLRepo("master")
                    }
                }
            }
        }
        stage("NextJs Asset Build") {
            steps {
                script {
                    currentBuild.displayName = "Asset Building: ${currentBuild.number}"                    
                    def commitHash = sh (script: "git log -n 1 --pretty=format:'%H'", returnStdout: true)
                    FULL_ASSET_ID = "prod-$commitHash-${currentBuild.number}"
                    ECR_BUILD_ID = "${variables.PRODUCTION_BRANCH}_${commitHash.substring(0,7)}_${currentBuild.number}"
                    ECR_BUILD_ID = ECR_BUILD_ID.replaceAll('/', '_')
                    
                    dir("nextjs/packages/next-client") {
                        sh "sed -i 's/BUILD_VERSION=prod-[a-zA-Z0-9]*-[0-9]*/BUILD_VERSION=${FULL_ASSET_ID}/' ${env.WORKSPACE}/${variables.CONFIG_REPO_FOLDER}/${variables.SS_NEXTJS_WEBSITE['configFile']}"
                        sh "cat ${env.WORKSPACE}/${variables.CONFIG_REPO_FOLDER}/${variables.SS_NEXTJS_WEBSITE['configFile']} | grep $FULL_ASSET_ID"
                        sh "cp ${env.WORKSPACE}/${variables.CONFIG_REPO_FOLDER}/${variables.SS_NEXTJS_WEBSITE['configFile']} .env.local"
                                            
                        nodejs('Node-v18.20.2') {
                            sh "yarn install"
                            sh "NODE_OPTIONS=--max_old_space_size=16384 yarn build"
                        }

                        sh "rm -rf node_modules/"
                        sh "cp ${env.WORKSPACE}/nextjs/node_modules/ . -R"                 
                    }
                }
            }
        }
 
        stage('NextJs Docker Build') {
            steps {
                script {
                    dir("nextjs/packages/next-client") {
                        ECR_REPO = "${variables.ECR_REPO_APP[variables.DEFAULT_REGION]}/${variables.SS_NEXTJS_WEBSITE['ecr']}"
                        sh "DOCKER_BUILDKIT=1 docker build -t $ECR_REPO:latest -t $ECR_REPO:$ECR_BUILD_ID ."
                    }
                }
            }
        }
        stage("Asset and Docker Upload") {
          steps {
                script {
                    sh "aws s3 cp --region ${variables.DEFAULT_REGION} ${env.WORKSPACE}/nextjs/packages/next-client/.next/static/ s3://${variables.ASSET_BUCKET}/website/${FULL_ASSET_ID}/_next/static/ --exclude '*.map' --recursive --quiet"

                    DEPLOYMENT_FILE = "01-nextjs-website-deployment.yaml"
                    CONFIG_YAML_FILE = "02-nextjs-website-configmap.yaml"

                    dir("${variables.EKS_REPO_FOLDER}/${variables.SS_NEXTJS_WEBSITE['eksPath']}") {
                        def deploymentData = readYaml file: DEPLOYMENT_FILE
                        deploymentData.spec.template.spec.containers[0].image = "$ECR_REPO:$ECR_BUILD_ID"
                        writeYaml file: DEPLOYMENT_FILE, data: deploymentData, overwrite: true
                    }

                    EKS_CONFIGMAP_PATH = "${env.WORKSPACE}/${variables.EKS_REPO_FOLDER}/${variables.SS_NEXTJS_WEBSITE['eksPath']}/${CONFIG_YAML_FILE}"
                    
                    dir(variables.CONFIG_REPO_FOLDER) {
                        sh "CONFIGMAPNAME=${variables.SS_NEXTJS_WEBSITE['configmap']} NAMESPACE=${variables.SS_NEXTJS_WEBSITE['namespace']} ENVFILE=${variables.SS_NEXTJS_WEBSITE['configFile']} TARGETFILE=$EKS_CONFIGMAP_PATH sh ../Helpers/env2configmap.sh"
                    }
                    
                    sh "aws ecr get-login-password --region ${variables.DEFAULT_REGION} | docker login --username AWS --password-stdin ${variables.ECR_REPO_APP[variables.DEFAULT_REGION]}"
                    sh "docker push $ECR_REPO:$ECR_BUILD_ID"
                    sh "docker push $ECR_REPO:latest"
                    sh "docker image prune -a -f"
                }
            }
        }
        stage("Asset and Deployment Sync and Validation") {
            steps {
                script {
                    currentBuild.displayName = "Syncing : ${variables.PRODUCTION_BRANCH}: ${currentBuild.number}"
                    currentBuild.description = "Syncing Deployment: ${variables.PRODUCTION_BRANCH}"

                    dir(variables.EKS_REPO_FOLDER) {
                        methods.commitAndPush("Updating Nextjs Latest Deployment Image:$ECR_BUILD_ID")
                    }
                    dir(variables.CONFIG_REPO_FOLDER) {
                        methods.commitAndPush("Updating Nextjs Latest Config Change Build ID: ${FULL_ASSET_ID}")
                    }
                }
            }
        }
        stage('Deploying to EKS') {
            steps {
                script {
                    currentBuild.displayName = "Syncing: ${variables.PRODUCTION_BRANCH}: ${currentBuild.number}"
                    currentBuild.description = "Deployment SYNCING for branch: ${variables.PRODUCTION_BRANCH}"
                    sh "argocd login ${variables.SURVEYSPARROW_ARGOCD_DOMAIN} --username ${env.ARGOCD_CRED_USR} --password ${env.ARGOCD_CRED_PSW}"
                    sh "argocd app sync ${variables.SS_NEXTJS_WEBSITE['app']} --grpc-web"
                    sh "argocd app wait ${variables.SS_NEXTJS_WEBSITE['app']} --grpc-web"
                    sh "argocd logout ${variables.SURVEYSPARROW_ARGOCD_DOMAIN}"
                }
            }
            post {
                success {
                    script {
                        currentAppVersion = sh(script: "aws ssm get-parameter --region ${variables.DEFAULT_REGION} --name '${variables.WEBSITE_NEXTJS_VERSION_PARAMETER}' --with-decryption | jq '.Parameter.Value' | tr -d '\n\"'", returnStdout: true)
                        echo "Current Version: $currentAppVersion"
                        latestAppVersion = (currentAppVersion.toInteger()  + 1).toString()
                        echo "Latest Deployment version: $latestAppVersion"
                        sh "aws ssm put-parameter --region ${variables.DEFAULT_REGION} --name '${variables.WEBSITE_NEXTJS_VERSION_PARAMETER}' --value '${latestAppVersion}' --type SecureString --overwrite"
                        sh "aws ssm put-parameter --region ${variables.DEFAULT_REGION} --name '${variables.WEBSITE_NEXTJS_BUNDLE_PARAMETER}' --value '$FULL_ASSET_ID|$ECR_BUILD_ID' --type SecureString --overwrite"
                    }
                }
            }
        }
        stage("Purge Cache") {
            steps {
                script {
                    domain_list = ""
                    for (domain in variables.CLOUDFLARE_DOMAINS.keySet()) {
                        zone_id = variables.CLOUDFLARE_DOMAINS[domain]["zoneid"]
                        methods.purgeCache(zone_id, CLOUDFLARE_API_TOKEN, "hosts", [domain])
                        domain_list += domain + " "
                    }
                }
            }
        }
    }

    post {
        always {
            cleanWs()        
        }
        success {
            script {
                notifier.sendSlackNotificationWebsite("#2ECC71", "SS_WEBSITE_DEPLOYMENT_SUCCESS")
                currentBuild.displayName = "Deployed: ${variables.PRODUCTION_BRANCH} : ${currentBuild.number} Version: ${latestAppVersion}"
                currentBuild.description = "Deployment SUCCESS for branch: ${variables.PRODUCTION_BRANCH}"
            }
        }
        failure {
            script {
                notifier.sendSlackNotificationWebsite("#E74C3C", "SS_WEBSITE_DEPLOYMENT_FAILURE")
                currentBuild.displayName = "Failure: ${variables.PRODUCTION_BRANCH} : ${currentBuild.number}"
                currentBuild.description = "Deployment FAILED for branch: ${variables.PRODUCTION_BRANCH}"
            }
        }
        aborted {
            script {
                notifier.sendSlackNotificationWebsite("#9D9D9D", "SS_WEBSITE_DEPLOYMENT_ABORTED")
                currentBuild.displayName = "Aborted: ${variables.PRODUCTION_BRANCH} : ${currentBuild.number}"
                currentBuild.description = "Deployment ABORTED for branch: ${variables.PRODUCTION_BRANCH}"
            }
        }
    }
}
