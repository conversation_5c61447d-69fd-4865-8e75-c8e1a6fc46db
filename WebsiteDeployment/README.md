# Surveysparrow Website Deployment #

This Jenkins Pipleine is used for deploying Surveysparrow website hosted in Elastic Beanstalk 

### What Does this Pipeline Do? ###

* Merge PR Created from Release branch to Master
* Create Deployment Tag
* Create a bundle for deployment in Elastic Beanstalk and upload it to S3
* Deployment of the bundle in S3 to Elastic Beanstalk in production
* Post Jira Tickets and Update Jira Ticket status to DONE

### Parameters ###

ReleaseBranch - The current release branch is the parameter which needed to be given as input. Type: String
