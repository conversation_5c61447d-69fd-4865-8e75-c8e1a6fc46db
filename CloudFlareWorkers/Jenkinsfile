pipeline {

    agent {
        label 'DockerBuilderFleet'
    }

    tools {
        nodejs 'Node-v18.20.2'
    }

    environment {
        CLOUDFLARE_API_TOKEN  = credentials('cloudflare-token')
    }

    parameters {
        string name: 'application', description: 'Name of the application to deploy the service'
        choice name: 'language', 
            choices: [ 
                'typescript', 
                'javascript'
            ], 
            description: 'Language you are using for the worker'
        string name: 'description', description: 'Reason for deployment'
    }

    stages {
        stage('Pull Code and Checkout Branch') {
            steps {       
                script{
                    if (application.isEmpty()) {
                        error("The application parameter cannot be empty")
                    }

                    if (description.isEmpty()) {
                        description = "No description provided"
                    }
                    variables         = load "Helpers/variables.groovy"
                    methods           = load "Helpers/methods.groovy"
                    notifier          = load "Helpers/notifications.groovy"

                    notifier.sendSlackNotification('CLOUDFLARE_DEPLOYMENT_STARTED')
                    dir("cloudflare-workers") {
                        methods.cloneRepo(variables.CLOUDFLARE_CODE_REPOSITORY, "master")                
                    }
                }
            }
        }

        stage('Install Packages') {
            steps {       
                script{
                    dir("cloudflare-workers/${params.application}") {
                        sh "npm i"
                    }
                }
            }
        }

        stage('Pull Config Repo and update env') {
            steps {
                script {
                    dir(variables.CONFIG_REPO_FOLDER) {
                        methods.cloneConfig()
                    }
                    def relative_path_configfile = "${params.application}.toml"
                    config_file = "${env.WORKSPACE}/${variables.CONFIG_REPO_FOLDER}/cloudflare-workers/${relative_path_configfile}"
                    dir ("cloudflare-workers/${params.application}") {
                        sh "cp ${config_file} wrangler.toml"
                    }
                }
            }
        }

        stage('Deploy to Cloudflare Worker') {
            steps {
                script {
                    dir("cloudflare-workers/${params.application}") {
                        env.CLOUDFLARE_ACCOUNT_ID = variables.CLOUDFLARE_ACCOUNT_ID
                        env.index_file = params.language == "javascript" ? "index.js" : "index.ts"
                        sh 'CLOUDFLARE_ACCOUNT_ID=$CLOUDFLARE_ACCOUNT_ID CLOUDFLARE_API_TOKEN=$CLOUDFLARE_API_TOKEN wrangler deploy --minify src/$index_file'
                    }
                }
            }
        }
    }

    post {
        always {
            echo "Cleaning Deployment Service Workspace"
            cleanWs()
        }
        success {
            script {
                notifier.sendSlackNotification('CLOUDFLARE_DEPLOYMENT_SUCCESSFUL')
            }
        }

        failure {
            script {
                notifier.sendSlackNotification('CLOUDFLARE_DEPLOYMENT_FAILED')
            }
        }

        aborted {
            script {
                notifier.sendSlackNotification('CLOUDFLARE_DEPLOYMENT_ABORTED')
            }
        }
    }
}