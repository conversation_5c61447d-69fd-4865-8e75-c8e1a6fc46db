def setNotificationParams() {
    wrap([$class: 'BuildUser']) {
        notificationParams = [
            'pipeline': 'Edith deployment',
            'Region': AWS_REGION,
            'Branch': variables.PRODUCTION_BRANCH,
            'Layer': params.application,
            'Started By': env.BUILD_USER,
            'Reason': params.reason,
        ]   
    }
}


def deploymentContextFile = [
    "api-server": "edith/05-edith-api-server-deployment.yaml",
    "worker": "edith/10-edith-worker-deployment.yaml"
]

properties([
    parameters([
        choice(name: 'application', choices: ['api-server', 'worker'], description: 'Select your deployment service'),
        string(name: 'reason', description: 'Specify the reason for your build', trim: true)
    ])
])

pipeline {
    agent {
        label 'ArmDockerBuilderFleet'
    }

    environment {
        ARGOCD_PASSWORD = credentials('PlatformArgoCD')
    }

    stages {
        stage('Initialize') {
            steps {
                script {
                    variables         = load 'Helpers/variables.groovy'
                    notifier          = load 'Helpers/notifications.groovy'
                    methods           = load 'Helpers/methods.groovy'

                    REGION_CODE       = env.JOB_BASE_NAME
                    AWS_REGION        = variables.REGION_CODE_DC_NAME_MAP[REGION_CODE]
                    EDITH             = variables.SERVICES["Edith"]

                    setNotificationParams()
                    notifier.sendOpsRelatedNotifcationToChannel("STARTED", variables.EDITH_DEPLOYMENT_CHANNEL)
                }
            }
        }
        stage('Clone Repositories') {
            steps {
                script {
                    dir('edith') {
                        methods.cloneRepo(variables.EDITH_MAILER_REPO, variables.PRODUCTION_BRANCH)
                    }
                    dir(variables.EKS_REPO_FOLDER) {
                        methods.cloneRepoWithGit(variables.KUBERNETES_YAML_REPO, "master")
                    }
                    dir(variables.CONFIG_REPO_FOLDER) {
                        methods.cloneConfig()
                    }
                }
            }
        }
        stage('Build') {
            steps {
                dir('edith') {
                    script {
                        latest_commit = sh(script: 'git rev-parse --short HEAD', returnStdout: true).trim()
                        ECR_IMAGE_REPO = "${variables.PLATFORM_ECR_REPO_APP[AWS_REGION]}/edith-mailer/${params.application}"
                        IMAGE_TAG = "edith_${variables.PRODUCTION_BRANCH}_${latest_commit}"

                        sh "docker build --build-arg BUILD_VERSION=${env.BUILD_NUMBER} --target ${params.application} -t ${ECR_IMAGE_REPO}:${IMAGE_TAG} -t ${ECR_IMAGE_REPO}:latest ."
                        sh 'docker images'

                        withAWS(region: AWS_REGION, roleAccount: EDITH["account"], role: EDITH["role"]) {
                            sh "aws ecr get-login-password | docker login --username AWS --password-stdin ${variables.PLATFORM_ECR_REPO_APP[AWS_REGION]}"
                            sh "docker push ${ECR_IMAGE_REPO}:${IMAGE_TAG}"
                            sh "docker push ${ECR_IMAGE_REPO}:latest"
                        }
                    }
                }
            }
        }
        stage('Update K8s Contexts') {
            steps {
                script {
                    dir(variables.EKS_REPO_FOLDER) {
                        KUBERNETES_CONFIG_EKS_FILE = "edith/03-edith-configmap.yaml"

                        CONFIG_REPO_PATH = "${env.WORKSPACE}/${variables.CONFIG_REPO_FOLDER}"
                        JSON_CONFIG_FILE = "${CONFIG_REPO_PATH}/Platform/edith/${AWS_REGION}/config.json"

                        configFile = readYaml file: KUBERNETES_CONFIG_EKS_FILE
                        configFile.data.config = readFile(JSON_CONFIG_FILE)
                        writeYaml file: KUBERNETES_CONFIG_EKS_FILE, data: configFile, overwrite: true

                        deploymentFile = readYaml file: deploymentContextFile[params.application]
                        deploymentFile['spec']['template']['spec']['containers'][0]['image'] = "${ECR_IMAGE_REPO}:${IMAGE_TAG}"
                        writeYaml file: deploymentContextFile[params.application], data: deploymentFile, overwrite: true

                        try {
                            methods.commitAndPush("update edith context - deployment #${env.BUILD_NUMBER}")
                        }
                        catch (Exception e) {
                            sh 'echo "No changes found in the repository"'
                        }
                    }
                }
            }
        }
        stage('Deploy') {
            steps {
                script {
                    sh "argocd login ${variables.PLATFORM_ARGOCD_DOMAIN} --username ${variables.PLATFORM_ARGOCD_USERNAME} --password ${env.ARGOCD_PASSWORD} --grpc-web"
                    sh "argocd app sync edith-${AWS_REGION} --grpc-web"
                    currentBuild.description = 'Syncing deployment'
                    notificationParams['Tag'] = IMAGE_TAG
                    sh "argocd app wait edith-${AWS_REGION} --grpc-web --timeout 1200"
                    sh "argocd logout ${variables.PLATFORM_ARGOCD_DOMAIN}"
                }
            }
        }
    }
    post {
        always {
            script {
                cleanWs()
            }
        }
        success {
            script {
                currentBuild.description = 'Deployment successful'
                notifier.sendOpsRelatedNotifcationToChannel('SUCCESS', variables.EDITH_DEPLOYMENT_CHANNEL)
            }
        }
        failure {
            script {
                currentBuild.description = 'Deployment failed'
                notifier.sendOpsRelatedNotifcationToChannel('FAILED', variables.EDITH_DEPLOYMENT_CHANNEL)
            }
        }
        aborted {
            script {
                currentBuild.description = 'Deployment aborted'
                notifier.sendOpsRelatedNotifcationToChannel('ABORTED', variables.EDITH_DEPLOYMENT_CHANNEL)
            }
        }
    }
}

