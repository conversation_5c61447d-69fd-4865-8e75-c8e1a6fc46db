def setNotificationParams() {
    wrap([$class: 'BuildUser']) {
        notificationParams = [
            'pipeline': 'Edith config update',
            'Region': AWS_REGION,
            'Started By': env.BUILD_USER,
            'Layer': params.application,
            'channel': variables.EDITH_DEPLOYMENT_CHANNEL
        ]   
    }
}

properties([
    parameters([
        choice(name: 'application', choices: ['api-server', 'worker'], description: 'Select your application to update config.'),
        choice(name: 'region', choices: ['us-east-1'], description: 'Select the region to deploy the application.')
    ])
])

pipeline {
    agent {
        label 'JenkinsWorkerFleet'
    }

    environment {
        ARGOCD_PASSWORD = credentials('PlatformArgoCD')
    }

    stages {
        stage('Initialize') {
            steps {
                script {
                    variables         = load 'Helpers/variables.groovy'
                    notifier          = load 'Helpers/notifications.groovy'
                    methods           = load 'Helpers/methods.groovy'
                    
                    AWS_REGION        = params.region

                    setNotificationParams()
                    notifier.sendOpsRelatedNotifcationToChannel("STARTED", variables.EDITH_DEPLOYMENT_CHANNEL)
                }
            }
        }
        stage('Clone Repositories') {
            steps {
                script {
                    dir(variables.EKS_REPO_FOLDER) {
                        methods.cloneRepoWithGit(variables.KUBERNETES_YAML_REPO, "master")
                    }
                    dir(variables.CONFIG_REPO_FOLDER) {
                        methods.cloneConfig()
                    }
                }
            }
        }
        stage('Update K8s Contexts') {
            steps {
                script {
                    dir(variables.EKS_REPO_FOLDER) {
                        KUBERNETES_CONFIG_EKS_FILE = "edith/03-edith-configmap.yaml"

                        CONFIG_REPO_PATH = "${env.WORKSPACE}/${variables.CONFIG_REPO_FOLDER}"
                        JSON_CONFIG_FILE = "${CONFIG_REPO_PATH}/Platform/edith/${AWS_REGION}/config.json"

                        configFile = readYaml file: KUBERNETES_CONFIG_EKS_FILE
                        configFile.data.config = readFile(JSON_CONFIG_FILE)
                        writeYaml file: KUBERNETES_CONFIG_EKS_FILE, data: configFile, overwrite: true

                        try {
                            methods.commitAndPush("Update edith configmap context in EKS ConfigReload #${env.BUILD_NUMBER}")
                        }
                        catch (Exception e) {
                            sh 'echo "No changes found in the repository"'
                        }
                    }
                }
            }
        }
        stage('Deploy') {
            steps {
                script {
                    sh "argocd login ${variables.PLATFORM_ARGOCD_DOMAIN} --username ${variables.PLATFORM_ARGOCD_USERNAME} --password ${env.ARGOCD_PASSWORD}"
                    sh "argocd app sync edith-${AWS_REGION} --resource :ConfigMap:edith-configmap --grpc-web "
                    currentBuild.description = 'Syncing deployment'
                    sh "argocd app wait edith-${AWS_REGION} --grpc-web --timeout 120"
                    sh "argocd app actions run argocd/edith-${AWS_REGION} restart --kind Deployment --resource-name edith-${params.application}-deployment --grpc-web"
                    sh "argocd app wait edith-${AWS_REGION} --grpc-web --timeout 720"
                    sh "argocd logout ${variables.PLATFORM_ARGOCD_DOMAIN}"
                }
            }
        }
    }
    post {
        always {
            script {
                cleanWs()
            }
        }
        success {
            script {
                currentBuild.description = 'Deployment successful'
                notifier.sendOpsRelatedNotifcationToChannel('SUCCESS', variables.EDITH_DEPLOYMENT_CHANNEL)
            }
        }
        failure {
            script {
                currentBuild.description = 'Deployment failed'
                notifier.sendOpsRelatedNotifcationToChannel('FAILED', variables.EDITH_DEPLOYMENT_CHANNEL)
            }
        }
        aborted {
            script {
                currentBuild.description = 'Deployment aborted'
                notifier.sendOpsRelatedNotifcationToChannel('ABORTED', variables.EDITH_DEPLOYMENT_CHANNEL)
            }
        }
    }
}
