# EKS Blue Green Deployment Pipeline

A pipeline to deploy to multiple regions in EKS Directly to Green Environment.

## Input to Pipeline
  - ReleaseBranch - The release branch for notification and tracking purposes.
  - DeploymentTag - The tag that was created by Orchestrator.
  - BranchName - The branch that should be deployed.
  - CompileAssets - If we should do webpack build.

## Pipeline Steps
  - DEPLOYMENT PREP
    - Fetch the asset version from parameter store if compile assets is false, else generate new.
    - Fetch the Green Deployments, ConfigMaps, Services from Parameter Store

  - CHECK MIGRATIONS
    - Migrations are automated here, Spawn an instance in that Specific Region and run the specified command to check if PRE and POST Migration Exists

  - Build
    - WEBPACK BUILD
      - If compile assets is true, run webpack build
    - RUNNING_PRE_MIGRATIONS
      - Run Migrations which are marked as PRE
    - DOCKER IMAGE BUILD
      - Build Docker image and push to ECR
  
  - VERIFY WEBPACK BUILD
    - Verify if the webpack build was proper by doing curl to crucial bundles
  
  - UPDATE_CONFIG
    - From the Green ConfigMap fetched from Parameter Store, update the ConfigMap of the Green Deployment using the ConfigUpdate Python Script.

  - DEPLOY TO GREEN
    - From the Green Deployment fetched in DEPLOYMENT_PREP, image of the Green deployment directly.
    - Note that services are not updated. Since Green is live and recieving traffic, we cannot afford service updates which might cause some minor downtime.

  - RUNNING POST MIGRATIONS
    - Once the Deployment is Done, spawn an instance in that specific region and run the specified command to run the POST migrations

  - UPDATE PARAMETER STORE
    - Once the deployment is successful, update the parameter store with the new values.