def CLUSTER_NAME_JOBNAME_MAP = [
  'us-east-1': 'ss-production-us',
  'eu-central-1': 'ss-production-eu',
  'ap-south-1': 'ss-production-ap',
  'me-central-1': 'ss-production-me',
  'eu-west-2': 'ss-production-eu-ln',
  'ca-central-1': 'ss-production-ca',
  'ap-southeast-2': 'ss-production-ap-sy',
  'me-central2-b': 'ss-production-me-ksa'
]

def JOBNAME_REGION_MAP = [
  'App-v1/GreenDeployments/US-Green': 'us-east-1',
  'App-v1/GreenDeployments/EU-Green': 'eu-central-1',
  'App-v1/GreenDeployments/AP-Green': 'ap-south-1',
  'App-v1/GreenDeployments/ME-Green': 'me-central-1',
  'App-v1/GreenDeployments/UK-Green': 'eu-west-2',
  'App-v1/GreenDeployments/SY-Green': 'ap-southeast-2',
  'App-v1/GreenDeployments/CA-Green': 'ca-central-1',
  'App-v1/GreenDeployments/KSA-Green': 'me-central2-b'
]

STAGE_COUNT   = 8
CURRENT_STAGE = 0

pipeline {
  options {
    parallelsAlwaysFailFast()
  }

  agent {
    label "DockerBuilderFleet"
  }

  tools {
    nodejs "Node-v14.18.2"
  }

  parameters {
    string (
      description: "Release branch that was merged.",
      name: 'releasebranch'
    )

    string (
      description: "Deployment Tag to be deployed.",
      name: "deploymenttag"
    )

    booleanParam (
      name: 'compileassets',
      defaultValue: true,
      description: 'If compile assets ?'
    )
  }

  environment {
    JOB_NAME = "${JOBNAME_REGION_MAP[env.JOB_NAME]}"
    CLUSTER_NAME = "${CLUSTER_NAME_JOBNAME_MAP[env.JOB_NAME]}"
    SLACK_TOKEN = credentials('SlackBotToken')
  }

  stages {
    stage('DEPLOYMENT_PREP') {
      steps {
        script {
          variables = load 'Helpers/variables.groovy'
          methods   = load 'Helpers/methods.groovy'
          notifier   = load 'Helpers/notifications.groovy'
          
          postMigrationExists = true
          methods.prepInstance()
          actualJobName               = variables.GREEN_JOB_NAME_REGION_MAP.find { it.value == env.JOB_NAME }?.key
          lastSuccessfulBuildDuration = methods.getLastSuccessfulBuildDuration(actualJobName)
          message_timestamp           = notifier.sendDeploymentProgressNotification(CURRENT_STAGE, lastSuccessfulBuildDuration - currentBuild.duration, null, 'DEPLOYMENT_STARTED')
          notifier.sendDeploymentNotification('DEPLOYMENT_STARTED')

          dir('app-v1') {
            methods.cloneApp()
            greenDeploymentParams = methods.getDeploymentParameters('green', env.JOB_NAME)
            version               = params.compileassets ? methods.getAssetVersionFromRepo() : greenDeploymentParams.version
            packageJsonHash       = methods.checkToInstallNodeModules()
          }

          methods.authenticateToK8sCluster(env.JOB_NAME, variables.CLUSTER_NAME_REGION_MAP[env.JOB_NAME])

          currentBuild.displayName = "#${params.deploymenttag}"
          currentBuild.description = "Green - ${greenDeploymentParams.deployment} | ${params.releasebranch}"
          echo "\n\n=====================================================\n\n"
          echo "Green Deployment - ${greenDeploymentParams.deployment}\nGreen ConfigMap - ${greenDeploymentParams.configmap}\nGreen App - ${greenDeploymentParams.app}\nGreen Service - ${greenDeploymentParams.service}\nGreen Version - ${greenDeploymentParams.version}\nGreen Image = ${greenDeploymentParams.image}\n"
          echo "Image to be deployed - ${params.deploymenttag}\n"
          echo "\n\n=================================================\n\n"
          notifier.sendDeploymentProgressNotification(methods.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
        }
      }
    }

    stage ('BUILD') {
      steps {
        script {
          notifier.sendDeploymentProgressNotification(methods.calculatePercentage(CURRENT_STAGE, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
          methods.buildAssetsAndDockerImage()
          notifier.sendDeploymentProgressNotification(methods.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
        }
      }

      post {
        failure {
          script {
            notifier.sendDeploymentNotification('BUILD_FAILED')
          }
        }
      }
    }

    stage ('VERIFY_WEBPACK_BUILD') {
      steps {
        script {
          notifier.sendDeploymentProgressNotification(methods.calculatePercentage(CURRENT_STAGE, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
          methods.verifyWebpackBuild(version)
          notifier.sendDeploymentProgressNotification(methods.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
        }
      }

      post {
        failure {
          script {
            notifier.sendDeploymentNotification('WEBPACK_BUILD_FAILED')
          }
        }
      }
    }

    stage('RUNNING_PRE_MIGRATIONS') {
      steps {
        script {
          notifier.sendDeploymentProgressNotification(methods.calculatePercentage(CURRENT_STAGE, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
          notifier.sendDeploymentNotification('PRE_MIGRATION_STARTED')
          input (message: "Approve to start the migration.")

          migrationStatus = methods.runMigrations('PRE')
          preMigrationStatus = migrationStatus['migrationStatus']
          postMigrationExists = migrationStatus['postMigrationsCheck']
          if (preMigrationStatus == 0) {
            notifier.sendDeploymentProgressNotification(methods.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
            notifier.sendDeploymentNotification('PRE_MIGRATION_SUCCESSFUL')
            echo "Migrations ran successfully"
            sh "aws ssm put-parameter --region ${variables.DEFAULT_REGION} --name '/${env.JOB_NAME}/premigration/status' --value 0 --type SecureString --overwrite"
          } else {
            notifier.sendDeploymentNotification('PRE_MIGRATION_FAILED')
            error "[-] Pre-Migration Failed"
          }
        }
      }
    }

    stage ('UPDATE_CONFIG') {
      steps {
        script {
          notifier.sendDeploymentProgressNotification(methods.calculatePercentage(CURRENT_STAGE, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)

          methods.updateAppConfig(greenDeploymentParams.deployment)
          notifier.sendDeploymentProgressNotification(methods.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
        }
      }
    }

    stage ('DEPLOY_TO_GREEN') {
      steps {
        script {
          notifier.sendDeploymentNotification('DEPLOYMENT_TO_GREEN_STARTED')
          input (message: "Approve to start the deployment.")
          notifier.sendDeploymentProgressNotification(methods.calculatePercentage(CURRENT_STAGE, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
          methods.deployToGreen(greenDeploymentParams.deployment)
          notifier.sendDeploymentProgressNotification(methods.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
        }
      }

      post {
        failure {
          script {
            notifier.sendDeploymentNotification('DEPLOYMENT_TO_BLUE_FAILED')
            methods.handleDeploymentFailure('DEPLOY_TO_GREEN')
          }
        }
      }
    }

    stage ('UPDATE_PARAMETER_STORE') {
      steps {
        script {
          notifier.sendDeploymentProgressNotification(methods.calculatePercentage(CURRENT_STAGE, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
          deploymentParams = [
            'deployment': greenDeploymentParams.deployment,
            'configmap': greenDeploymentParams.configmap,
            'app': greenDeploymentParams.app,
            'version': version,
            'image': params.deploymenttag,
            'service': greenDeploymentParams.service,
          ]

          echo "\n ------------------------------------ Switching Values in Parameter Store -------------------------------------------------- \n"
          methods.updateDeploymentParameters('green', deploymentParams)
          echo "\n ------------------------------------ Switched Values in Parameter Store -------------------------------------------------- \n"
          notifier.sendDeploymentProgressNotification(methods.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
        }
      }
    }

    stage('RUNNING_POST_MIGRATIONS') {
      when {
        expression {
          return postMigrationExists
        }
      }

      steps {
        script {
          notifier.sendDeploymentProgressNotification(methods.calculatePercentage(CURRENT_STAGE, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
          notifier.sendDeploymentNotification('POST_MIGRATION_STARTED')
          input (message: "Approve to start running post migrations.")
          migrationStatus = methods.runMigrations('POST')
          postMigrationStatus = migrationStatus['migrationStatus']
          if (postMigrationStatus == 0) {
            notifier.sendDeploymentProgressNotification(methods.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
            notifier.sendDeploymentNotification('POST_MIGRATION_SUCCESSFUL')
            echo "Migrations ran successfully"
          } else {
            notifier.sendDeploymentNotification('POST_MIGRATION_FAILED')
            error "[-] Post Migration Failed"
          }
        }
      }
    }
  }

  post {
    always {
      script {
        step([$class: 'NewRelicDeploymentNotifier',
          notifications: [
            [
              apiKey: "NewrelicNotifier",
              applicationId: '',
              european: false,
              entityGuid: "${variables.REGION_ENTITY_GUID_MAP[env.JOB_NAME]}",
              changelog: "",
              commit: "${params.deploymenttag}",
              deeplink: "${env.BUILD_URL}",
              deploymentType: "ROLLING",
              description: "Deployment Tag: ${params.deploymenttag}",
              groupId: "${env.JOB_NAME}",
              user: "${env.BUILD_USER}",
              version: "${params.deploymenttag}",
              timestamp: ''
            ]
          ]
        ])
      }
    }

    success {
      script {
        methods.runAutomationSanity()
        notifier.sendDeploymentProgressNotification(100, currentBuild.duration, message_timestamp, 'DEPLOYMENT_SUCCESSFUL')
        notifier.sendDeploymentNotification('DEPLOYMENT_SUCCESSFUL')
        cleanWs()
      }
    }

    failure {
      script {
        notifier.sendDeploymentProgressNotification(methods.calculatePercentage(CURRENT_STAGE, STAGE_COUNT), currentBuild.duration, message_timestamp, 'DEPLOYMENT_FAILED')
        notifier.sendDeploymentNotification('DEPLOYMENT_FAILED')
        cleanWs()
      }
    }

    aborted {
      script {
        notifier.sendDeploymentProgressNotification(methods.calculatePercentage(CURRENT_STAGE, STAGE_COUNT), currentBuild.duration, message_timestamp, 'DEPLOYMENT_ABORTED')
        notifier.sendDeploymentNotification('DEPLOYMENT_ABORTED')
        cleanWs()
      }
    }
  }
}
