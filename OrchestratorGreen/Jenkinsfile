def mergesubmodules(submodule, branch) {
  if (sh (script: "cd ${submodule}; git ls-remote | grep ${branch}", returnStatus: true) == 0) {
    echo "${branch} exists"
    sh """
    cd ${submodule}
    git config user.email ${variables.GIT_EMAIL}
    git config user.name ${variables.GIT_USERNAME}
    git remote update
    git fetch --all
    git fetch origin ${branch}
    git checkout --track origin/${branch}
    git pull origin ${branch}
    git fetch origin ${variables.PRODUCTION_RELEASE_BRANCH}
    git checkout ${variables.PRODUCTION_RELEASE_BRANCH}
    git pull origin ${variables.PRODUCTION_RELEASE_BRANCH}
    git merge ${branch}
    git push
    git fetch origin ${variables.PRODUCTION_BRANCH}
    git checkout ${variables.PRODUCTION_BRANCH}
    git pull origin ${variables.PRODUCTION_BRANCH}
    git merge ${variables.PRODUCTION_RELEASE_BRANCH}
    git push origin ${variables.PRODUCTION_BRANCH}
    cd ..
    """
  } else {
    echo "${branch} doesn't exist in ${submodule}"
  }
}

def SUBMODULES = ['surveysparrow-analyze', 'surveysparrow-billing', 'surveysparrow-integrations', 'surveysparrow-reputation-management', 'surveysparrow-ticket-management', 'surveysparrow-external-events', 'platform-services', 'surveysparrow-db-models', 'surveysparrow-operations']

def JOB_NAMES = [
  'US_EKS': 'App-v1/GreenDeployments/US-Green',
  'EU_EKS': 'App-v1/GreenDeployments/EU-Green',
  'AP_EKS': 'App-v1/GreenDeployments/AP-Green',
  'ME_EKS': 'App-v1/GreenDeployments/ME-Green',
  'UK_EKS': 'App-v1/GreenDeployments/UK-Green'
]

pipeline {
  agent {
    label "DockerBuilderFleet"
  }

  environment {
    PROJECT_KEY = "SSE"
    JIRA_TOKEN = credentials('JiraAuthToken')
    APP_REPO_URL = '*****************:surveysparrow/app-v1.git'
    BB_CREDENTIAL_ID = 'BitBucketPrivateKey'
    PRODUCTION_RELEASE_BRANCH = 'production'
    PRODUCTION_BRANCH = 'master'
  }

  parameters {
    string (
      name: 'branchname',
      description: "Release branch to deploy"
    )
    
    booleanParam (
      name: 'compileassets',
      defaultValue: true,
      description: 'If compile assets ?'
    )
  }

  stages {
    stage('CREATE_DEPLOYMENT_TAG') {
      steps {
        script {
          notifier   = load 'Helpers/notifications.groovy'
          variables = load 'Helpers/variables.groovy'
          methods   = load 'Helpers/methods.groovy'

          methods.prepInstance()

          currentBuild.displayName = "#${params.branchname}"
          notifier.sendDeploymentNotification('DEPLOYMENT_TRIGGERED')

          issuekeys = sh(returnStdout: true, script: "RELEASE_BRANCH=${params.branchname} JIRA_TOKEN=${JIRA_TOKEN} PROJECT_KEY=${PROJECT_KEY} python3 Helpers/jirahelper.py -k").trim()
          issueKeysArr = "${issuekeys.replace('[','')}"
          issueKeysArr = "${issueKeysArr.replace(']', '')}"
          issueList = []
          for(issue in issueKeysArr.split(',')) {
            issueList.add(issue.replace("'", '').trim())
          }
        }

        echo "[+] Merging ${params.branchname} to ${variables.PRODUCTION_RELEASE_BRANCH} in app-v1 and creating a deployment tag."
        dir('app-v1') {
          git branch: "${params.branchname}", credentialsId: "${variables.BB_CREDENTIAL_ID}", url: "${variables.APP_REPO}"
          sshagent(["${variables.BB_CREDENTIAL_ID}"]) {
            script {
              sh "git submodule update --init --recursive"

              for (submodule in SUBMODULES) {
                mergesubmodules(submodule, params.branchname)
              }

              methods.setGitConfig()
              if (sh (script: "git status | grep nothing", returnStatus: true) == 0) {
                echo "[+] Nothing changed no commits to be made"
              } else {
                sh """
                  git add .
                  git commit -m "update submodule references"
                """
              }

              methods.setGitConfig()
              sh """
              git push origin ${params.branchname}
              git fetch --all
              git fetch origin ${variables.PRODUCTION_RELEASE_BRANCH}
              git checkout --track origin/${variables.PRODUCTION_RELEASE_BRANCH}
              git pull origin ${variables.PRODUCTION_RELEASE_BRANCH}
              git merge ${params.branchname}
              git push origin ${variables.PRODUCTION_RELEASE_BRANCH}
              git fetch origin ${variables.PRODUCTION_BRANCH}
              git checkout ${variables.PRODUCTION_BRANCH}
              git pull origin ${variables.PRODUCTION_BRANCH}
              git merge ${variables.PRODUCTION_RELEASE_BRANCH}
              git push origin ${variables.PRODUCTION_BRANCH}
              """

              GIT_TAG = "production-${new Date().format('dd-MM-yyyy')}"
              GIT_TAG_COUNT = sh(script: "git tag --list '${GIT_TAG}-*' | wc -l | tr -d '[:space:]'", returnStdout: true).trim().toInteger() + 1
              GIT_TAG_FULL = "${GIT_TAG}-${GIT_TAG_COUNT}"
              sh """
              git tag ${GIT_TAG_FULL}
              git push origin ${GIT_TAG_FULL}
              """
              notifier.sendDeploymentNotification('TAG_CREATED')
              notifier.sendDeploymentNotification('DEPLOYMENT_STARTED_ALL_DCS')
            }
          }
        }
      }
    }

    stage('DEPLOYMENT') {
      parallel {
        stage ('DEPLOYMENT_TO_US') {
          steps {
            catchError(buildResult: 'SUCCESS', message: 'Stage Failed', stageResult: 'FAILURE') {
              dir('app-v1') {
                build job: JOB_NAMES['US_EKS'], parameters: [ string(name: 'releasebranch', value: "${params.branchname}"), string(name: 'deploymenttag', value: "${GIT_TAG_FULL}"), string(name: 'branchname', value: "${variables.PRODUCTION_BRANCH}"), booleanParam(name: 'compileassets', value: params.compileassets)]
              }
            }
          }

          post {
            always {
              jiraSendDeploymentInfo environmentId: 'us-east-1', environmentName: 'us-east-1', environmentType: 'production', issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'successful'
            }
          }
        }

        stage ('DEPLOYMENT_TO_EU') {
          steps {
            catchError(buildResult: 'SUCCESS', message: 'Stage Failed', stageResult: 'FAILURE') {
              dir('app-v1') {
                build job: JOB_NAMES['EU_EKS'], parameters: [ string(name: 'releasebranch', value: "${params.branchname}"), string(name: 'deploymenttag', value: "${GIT_TAG_FULL}"), string(name: 'branchname', value: "${variables.PRODUCTION_BRANCH}"), booleanParam(name: 'compileassets', value: params.compileassets)]
              }
            }
          }

          post {
            always {
              jiraSendDeploymentInfo environmentId: 'eu-central-1', environmentName: 'eu-central-1', environmentType: 'production', issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'successful'
            }
          }
        }

        stage('DEPLOYMENT_TO_AP') {
          steps {
            catchError(buildResult: 'SUCCESS', message: 'Stage Failed', stageResult: 'FAILURE') {
              dir ('app-v1') {
                jiraSendDeploymentInfo environmentId: 'ap-south-1', environmentName: 'ap-south-1', environmentType: 'production', issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'in_progress'
                build job: JOB_NAMES['AP_EKS'], parameters: [ string(name: 'releasebranch', value: "${params.branchname}"), string(name: 'deploymenttag', value: "${GIT_TAG_FULL}"), string(name: 'branchname', value: "${variables.PRODUCTION_BRANCH}"), booleanParam(name: 'compileassets', value: params.compileassets)]
              }
            }
          }

          post {
            always {
              jiraSendDeploymentInfo environmentId: 'ap-south-1', environmentName: 'ap-south-1', environmentType: 'production', issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'successful'
            }
          }
        }

        stage('DEPLOYMENT_TO_ME') {
          steps {
            catchError(buildResult: 'SUCCESS', message: 'Stage Failed', stageResult: 'FAILURE') {
              dir ('app-v1') {
                jiraSendDeploymentInfo environmentId: 'me-central-1', environmentName: 'me-central-1', environmentType: 'production', issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'in_progress'
                build job: JOB_NAMES['ME_EKS'], parameters: [ string(name: 'releasebranch', value: "${params.branchname}"), string(name: 'deploymenttag', value: "${GIT_TAG_FULL}"), string(name: 'branchname', value: "${variables.PRODUCTION_BRANCH}"), booleanParam(name: 'compileassets', value: params.compileassets)]
              }
            }
          }

          post {
            always {
              jiraSendDeploymentInfo environmentId: 'me-central-1', environmentName: 'me-central-1', environmentType: 'production', issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'successful'
            }
          }
        }

        stage('DEPLOYMENT_TO_UK') {
          steps {
            catchError(buildResult: 'SUCCESS', message: 'Stage Failed', stageResult: 'FAILURE') {
              dir ('app-v1') {
                jiraSendDeploymentInfo environmentId: 'eu-west-2', environmentName: 'eu-west-2', environmentType: 'production', issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'in_progress'
                build job: JOB_NAMES['UK_EKS'], parameters: [ string(name: 'releasebranch', value: "${params.branchname}"), string(name: 'deploymenttag', value: "${GIT_TAG_FULL}"), string(name: 'branchname', value: "${variables.PRODUCTION_BRANCH}"), booleanParam(name: 'compileassets', value: params.compileassets)]
              }
            }
          }

          post {
            always {
              jiraSendDeploymentInfo environmentId: 'eu-west-2', environmentName: 'eu-west-2', environmentType: 'production', issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'successful'
            }
          }
        }
      }
    }
  }

  post {
    always {
      script {
        currentBuild.displayName = "#${GIT_TAG_FULL} - ${params.branchname}"
        currentBuild.description = "${issueList}"

        input(message: "Post message to ${params.branchname} ?")
        notifier.sendDeploymentNotification('DEPLOYMENT_SUCCESSFUL_ALL_DCS')

        input(message: 'Post message to #deployment ?')
        sh "RELEASE_BRANCH=${branchname} DEPLOYMENT_TAG=${GIT_TAG_FULL} JIRA_TOKEN=${JIRA_TOKEN} PROJECT_KEY=${PROJECT_KEY} python3 Helpers/jirahelper.py -p"
        sh "RELEASE_BRANCH=${branchname} DEPLOYMENT_TAG=${GIT_TAG_FULL} JIRA_TOKEN=${JIRA_TOKEN} PROJECT_KEY=${PROJECT_KEY} python3 Helpers/jirahelper.py -r"

        methods.prepInstance()
        greenDeploymentParams = methods.getDeploymentParameters('green', 'us-east-1')
        methods.updateLambdaEnvVariables(greenDeploymentParams.version)

        cleanWs()
      }
    }
  }
}