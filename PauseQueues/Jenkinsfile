import groovy.json.JsonOutput
pipeline {
  agent {
    label 'JenkinsWorkerFleet'
  }

  environment {
    BB_TOKEN = credentials('BitBucketOAuthToken')
    BB_CREDENTIAL_ID = 'BitBucketPrivateKey'
  }

  parameters {
    choice (
      choices: [ 'us-east-1', 'eu-central-1', 'eu-west-2', 'ap-south-1', 'me-central-1' ],
      name: 'region',
      description: 'Specify which region to pause the queues'
    )

    string (
      name: 'json',
      defaultValue: '{}',
      description: 'Specify the json payload to pause the queues'
    )

    string (
      name: 'reason',
      defaultValue: 'No reason provided',
      description: 'Specify the reason for pausing the queues'
    )
  }

  stages {
    stage ('VALIDATE_JSON') {
      steps {
        script {
          variables = load 'Helpers/variables.groovy'
          methods   = load 'Helpers/methods.groovy'
          notifier   = load 'Helpers/notifications.groovy'

          methods.prepInstance()
          notifier.sendSlackNotification('PAUSE_QUEUES_STARTED')
          pauseQueueJsonObj = readJSON text: params.json
          pauseQueueJson    = writeJSON json: pauseQueueJsonObj, returnText: true

          echo "[+] Validating JSON"
          isValidJson = sh(script: "python3 Helpers/jsonvalidator.py -j ${JsonOutput.toJson(pauseQueueJson)}", returnStatus: true)

          if (isValidJson == 0) {
            echo "[+] JSON is valid"
          } else {
            error "[!] JSON - ${pauseQueueJson} is invalid"
          }
        }
      }
    }

    stage ('UPDATE_CONFIG_CREATE_PR') {
      steps {
        script {
          dir ('surveysparrow-production-config') {
            methods.cloneConfig()
            uniqueIdentifier = new Date().getTime()
            appConfigJson = readJSON file: "${params.region}.json"
            appConfigJson['pauseQueues'] = [ "uniqueIdentifier": uniqueIdentifier ]
            appConfigJson['pauseQueues']['jobCondition'] = pauseQueueJsonObj

            writeJSON json: appConfigJson, file: "${params.region}.json"
            sh "cat ${params.region}.json | jq . > temp.json"
            sh "mv temp.json ${params.region}.json"

            sshagent(["$BB_CREDENTIAL_ID"]) {
              branchName = "${params.region}-${new Date().format('yyyyMMddHHmm')}-pauseQueues"
              sh "git config --global user.email <EMAIL>"
              sh "git config --global user.name saasdevops"
              sh "git checkout -b ${branchName}"
              sh "git add ${params.region}.json"
              sh "git commit -m 'Updated ${params.region}.json with pauseQueues'"
              sh "git push origin ${branchName}"
            }
          }

          sh "BB_TOKEN=$BB_TOKEN python3 Helpers/prcreator.py -b ${branchName} -r surveysparrow-production-config -t 'PauseQueues-${params.region} ${new Date().format('yyyyMMdd')}'"
        }
      }
    }

    stage ('DYNAMODB_UPDATE') {
      steps {
        script {
          wrap([$class: 'BuildUser']) {
            sh "aws dynamodb put-item --table-name ${variables.PAUSE_QUEUES_DYNAMODB_TABLE} --item '{\"region\":{\"S\":\"${params.region}\"},\"unique_id\":{\"S\":\"${uniqueIdentifier}\"},\"jobCondition\":{\"S\":${JsonOutput.toJson(pauseQueueJson)},\"createdBy\":{\"S\":\"${env.BUILD_USER}\"},\"createdDate\":{\"S\":\"${new Date()}\"}}' --region ${variables.DEFAULT_REGION}"
          }
        }
      }
    }
  }

  post {
    always {
      cleanWs()
    }

    success {
      script {
        notifier.sendSlackNotification('PAUSE_QUEUES_SUCCESSFUL')
      }
    }

    failure {
      script {
        notifier.sendSlackNotification('PAUSE_QUEUES_FAILED')
      }
    }

    aborted {
      script {
        notifier.sendSlackNotification('PAUSE_QUEUES_ABORTED')
      }
    }
  }
}