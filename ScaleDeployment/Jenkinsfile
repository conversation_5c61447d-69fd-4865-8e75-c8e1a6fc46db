pipeline {
  agent { label "JenkinsWorkerFleet" }

  parameters {
    choice (
      choices: [ 'ap-south-1',  'eu-central-1', 'eu-west-2', 'me-central-1', 'us-east-1', 'me-central2-b' ],
      name: 'region',
      description: 'Specify which region to run restart'
    )

    choice (
      choices: ['api', 'application', 'eui', 'hulk-worker', 'reports', 'generic-worker', 'cron-worker', 'notification-worker', 'haproxy'],
      name: 'layer',
      description: 'Specify which layer to run restart'
    )
    
    string (
      name: 'minreplicas',
      defaultValue: '2',
      description: 'Specify the minimum number of replicas'
    )

    string (
      name: 'maxreplicas',
      defaultValue: '10',
      description: 'Specify the maximum number of replicas'
    )

    string (
      name: 'reason',
      description: 'Specify the reason to update the Scaling Configuration'
    )
  }

  stages {
    stage('Updating HPA') {

      steps {
        script {
          notifier   = load 'Helpers/notifications.groovy'
          variables = load 'Helpers/variables.groovy'
          methods   = load 'Helpers/methods.groovy'

          methods.prepInstance()
          notifier.sendSlackNotification('SCALING_CONFIG_UPDATE_STARTED')

          clusterName = variables.CLUSTER_NAME_REGION_MAP[params.region]
          methods.authenticateToK8sCluster(params.region, clusterName)

          if (params.layer != 'haproxy') {
            blueDeploymentParams  = methods.getDeploymentParameters('blue', params.region)
            greenDeploymentParams = methods.getDeploymentParameters('green', params.region)

            appName     = variables.REGION_APP_MAP[params.region]

            blueHpa  = "${appName}-${params.layer}-${blueDeploymentParams.deployment}-hpa"
            greenHpa = "${appName}-${params.layer}-${greenDeploymentParams.deployment}-hpa"

            blueHPAExists  = methods.checkIfHPAExists(blueHpa, variables.DEFAULT_NAMESPACE)
            greenHPAExists = methods.checkIfHPAExists(greenHpa, variables.DEFAULT_NAMESPACE) 

            if (blueHPAExists && greenHPAExists) {
              sh "kubectl patch hpa ${blueHpa} -p '{\"spec\":{\"minReplicas\":${params.minreplicas},\"maxReplicas\":${params.maxreplicas}}}' -n ${variables.DEFAULT_NAMESPACE}"
              sh "kubectl patch hpa ${greenHpa} -p '{\"spec\":{\"minReplicas\":${params.minreplicas},\"maxReplicas\":${params.maxreplicas}}}' -n ${variables.DEFAULT_NAMESPACE}"
            } else {
              echo "[-] HPA - ${blueHpa}, ${greenHpa} Doesn't Exist"
            }
          } else {
            haproxyHpa = "${params.layer}-forwarder-hpa"

            haproxyHPAExists = methods.checkIfHPAExists(haproxyHpa, variables.DEFAULT_NAMESPACE)

            if (haproxyHPAExists) {
              sh "kubectl patch hpa ${haproxyHpa} -p '{\"spec\":{\"minReplicas\":${params.minreplicas},\"maxReplicas\":${params.maxreplicas}}}' -n ${variables.DEFAULT_NAMESPACE}"
            } else {
              echo "[-] HPA - ${haproxyHpa} Doesn't Exist"
            }
          }
        }
      }
    }
  }

  post {
    always {
      cleanWs()
    }

    success {
      script {
        notifier.sendSlackNotification('SCALING_CONFIG_UPDATE_SUCCESSFUL')
      }
    }

    failure {
      script {
        notifier.sendSlackNotification('SCALING_CONFIG_UPDATE_FAILED')
      }
    }

    aborted {
      script {
        notifier.sendSlackNotification('SCALING_CONFIG_UPDATE_ABORTED')
      }
    }
  }
}