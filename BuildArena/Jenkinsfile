color_region_map = [ 
  "us-east-1": "#B31942",
  "eu-central-1": "#003776",
  "ap-south-1": "#00C0F0",
  "me-central-1": "#4900AD"
]

def sendSlackNotification(message_type) {
  def message = ""
  def color = ""
  wrap([$class: 'BuildUser']) {
    switch (message_type) {
      case 'DEPLOYMENT_STARTED':
        color = color_region_map[params.region]
        message = ":ox: *Arena build started!*\n\nStarted by - `${env.BUILD_USER}`\nEnvironment - `${params.region}`"
        break
      case 'DEPLOYMENT_FAILED':
        color = "#E74C3C"
        message = ":bomb: *Arena build failed!*\n\nStarted by - `${env.BUILD_USER}`\nEnvironment - `${params.region}`\nLook into the issues here at ${env.BUILD_URL}console"
        break
      case 'DEPLOYMENT_SUCCESSFUL':
        color = "#2ECC71"
        message = ":pirate_flag: *Arena build completed successful!!*\n\nStarted by - `${env.BUILD_USER}`\nEnvironment - `${params.region}`"
        break
      case 'DEPLOYMENT_ABORTED':
        color = "#222258"
        message = ":dizzy_face: * Arena build aborted!*\n\nStarted by - `${env.BUILD_USER}`\nEnvironment - `${params.region}`"
      default:
        color = "#E74C3C"
        message = ":bomb: Status of the deployment to `${env.JOB_NAME}` initiated by `${env.BUILD_USER}` is unknown. :boom:"
        break
    }
  }

  slackSend (
    color: "${color}", 
    message: "${message}", 
    teamDomain: 'surveysparrow', 
    tokenCredentialId: 'SlackIntegrationToken'
  )
}

pipeline {
  agent { label "DockerBuilderFleet-${params.region}" }

  parameters {
    choice (
      name: 'region', 
      choices: [ 'ap-south-1', 'me-central-1' ],
      description: 'Name of the region to deploy arena'
    )
  }
  
  environment {
    ARENA_REPO = '*****************:surveysparrow/surveysparrow-arena.git'
    BB_CREDENTIAL_ID = 'BitBucketPrivateKey'
    BRANCH = 'move-queues-to-config'
  }

  stages {
    stage('Git Clone') {
      agent { label "DockerBuilderFleet-${params.region}" }

      steps {
        sendSlackNotification('DEPLOYMENT_STARTED')
        dir ('surveysparrow-arena') {
          git branch: "${env.BRANCH}", credentialsId: "${env.BB_CREDENTIAL_ID}", url: "${env.ARENA_REPO}"
        }
      }
    }

    stage ('Build') {
      agent { label "DockerBuilderFleet-${params.region}" }

      steps {
        sh "sudo service docker start"

        dir ('surveysparrow-arena') {
          sh "aws ecr get-login-password --region ${params.region} | docker login --username AWS --password-stdin 556123534406.dkr.ecr.${params.region}.amazonaws.com"
          sh "docker build --label 'env=${params.region}' -f Dockerfiles/production -t 556123534406.dkr.ecr.${params.region}.amazonaws.com/arena:${env.BUILD_NUMBER} ."
          sh "docker tag 556123534406.dkr.ecr.${params.region}.amazonaws.com/arena:${env.BUILD_NUMBER} 556123534406.dkr.ecr.${params.region}.amazonaws.com/arena"
          sh "docker push 556123534406.dkr.ecr.${params.region}.amazonaws.com/arena:${env.BUILD_NUMBER}"
          sh "docker push 556123534406.dkr.ecr.${params.region}.amazonaws.com/arena"
          sh "docker rmi \$(docker images --filter 'label=env=${params.region}' -q | uniq) --force"
        }
      }
    }
  }

  post {
    always {
      cleanWs()
    }

    success {
      sendSlackNotification('DEPLOYMENT_SUCCESSFUL')
    }

    failure {
      sendSlackNotification('DEPLOYMENT_FAILED')
    }

    aborted {
      sendSlackNotification('DEPLOYMENT_ABORTED')
    }
  }
}