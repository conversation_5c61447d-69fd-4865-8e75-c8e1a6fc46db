# BuildArena Pipeline

A pipeline to Build and Push Docker images for Arena to ECR
Pipeline to be used only when code changes are made to Arena, to update queue names, use ConfigUpdate Pipeline

## Input to Pipeline
  - Region - ap-south-1, me-central-1

## Pipeline Steps
  - Git Clone
    - Clone the surveysparrow-arena repo (restricted only to move-queues-to-config branch which will be changed to master when all DCs are moved to EKS)

  - Build
    - Build the Docker Image for Arena and Push it to ECR