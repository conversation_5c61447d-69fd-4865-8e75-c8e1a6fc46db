pipeline {
  agent {
    label "DockerBuilderFleet"
  }

  environment {
    JIRA_TOKEN = credentials('JiraAuthToken')
  }

  parameters {
    string (
      name: 'releasebranch',
      description: "Release branch to deploy"
    )
  }

  stages {
    stage('CREATE_DEPLOYMENT_TAG') {
      steps {
        script {
          variables = load 'Helpers/variables.groovy'
          notifier   = load 'Helpers/notifications.groovy'
          methods   = load 'Helpers/methods.groovy'

          env.JOB_NAME     = env.JOB_NAME.split('/')[1]
          microServiceVars = variables.SOC[env.JOB_NAME]['backend']

          methods.prepInstance()

          currentBuild.displayName = "#${params.releasebranch}"
          notifier.sendSlackNofiticationMicroservices('SOC_DEPLOYMENT_TRIGGERED')

          issuekeys = sh(returnStdout: true, script: "RELEASE_BRANCH=${params.releasebranch} JIRA_TOKEN=${JIRA_TOKEN} PROJECT_KEY=${variables.DEFAULT_JIRA_PROJECT_KEY} python3 Helpers/jirahelper.py -k").trim()
          issueKeysArr = "${issuekeys.replace('[','')}"
          issueKeysArr = "${issueKeysArr.replace(']', '')}"
          issueList = []
          for(issue in issueKeysArr.split(',')) {
            issueList.add(issue.replace("'", '').trim())
          }

          echo "[+] Merging ${params.releasebranch} to ${env.PRODUCTION_RELEASE_BRANCH} in ${microServiceVars.service} and creating a deployment tag."
          dir(microServiceVars.folderName) {
            methods.cloneRepoWithGit(microServiceVars.repoUrl, params.releasebranch)
            sshagent([variables.BB_CREDENTIAL_ID]) {
              sh "git submodule update --init --recursive"

              for (submodule in variables.SUBMODULES[microServiceVars.folderName]) {
                methods.mergeSubmodulesToProduction(submodule, params.releasebranch)
              }

              methods.setGitConfig()
              if (sh (script: "git status | grep nothing", returnStatus: true) == 0) {
                echo "[+] Nothing changed no commits to be made"
              } else {
                sh """
                  git add .
                  git commit -m "update submodule references"
                """
              }

              methods.setGitConfig()
              sh """
              git push origin ${params.releasebranch}
              git fetch --all
              git fetch origin ${variables.PRODUCTION_RELEASE_BRANCH}
              git checkout --track origin/${variables.PRODUCTION_RELEASE_BRANCH}
              git pull origin ${variables.PRODUCTION_RELEASE_BRANCH}
              git merge ${params.releasebranch}
              git push origin ${variables.PRODUCTION_RELEASE_BRANCH}
              git fetch origin ${variables.PRODUCTION_BRANCH}
              git checkout ${variables.PRODUCTION_BRANCH}
              git pull origin ${variables.PRODUCTION_BRANCH}
              git merge ${variables.PRODUCTION_RELEASE_BRANCH}
              git push origin ${variables.PRODUCTION_BRANCH}
              """

              GIT_TAG = "production-${new Date().format('dd-MM-yyyy')}"
              GIT_TAG_COUNT = sh(script: "git tag --list '${GIT_TAG}-*' | wc -l | tr -d '[:space:]'", returnStdout: true).trim().toInteger() + 1
              GIT_TAG_FULL = "${GIT_TAG}-${GIT_TAG_COUNT}"
              sh """
              git tag ${GIT_TAG_FULL}
              git push origin ${GIT_TAG_FULL}
              """
              notifier.sendSlackNofiticationMicroservices('SOC_TAG_CREATED')
              notifier.sendSlackNofiticationMicroservices('SOC_DEPLOYMENT_STARTED_ALL_DCS')
            }
          }
        }
      }
    }

    stage('DEPLOYMENT') {
      parallel {
        stage ('US') {
          steps {
            catchError(buildResult: 'SUCCESS', message: 'Stage Failed', stageResult: 'FAILURE') {
              dir(microServiceVars.folderName) {
                build job: variables.SOC_JOB_NAMES_MAP[env.JOB_NAME][env.STAGE_NAME], parameters: [ string(name: 'releasebranch', value: "${params.releasebranch}"), string(name: 'deploymenttag', value: "${GIT_TAG_FULL}")], propagate: false
              }
            }
          }

          post {
            always {
              jiraSendDeploymentInfo environmentId: env.STAGE_NAME, environmentName: env.STAGE_NAME, environmentType: 'production', issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'successful'
            }
          }
        }

        stage ('EU') {
          steps {
            catchError(buildResult: 'SUCCESS', message: 'Stage Failed', stageResult: 'FAILURE') {
              dir(microServiceVars.folderName) {
                build job: variables.SOC_JOB_NAMES_MAP[env.JOB_NAME][env.STAGE_NAME], parameters: [ string(name: 'releasebranch', value: "${params.releasebranch}"), string(name: 'deploymenttag', value: "${GIT_TAG_FULL}")], propagate: false
              }
            }
          }

          post {
            always {
              jiraSendDeploymentInfo environmentId: env.STAGE_NAME, environmentName: env.STAGE_NAME, environmentType: 'production', issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'successful'
            }
          }
        }

        stage ('AP') {
          when {
            expression { variables.SOC_JOB_NAMES_MAP[env.JOB_NAME][env.STAGE_NAME] != null }
          }

          steps {
            catchError(buildResult: 'SUCCESS', message: 'Stage Failed', stageResult: 'FAILURE') {
              dir(microServiceVars.folderName) {
                build job: variables.SOC_JOB_NAMES_MAP[env.JOB_NAME][env.STAGE_NAME], parameters: [ string(name: 'releasebranch', value: "${params.releasebranch}"), string(name: 'deploymenttag', value: "${GIT_TAG_FULL}")], propagate: false
              }
            }
          }

          post {
            always {
              jiraSendDeploymentInfo environmentId: env.STAGE_NAME, environmentName: env.STAGE_NAME, environmentType: 'production', issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'successful'
            }
          }
        }

        stage ('ME') {
          when {
            expression { variables.SOC_JOB_NAMES_MAP[env.JOB_NAME][env.STAGE_NAME] != null }
          }

          steps {
            catchError(buildResult: 'SUCCESS', message: 'Stage Failed', stageResult: 'FAILURE') {
              dir(microServiceVars.folderName) {
                build job: variables.SOC_JOB_NAMES_MAP[env.JOB_NAME][env.STAGE_NAME], parameters: [ string(name: 'releasebranch', value: "${params.releasebranch}"), string(name: 'deploymenttag', value: "${GIT_TAG_FULL}")], propagate: false
              }
            }
          }

          post {
            always {
              jiraSendDeploymentInfo environmentId: env.STAGE_NAME, environmentName: env.STAGE_NAME, environmentType: 'production', issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'successful'
            }
          }
        }

        stage ('UK') {
          when {
            expression { variables.SOC_JOB_NAMES_MAP[env.JOB_NAME][env.STAGE_NAME] != null }
          }

          steps {
            catchError(buildResult: 'SUCCESS', message: 'Stage Failed', stageResult: 'FAILURE') {
              dir(microServiceVars.folderName) {
                build job: variables.SOC_JOB_NAMES_MAP[env.JOB_NAME][env.STAGE_NAME], parameters: [ string(name: 'releasebranch', value: "${params.releasebranch}"), string(name: 'deploymenttag', value: "${GIT_TAG_FULL}")], propagate: false
              }
            }
          }

          post {
            always {
              jiraSendDeploymentInfo environmentId: env.STAGE_NAME, environmentName: env.STAGE_NAME, environmentType: 'production', issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'successful'
            }
          }
        }
      }
    }
  }

  post {
    always {
      script {
        currentBuild.displayName = "#${GIT_TAG_FULL} - ${params.releasebranch}"

        input(message: "Post message to ${params.releasebranch} ?")
        notifier.sendSlackNofiticationMicroservices('SOC_DEPLOYMENT_SUCCESSFUL_ALL_DCS')

        input(message: 'Post message to #deployment (Please proceed only if it is a standalone release, else abort.)')
        sh "RELEASE_BRANCH=${params.releasebranch} DEPLOYMENT_TAG=${GIT_TAG_FULL} JIRA_TOKEN=${JIRA_TOKEN} PROJECT_KEY=${variables.DEFAULT_JIRA_PROJECT_KEY} APPLICATION=${JOB_NAME} python3 Helpers/jirahelper.py -p"
        sh "RELEASE_BRANCH=${params.releasebranch} DEPLOYMENT_TAG=${GIT_TAG_FULL} JIRA_TOKEN=${JIRA_TOKEN} PROJECT_KEY=${variables.DEFAULT_JIRA_PROJECT_KEY} APPLICATION=${JOB_NAME} python3 Helpers/jirahelper.py -r"

        cleanWs()
      }
    }
  }
}