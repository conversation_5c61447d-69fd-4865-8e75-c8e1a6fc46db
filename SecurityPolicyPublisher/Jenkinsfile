def setNotificationParams(branch) {
    wrap([$class: 'BuildUser']) {
        notificationParams = [
            'pipeline': 'Security Policy Publish',
            'Branch': branch,
            'Started By': env.BUILD_USER,
            'Reason': params.description
        ]
    }
    return
}

def initialiseStateObject(def state_file_name, def source_branch) {
    sh "git checkout master"
    def state = [:]
    if (fileExists(state_file_name)) {
        state = readJSON file: state_file_name
        sh "git checkout ${source_branch}"
        return state
    }
    echo "State file does not exist."
    state["metadata"] = [:]
    state["metadata"]["name"] = "ss-security-policy"
    state["metadata"]["description"] = "Stateful file for ss-security-policy pipeline in production. It is auto generated file. Don't modify anything unless you find any issues with the file."
    state["metadata"]["last_updated"] = new Date().toString()
    state["policyStates"] = [:]
    sh "git checkout ${source_branch}"
    return state
}

pipeline {
    agent {
        label 'JenkinsWorkerFleet'
    }

    parameters {
        string name: 'pullRequestUrl', description: 'Note: Your PR should pass all checks and be in open state.', trim: true
        string name: 'description', description: 'Describe the for the policy.', defaultValue: 'Description not provided', trim: true
    }

    environment {
        BITBUCKET_OAUTH_TOKEN = credentials('BitBucketOAuthToken')
    }

    stages {
        stage('Initialise') {
            steps {
                script {
                    methods   = load 'Helpers/methods.groovy'
                    notifier  = load 'Helpers/notifications.groovy'
                    variables = load 'Helpers/variables.groovy'
                    
                    pr_link_tokens = params.pullRequestUrl.split("/")
                    repo_name = pr_link_tokens[4]
                    pr_id = pr_link_tokens[6]

                    methods.verifyPRChecks(repo_name, pr_id, BITBUCKET_OAUTH_TOKEN)
                    pr_data = methods.getPRInfoSet(repo_name, pr_id, BITBUCKET_OAUTH_TOKEN)
                    setNotificationParams(pr_data.src_branch)

                    notifier.sendOpsRelatedNotifcationToChannel("STARTED", variables.SECURITY_POLICY_CHANNEL)

                    if (pr_data.dest_branch != "master") {
                        currentBuild.description = "PR should be raised to master branch"
                        error("PR should be raised to master branch")
                    }

                    dir ("ss-policies") {
                        methods.cloneRepoWithGit(variables.SECURITY_POLICY_REPO, pr_data.src_branch)
                        author = sh script: "git show -s --format='%ae' HEAD | tr -d '\n'", returnStdout: true
                    }
                }
            }
        }

        stage('Initialise State') {
            steps {
                script {
                    dir ("ss-policies") {
                        state_file =  ".state.json"
                        state_obj = initialiseStateObject(state_file, pr_data.src_branch)
                    }
                }
            }
        }
        stage('Validate Json') {
            steps {
                script {
                    dir ("ss-policies") {
                        def expectedPattern = "^[A-Z][a-zA-Z0-9]*\\.(iam|scp)\\.json\$"
                        policy_files = pr_data.files.findAll { it.new.path.endsWith('.json') }
                        
                        for (policy_file_data in policy_files) {
                            policy_file = policy_file_data.new.path
                            sh "sed -i 's://.*::g' ${policy_file}"
                            sh "jq empty ${policy_file}"
                            
                            file_name = policy_file.split("/")[-1]

                            if (!file_name =~ expectedPattern) {
                                error "Filename ${file_name} does not match the expected PascalCase pattern. Use pascal case for the filename and follow this pattern. ^[A-Z][a-zA-Z0-9]*\\.(iam|scp)\\.json\$."
                            } else {
                                policy_type = file_name.split("\\.")[-2]
                                def policy_type_code = variables.POLICY_TYPES[policy_type]
                                notificationParams[policy_type_code] = "Detected"
                                sh "aws accessanalyzer validate-policy --policy-document file://${policy_file} --policy-type ${policy_type_code} --output yaml > validation.yml"
                                validation_result = sh script: "cat validation.yml | yq '.findings[] | select(.findingType == \"ERROR\") | .findingDetails'", returnStdout: true
                                if (validation_result != "") {
                                    sh "cat validation.yml"
                                    error "${validation_result}"
                                }
                                if (sh(script: "cat validation.yml | yq '.findings' | tr -d '\\n'", returnStdout: true) != "[]") {
                                    sh "cat validation.yml"
                                    input "Validation failed for ${file_name}. Check logs for more details. Do you want to continue?"
                                }
                            }
                        }
                    }
                }
            }
        }

        stage('Publish New Security Policy') {
            steps {
                script {
                    dir ("ss-policies") {
                        try {
                            for (policy_file_data in policy_files) {
                                try {
                                    policy_file = policy_file_data.new.path
                                } catch (Exception e) {
                                    error "Policy file not found."
                                }
                                
                                file_path_tokens = policy_file.split("/")
                                
                                account = file_path_tokens[0].split("-")
                                account_id = account[0]
                                
                                file_name_tokens = file_path_tokens[-1].split("\\.")
                                
                                policy_name = "GitManaged" + file_name_tokens[0]
                                policy_type = file_name_tokens[1]

                                tags = "Key=\"CreatedBy\",Value=\"${author}\" Key=\"Team\",Value=\"InfoSec\" Key=\"Service\",Value=\"security\""
                                state_key = account_id+"_"+policy_type.toUpperCase()+"_"+policy_name
                                
                                if (policy_type == "scp") {
                                    withAWS(region: 'us-east-1', roleAccount: variables.ORGANIZATIONS_ACCOUNT_NUMBER, role: variables.SECURITY_POLICY_MANAGING_ROLE) {
                                        if(!state_obj["policyStates"].containsKey(state_key)) {    
                                            try {
                                                policy_id = sh (script: "aws organizations create-policy --content \"file://${policy_file}\" --description \"${params.description}\" --name \"${policy_name}\" --output text --type SERVICE_CONTROL_POLICY --query \"Policy.PolicySummary.Id\" --tags ${tags}", returnStdout: true).trim()
                                                state_obj["policyStates"][state_key] = policy_id
                                                return
                                            } catch (Exception e) {
                                                policy_id = sh (script: "aws organizations list-policies --filter SERVICE_CONTROL_POLICY --query \"Policies[?Name=='${policy_name}'].Id\" --output text --profile org", returnStdout: true).trim()
                                                state_obj["policyStates"][state_key] = policy_id
                                            }
                                        }
                                        policy_id = state_obj["policyStates"][state_key]
                                        sh "aws organizations update-policy --content \"file://${policy_file}\" --description \"${params.description}\" --policy-id '${policy_id}'"
                                    
                                    }
                                } else {
                                    withAWS(region: 'us-east-1', roleAccount: account_id, role: variables.SECURITY_POLICY_MANAGING_ROLE) {
                                        if(!state_obj["policyStates"].containsKey(state_key)) {
                                            try {
                                                policy_arn = sh (script: "aws iam create-policy --policy-name \"${policy_name}\" --policy-document \"file://${policy_file}\" --query \"Policy.Arn\" --output text --tags ${tags}", returnStdout: true).trim()
                                                state_obj["policyStates"][state_key] = policy_arn
                                                return
                                            } catch (Exception e) {
                                                policy_arn = sh (script: "aws iam list-policies --query \"Policies[?PolicyName=='${policy_name}'].Arn\" --output text", returnStdout: true).trim()
                                                state_obj["policyStates"][state_key] = policy_arn
                                            }
                                        } 
                                        policy_arn = state_obj["policyStates"][state_key]
                                        sh "aws iam create-policy-version --policy-arn '${policy_arn}' --policy-document \"file://${policy_file}\" --set-as-default"
                                    }
                                }
                            }
                            methods.mergePR(repo_name, pr_id, BITBUCKET_OAUTH_TOKEN)
                        } catch (Exception e) {
                            error "Error while publishing the policy. ${e}"
                        } finally {
                            sh "git restore ."
                            sh "git clean -f"
                            sh "git checkout master"
                            state_obj["revision"] = env.BUILD_NUMBER
                            writeJSON file: state_file, json: state_obj, pretty: 4
                            sh "cat $state_file"
                            sh "git add $state_file"
                            methods.commitAndPush("${author} updated the state file.")
                        }
                    }
                }
            }
        }
    }
    post {
        always {
            script {
                cleanWs()
            }
        }
        success {
            script {
                currentBuild.description = 'Deployment successful'
                notifier.sendOpsRelatedNotifcationToChannel("SUCCESS", variables.SECURITY_POLICY_CHANNEL)
            }
        }
        failure {
            script {
                currentBuild.description = 'Deployment failed'
                notifier.sendOpsRelatedNotifcationToChannel("FAILED", variables.SECURITY_POLICY_CHANNEL)
            }
        }
        aborted {
            script {
                currentBuild.description = 'Deployment aborted'
                notifier.sendOpsRelatedNotifcationToChannel("ABORTED", variables.SECURITY_POLICY_CHANNEL)
            }
        }
    }
}
