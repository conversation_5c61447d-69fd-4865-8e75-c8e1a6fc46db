pipeline {
    agent {
        label "JenkinsWorkerFleet"
    }
    parameters {
        choice(name: 'application', choices: ['NextJs', 'Strapi'], description: 'Select the environment to rollback')
    }
    environment { 
        ARGOCD_PASSWORD = credentials('SurveySparrowArgoCD') 
    }
    stages {
        stage("Initialize Rollback") {
            steps {
                script {
                    methods                        = load "Helpers/methods.groovy"
                    variables                      = load "Helpers/variables.groovy"
                    notifier                       = load "Helpers/notifications.groovy"
                    // Environment Variables
                    env.AWS_DEFAULT_REGION         = variables.DEFAULT_REGION
                    env.ARGOCD_DOMAIN              = variables.SURVEYSPARROW_ARGOCD_DOMAIN
                    env.ARGOCD_USERNAME            = variables.SURVEYSPARROW_ARGOCD_USERNAME
                    
                    if (params.application == 'NextJs') {
                        env.PARAMETER_STORE_NAME   = variables.WEBSITE_NEXTJS_BUNDLE_PARAMETER
                    } else if (params.application == 'Strapi') {
                        env.PARAMETER_STORE_NAME   = variables.WEBSITE_STRAPI_PARAMETER
                    }
    
                    env.version = sh script: 'let "version=$(aws ssm get-parameter --name $PARAMETER_STORE_NAME --query \'Parameter.Version\')-1" && echo $version', returnStdout: true
                    env.version = env.version.trim()
                    echo "Rolling back to version $version"
                    stable_version_data_output = sh(script: 'aws ssm get-parameter --name $PARAMETER_STORE_NAME:$version --query "Parameter.Value" --output text --with-decryption', returnStdout: true).trim()
                    stable_version_data = (params.application == 'NextJs') ? stable_version_data_output.tokenize('|') : stable_version_data_output
                    if (params.application == 'NextJs' && stable_version_data.size() != 2 && stable_version_data[1].size() < 2) {
                        error("No more versions to rollback")
                    }
                    notifier.sendStatusBasedNotification('WEBSITE_ROLLBACK_STARTED', variables.WEBSITE_NOTIFICATION_CHANNEL)
                    approver = input message: "Do you want to rollback ${params.application} to version $version (${stable_version_data_output})?", ok: "Rollback", submitterParameter: 'approver'
                    notifier.sendStatusBasedNotification('WEBSITE_ROLLBACK_APPROVED_INFO', variables.WEBSITE_NOTIFICATION_CHANNEL)
                }
            }
        }
        stage("Clone Repos") {
            steps {
                script {
                    dir(variables.EKS_REPO_FOLDER) {
                        methods.cloneRepoWithGit(variables.KUBERNETES_YAML_REPO , variables.KUBERNETES_YAML_BRANCH)
                    }
                }
            }
        }
        stage("RollBack NextJs") {
            when {
                expression {
                    return params.application == 'NextJs'
                }
            }
            steps {
                script {
                    dir(variables.CONFIG_REPO_FOLDER) {
                        methods.cloneRepoWithGit(variables.CONFIG_REPO, variables.CONFIG_REPO_BRANCH)

                        sh "sed -i 's/BUILD_VERSION=.*/BUILD_VERSION=${stable_version_data[0]}/g' ${variables.WEBSITE_NEXTJS_ENV_FILE_NAME}"
                        sh "cat ${variables.WEBSITE_NEXTJS_ENV_FILE_NAME} | grep prod | grep ${stable_version_data[0]}"
                        EKS_CONFIGMAP_PATH="$WORKSPACE/${EKS_REPO_FOLDER}/${variables.WEBSITE_EKS_CONFIGMAP_NEXTJS_PATH}"
                        sh "CONFIGMAPNAME=${variables.WEBSITE_NEXTJS_CONFIGMAP_NAME} NAMESPACE=${variables.WEBSITE_NAMESPACE} ENVFILE=${variables.WEBSITE_NEXTJS_ENV_FILE_NAME} TARGETFILE=$EKS_CONFIGMAP_PATH sh ../Helpers/env2configmap.sh"
                        sh "cat $EKS_CONFIGMAP_PATH | grep prod | grep ${stable_version_data[0]}"
                    }
                    dir(variables.EKS_REPO_FOLDER) {
                        def deploymentData = readYaml file: variables.WEBSITE_EKS_NEXTJS_DEPLOYMENT_PATH
                        deploymentData.spec.template.spec.containers[0].image = "${variables.ECR_REPO_APP[variables.DEFAULT_REGION]}/${variables.WEBSITE_NEXTJS_ECR_REPO}:${stable_version_data[1]}"
                        writeYaml file: variables.WEBSITE_EKS_NEXTJS_DEPLOYMENT_PATH, data: deploymentData, overwrite: true
                        sh "cat ${variables.WEBSITE_EKS_NEXTJS_DEPLOYMENT_PATH} | grep :master | grep ${stable_version_data[1]}"
                    }
                }
            }
        }
        stage("RollBack Strapi") {
            when {
                expression {
                    return params.application == 'Strapi'
                }
            }
            steps {
                script {
                    dir(variables.EKS_REPO_FOLDER) {
                        def deploymentData = readYaml file: variables.WEBSITE_EKS_STRAPI_DEPLOYMENT_PATH

                        deploymentData.spec.template.spec.containers[0].image = "${variables.ECR_REPO_APP[variables.DEFAULT_REGION]}/${variables.WEBSITE_STRAPI_ECR_REPO}:${stable_version_data_output}"
                        writeYaml file: variables.WEBSITE_EKS_STRAPI_DEPLOYMENT_PATH, data: deploymentData, overwrite: true
                        sh "cat ${variables.WEBSITE_EKS_STRAPI_DEPLOYMENT_PATH} | grep ${stable_version_data_output} | grep -e ':[a-z1-9]\\{7\\}_[1-9]\\{1,5\\}\$'"
                    }
                }
            }
        }
        stage("Update Git Origin") {
            steps {
                script {
                    sshagent (credentials: ["${variables.BB_CREDENTIAL_ID}"]) {
                        if (params.application == 'NextJs') {
                            dir(variables.CONFIG_REPO_FOLDER) {
                                methods.commitAndPush("Rollback to version $version", variables.CONFIG_REPO_BRANCH)
                            }
                        }
                        dir(variables.EKS_REPO_FOLDER) {
                            methods.commitAndPush("Rollback to version $version", variables.KUBERNETES_YAML_BRANCH)
                        }
                    }
                }
            }
        }

        stage("ArgoCD Sync") {
            steps {
                script {
                    env.ARGOCD_APP = (params.application == 'NextJs') ? "website-nextjs-argocd" : "website-strapi-argocd"
                    sh 'argocd login $ARGOCD_DOMAIN --username $ARGOCD_USERNAME --password $ARGOCD_PASSWORD'
                    sh "argocd app sync $ARGOCD_APP --grpc-web"
                    sh "argocd app wait $ARGOCD_APP --grpc-web"
                    sh "argocd logout $ARGOCD_DOMAIN"
                }
            }
            post {
                success {
                    script {
                        sh "aws ssm put-parameter --name '${env.PARAMETER_STORE_NAME}' --value '${stable_version_data_output}' --type SecureString --overwrite"
                        env.VERSION_PARAMETER = (params.application == "NextJs") ? WEBSITE_NEXTJS_VERSION_PARAMETER : WEBSITE_STRAPI_VERSION_PARAMETER
                        downgrade_version = sh(script: 'let "version=$(aws ssm get-parameter --name $VERSION_PARAMETER --query \'Parameter.Value\' --with-decryption)-1" && echo $version', returnStdout: true).trim()
                        sh "aws ssm put-parameter --name $VERSION_PARAMETER --value $downgrade_version --type SecureString --overwrite"
                    }
                }
            }
        }
    }
    post {
        always {
            cleanWs()
        }
        success {
            script {
                notifier.sendStatusBasedNotification('WEBSITE_ROLLBACK_SUCCESS', variables.WEBSITE_NOTIFICATION_CHANNEL)
            }
        }

        failure {
            script {
                notifier.sendStatusBasedNotification('WEBSITE_ROLLBACK_FAILURE', variables.WEBSITE_NOTIFICATION_CHANNEL)
            }
        }

        aborted {
            script {
                notifier.sendStatusBasedNotification('WEBSITE_ROLLBACK_ABORTED', variables.WEBSITE_NOTIFICATION_CHANNEL)
            }
        }
    }
}