properties([
    parameters([
        choice(name: 'Domain', choices: ['surveysparrow.ae', 'surveysparrow.com'], description: 'Domain Name'),
        choice(name: 'Action', choices: ["add", "delete", "replace"], description: 'Action need to be performed in the Cloudflare'),
        choice(name: 'PathType', choices: ['fixed', 'prefix'], description: 'Regex mode for new entry (Any value can be passed for delete action)'),
        activeChoiceHtml(
            choiceType: 'ET_FORMATTED_HTML',
            name: 'URLPath',
            omitValueField: false,
            randomName: 'choice-parameter-298328809093902390',
            script: groovyScript(
                fallbackScript: [
                    classpath: [],
                    oldScript: '',
                    sandbox: true,
                    script: 'return "<b>Something went wrong, contact DevOps</b>"'
                ],
                script: [
                    classpath: [],
                    oldScript: '',
                    sandbox: true,
                    script: '''
                    return """
                    <div class=\'jenkins-form-description\'>Target path of the URL (example: <code>/about-us → about-us</code> - values can be space-separated)<br><i>[Rules for add action or replace with new entry: No leading slash, No trailing slash, No query string]<br>[Rules for delete: Please provide the path with the existing path regex]</i>.
                    </div><br><input name=\'value\' type=\'text\' value=\'\' class=\'jenkins-input\' required><br>
                    """
                    '''
                ]
            )
        ),
        activeChoiceHtml(
            choiceType: 'ET_FORMATTED_HTML',
            name: 'OldPath',
            omitValueField: false,
            randomName: 'choice-parameter-29832832093902390',
            referencedParameters: 'Action',
            script: groovyScript(
                fallbackScript: [
                    classpath: [],
                    oldScript: '',
                    sandbox: true,
                    script: 'return "<b>It\'s only required for the replace action</b>"'
                ],
                script: [
                    classpath: [],
                    oldScript: '',
                    sandbox: true,
                    script: '''if (Action.equals(\'replace\')) {
                    return """
                    <div class=\'jenkins-form-description\'>Old paths should be replaced with new paths. Please provide the path with the existing path regex <br><i>(where multiple values can be passed separated by a single space and enclose values with double quote for each value)</i>.
                    </div><br><input name=\'value\' type=\'text\' value=\'\' class=\'jenkins-input\' required><br>
                    """
                    }'''
                ]
            )
        ),
    ])
])

pipeline {
    agent {
        label "JenkinsWorkerFleet"
    }
    environment {
        CLOUDFLARE_API_KEY = credentials('cloudflare-token')
    }
    stages {
        stage("Initialize") {
            steps {
                script {
                    notifier          = load "Helpers/notifications.groovy"
                    variables         = load "Helpers/variables.groovy"
                    setup             = load "Helpers/methods.groovy"
                    command_output    = "Not Started"
                    oldpath = (params.OldPath == null || params.OldPath.size() == 0) ? "NA" :params.OldPath.replace(",", "")
                    domains_infoset = variables.CLOUDFLARE_DOMAINS
                    if(params.URLPath == null || params.URLPath.size() == 0) {
                        path = "NO_PATH"
                        currentBuild.description = "No path provided"
                        exit("No path provided")
                    }
                    path = params.URLPath.replace(",", "")
                    if(params.Action != "delete") {
                        patheffect = params.Action
                    } else {
                        patheffect = "purge"
                    }
                    setup.prepInstance()
                    notifier.sendStatusBasedNotification('WEBSITE_ORIGIN_REDIRECTION_STARTED', variables.WEBSITE_NOTIFICATION_CHANNEL)
                }
            }
        }
        stage("Update Cloudflare Redirection") {
            steps {
                script {
                    if (params.Action == "add") {
                        echo "Adding path redirection for ${params.Path}"
                        sh_output = sh script: "python3 Helpers/cloudflareredirection.py --zoneid ${domains_infoset["${Domain}"]["zoneid"]} --ruleid ${domains_infoset["${Domain}"]["ruleid"]} --apikey ${CLOUDFLARE_API_KEY}  --host ${params.Domain} --action ${params.Action} --mode ${params.PathType} --path ${path} >> output.txt 2>&1", returnStatus: true
                    } else if (params.Action == "delete") {
                        echo "Deleting path redirection for ${params.Path}"
                        sh_output = sh script: "python3 Helpers/cloudflareredirection.py --zoneid ${domains_infoset["${Domain}"]["zoneid"]} --ruleid ${domains_infoset["${Domain}"]["ruleid"]} --apikey ${CLOUDFLARE_API_KEY}  --host ${params.Domain} --action ${params.Action} --mode ${params.PathType} --path ${path} >> output.txt 2>&1", returnStatus: true
                    } else {
                        echo "Replacing path redirection for ${params.OldPath} with ${params.Path}"
                        sh_output = sh script: "python3 Helpers/cloudflareredirection.py --zoneid ${domains_infoset["${Domain}"]["zoneid"]} --ruleid ${domains_infoset["${Domain}"]["ruleid"]} --apikey ${CLOUDFLARE_API_KEY}  --host ${params.Domain} --action ${params.Action} --mode ${params.PathType} --path ${path} --oldpath ${oldpath} >> output.txt 2>&1", returnStatus: true
                    }
                    file_content = readFile('output.txt')
                    echo "Output: ${file_content}"
                    if (sh_output == 0) {
                        command_output = file_content
                    } else {
                        command_output = file_content
                        exit("Failed to update redirection")
                    }
                }
            }
        }
    }
    post {
        always {
            cleanWs()
            echo "Output: ${command_output}"
        }
        success {
            script {
                notifier.sendStatusBasedNotification('WEBSITE_ORIGIN_REDIRECTION_SUCCESS', variables.WEBSITE_NOTIFICATION_CHANNEL)
            }
        }
        failure {
            script {
                notifier.sendStatusBasedNotification('WEBSITE_ORIGIN_REDIRECTION_FAILURE', variables.WEBSITE_NOTIFICATION_CHANNEL)
            }
        }
        aborted {
            script {
                notifier.sendStatusBasedNotification('WEBSITE_ORIGIN_REDIRECTION_ABORTED', variables.WEBSITE_NOTIFICATION_CHANNEL)
            }
        }
    }
}