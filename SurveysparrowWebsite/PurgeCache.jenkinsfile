properties([
    parameters([
        activeChoice(
            choiceType: 'PT_SINGLE_SELECT', 
            description: 'Type of cache to purge. hosts - Purge cache for the specified hostnames. [prefixes - Purge cache for the specified path prefixes] [files - Purge cache for the specified files]',
            filterLength: 1, 
            filterable: false, 
            name: 'cache_type', 
            randomName: 'choice-parameter-7404054524953575', 
            script: groovyScript(
                fallbackScript: [
                    classpath: [], 
                    oldScript: '', 
                    sandbox: true, 
                    script: 'return ["ERROR"]'
                ],
                script: [
                    classpath: [], 
                    oldScript: '', 
                    sandbox: true, 
                    script: 'return ["hosts","prefixes","files"]'
                ]
            )
        ),
        string(name: 'url_path', description: 'Path to purge [include leading slash] (Note: This is required only if you have selected path prefix or full path as cache type)'),
        string(name: 'description', description: 'Mention the reason for this cache purge?'),
        activeChoice(
            choiceType: 'PT_SINGLE_SELECT', 
            description: 'Type of cache to purge. hosts - Purge cache for the specified hostnames. [prefixes - Purge cache for the specified path prefixes] [files - Purge cache for the specified files]',
            filterLength: 1, 
            filterable: false, 
            name: 'base_domain', 
            randomName: 'choice-parameter-7404054524952323', 
            script: groovyScript(
                fallbackScript: [
                    classpath: [], 
                    oldScript: '', 
                    sandbox: true, 
                    script: 'return ["ERROR"]'
                ],
                script: [
                    classpath: [], 
                    oldScript: '', 
                    sandbox: true, 
                    script: 'return ["surveysparrow.com","surveysparrow.ae"]'
                ]
            )
        ),
        reactiveChoice(
            choiceType: 'PT_CHECKBOX', 
            filterLength: 1, 
            filterable: false, 
            name: 'domain', 
            randomName: 'choice-parameter-7403090036542959', 
            referencedParameters: 'base_domain',
            script: groovyScript(
                fallbackScript: [
                    classpath: [], 
                    oldScript: '', 
                    sandbox: true, 
                    script: 'return[\'ERROR\']'
                ], 
                script: [
                    classpath: [], 
                    oldScript: '', 
                    sandbox: true, 
                    script: '''
                    if (base_domain == "surveysparrow.com") {
                        return ["surveysparrow.com","www.surveysparrow.com","br.surveysparrow.com"]
                    } else if (base_domain == "surveysparrow.ae") {
                        return ["surveysparrow.ae"]
                    } else {
                        return [base_domain]
                    }'''
                ]
            )
        )
    ])
])

pipeline {
    agent {
        label 'JenkinsWorkerFleet'
    }

    environment {
        CLOUDFLARE_API_TOKEN = credentials("cloudflare-token")
    }

    stages {
        stage("Initialize") {
            steps {
                script {
                    variables         = load "Helpers/variables.groovy"
                    methods           = load "Helpers/methods.groovy"
                    notifier          = load "Helpers/notifications.groovy"
                    zone_id           = variables.CLOUDFLARE_DOMAINS["${params.base_domain}"]["zoneid"]
                    domains           = params.domain.split(",")
                    job_description   = (params.description == '') ? 'No description provided' : params.description
                    paths             = (params.cache_type == 'hosts') ? [''] : params.url_path.split(" ") 

                    notifier.sendStatusBasedNotification('CLOUDFLARE_CACHE_PURGE_STARTED', variables.WEBSITE_NOTIFICATION_CHANNEL)
                }
            }
        }
        stage("Purge Cache") {
            steps {
                script {
                    methods.purgeCache(zone_id, CLOUDFLARE_API_TOKEN, params.cache_type, domains, paths)
                }
            }
        }
    }
    post {
        always {
            echo "Cleaning Deployment Service Workspace"
            cleanWs()
        }
        success {
            script {
                notifier.sendStatusBasedNotification('CLOUDFLARE_CACHE_PURGE_SUCCESS', variables.WEBSITE_NOTIFICATION_CHANNEL)
            }
        }

        failure {
            script {
                notifier.sendStatusBasedNotification('CLOUDFLARE_CACHE_PURGE_FAILURE', variables.WEBSITE_NOTIFICATION_CHANNEL)
            }
        }

        aborted {
            script {
                notifier.sendStatusBasedNotification('CLOUDFLARE_CACHE_PURGE_ABORTED', variables.WEBSITE_NOTIFICATION_CHANNEL)
            }
        }
    }
}