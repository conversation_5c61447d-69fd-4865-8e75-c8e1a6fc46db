def setNotificationParams() {
  wrap([$class: 'BuildUser']) {
    // setting this globally for this pipeline
    notificationParams = [
      'pipeline': "Blink deployment",
      'Region': AWS_REGION,
      'Branch': 'master',
      'Started By': env.BUILD_USER,
      'Reason': params.reason
    ]
  }
}

pipeline {
    agent {
        label 'ArmDockerBuilderFleet'
    }

    parameters {
        string (
            name: 'reason',
            defaultValue: 'No reason provided',
            description: 'The reason for updating the resources'
        )
    }

    environment {
        ARGOCD_PASSWORD = credentials('PlatformArgoCD')
    }

    stages {
        stage('Initialize') {
            steps {
                script {
                    variables         = load 'Helpers/variables.groovy'
                    methods           = load 'Helpers/methods.groovy'
                    notifier          = load 'Helpers/notifications.groovy'

                    region_code       = env.JOB_BASE_NAME
                    AWS_REGION   = variables.REGION_CODE_DC_NAME_MAP[region_code]
                    
                    setNotificationParams()
                    notifier.sendOpsRelatedNotifcationToChannel("STARTED", variables.BLINK_DEPLOYMENT_CHANNEL)
                }
            }
        }
        stage('Clone Repositories') {
            steps {
                script {
                    dir("blink") {
                        methods.cloneRepoWithGit(variables.BLINK_REPO)
                    }
                    dir(variables.EKS_REPO_FOLDER) {
                        methods.cloneRepoWithGit(variables.KUBERNETES_YAML_REPO)
                    }
                    dir(variables.CONFIG_REPO_FOLDER) {
                        methods.cloneConfig()
                    }
                }
            }
        }
        stage('Build') {
            steps {
                dir("blink") {
                    script {
                        current_branch = sh(script: "git rev-parse --abbrev-ref HEAD", returnStdout: true).trim()
                        latest_commit = sh(script: "git rev-parse --short HEAD", returnStdout: true).trim()
                        ECR_REPO = "${variables.PLATFORM_ECR_REPO_APP[AWS_REGION]}/blink"
                        IMAGE_TAG = "${current_branch}_${latest_commit}"

                        sh "docker build --target production -t ${ECR_REPO}:${IMAGE_TAG} -t ${ECR_REPO}:latest ."
                        sh "docker images"
                        
                        withAWS(region: AWS_REGION, roleAccount: variables.BLINK_SERVICE["account"], role: variables.BLINK_SERVICE["role"]) {
                            sh "aws ecr get-login-password | docker login --username AWS --password-stdin ${ECR_REPO}"
                            sh "docker push ${ECR_REPO}:${IMAGE_TAG}"
                            sh "docker push ${ECR_REPO}:latest"
                        }
                    }
                }
            }
        }
        stage('Update K8s Contexts') {
            steps {
                script {
                    dir(variables.EKS_REPO_FOLDER) {
                        KUBERNETES_DEPLOYMENT_YAML_FILE = "blink/${AWS_REGION}/02-blink-deployment.yaml"
                        KUBERNETES_CONFIG_EKS_FILE = "blink/${AWS_REGION}/03-blink-configmap.yaml"
                        
                        CONFIG_REPO_PATH = "${env.WORKSPACE}/${variables.CONFIG_REPO_FOLDER}"
                        KUBERNETES_CONFIG_FILE = "${CONFIG_REPO_PATH}/Platform/blink/${AWS_REGION}/configmap.yaml"

                        sh "sed -i 's|${ECR_REPO}.*|${ECR_REPO}:${IMAGE_TAG}|g' ${KUBERNETES_DEPLOYMENT_YAML_FILE}"
                        sh "cp ${KUBERNETES_CONFIG_FILE} ${KUBERNETES_CONFIG_EKS_FILE}"

                        sshagent (credentials: ["${variables.BB_CREDENTIAL_ID}"]) {
                            methods.commitAndPush("Update blink context in ${AWS_REGION} EKS deployment #${env.BUILD_NUMBER}")
                        }
                    }
                }
            }
        }
        stage('Deploy') {
            steps {
                script {
                    sh "argocd login ${variables.PLATFORM_ARGOCD_DOMAIN} --username ${variables.PLATFORM_ARGOCD_USERNAME} --password ${env.ARGOCD_PASSWORD}"
                    sh "argocd app sync blink-${AWS_REGION} --grpc-web"
                    currentBuild.description = 'Syncing deployment'
                    notificationParams['Image Tag'] = IMAGE_TAG
                    notifier.sendOpsRelatedNotifcationToChannel("INFO", variables.BLINK_DEPLOYMENT_CHANNEL)
                    sh "argocd app wait blink-${AWS_REGION} --grpc-web"
                    sh "argocd logout ${variables.PLATFORM_ARGOCD_DOMAIN}"
                }
            }
        }
    }
    post {
        always {
            script {
                cleanWs()
            }
        }
        success {
            script {
                currentBuild.description = 'Deployment successful'
                notifier.sendOpsRelatedNotifcationToChannel("SUCCESS", variables.BLINK_DEPLOYMENT_CHANNEL)
            }
        }
        failure {
            script {
                currentBuild.description = 'Deployment failed'
                notifier.sendOpsRelatedNotifcationToChannel("FAILURE", variables.BLINK_DEPLOYMENT_CHANNEL)
            }
        }
        aborted {
            script {
                currentBuild.description = 'Deployment aborted'
                notifier.sendOpsRelatedNotifcationToChannel("ABORTED", variables.BLINK_DEPLOYMENT_CHANNEL)
            }
        }
    }
}