# SurveySparrow Pipeline #

This repository contains all the pipeline files which are used in the production Jenkins Server - https://blackpearl.surveysparrow.com

### Files and Folder Structure ###
Follow the pipeline folder structure while creating the pipelines.

- Pipeline Name
  - Jenkinsfile
  - Other supporting .py or nodejs scripts

If there are multiple Jenkinsfiles associated with an single project / pipeline.

- Pipeline Name
  - Jenkins
    - staging
    - production
  - Other supporting .py or nodejs scripts


### Development ###
1. Branch name should be descriptive as name of the pipeline or for what purpose the branch is created.
2. Follow naming conventions,
    - Folder name should be CamelCased.
    - If you are using multiple Jenkinsfiles for the same pipeline / same service. Create a folder called Jenkins and then place the different Jenkinsfiles inside that folder.
    - Jenkinsfile case should be followed as it is.
    - If there is only one supporting file name it as `main.ext`
    - If there are multiple files, create a `main.ext` which will be the main file called from the Jenkins Pipeline.