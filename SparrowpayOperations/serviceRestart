pipeline
{
    agent { label "JenkinsWorkerFleet" }

    parameters
    {
        choice(
            name: 'service',
            choices: [ 'payments' , 'webhooks' , 'gateway' , 'admin-backend' , 'notifier' ], 
            description: 'Specify which micro service to deploy'
        )
    }

    environment
    {
        // ARGOCD Details
        ARGOCD_PASSWORD = credentials('PlatformArgoCD')
        ARGOCD_USERNAME = "admin"
        ARGOCD_DOMAIN = "platform-argocd.surveysparrow.com"

        // Branch Details
        ECR_REPO_NAME = 'sparrowpay'
    }

    stages
    {
        stage("Restarting Service")
        {
            steps
            {
                script
                {
                    notifier = load 'Helpers/notifications.groovy'
                    notifier.sendSlackNotificationSparrowpay("#0066CC", "SPARROWPAY_SERVICE_RESTART_STARTED")

                    echo "Logging into ARGO CD"
                    sh "argocd login ${env.ARGOCD_Domain} --username ${env.ARGOCD_USERNAME} --password ${env.ARGOCD_PASSWORD}"

                    serviceNameCorrection = [
                        "payments" : "payment",
                        "webhooks" : "webhook",
                        "gateway" : "gateway",
                        "admin-backend" : "admin-backend",
                        "notifier" : "notifier"
                    ]

                    echo "Redeploying the Admin Backend to get the latest latest Config"
                    sh "argocd app actions run argocd/${env.ECR_REPO_NAME}-${params.service}-argocd  restart --kind Deployment --resource-name ${env.ECR_REPO_NAME}-${serviceNameCorrection[params.service]}-deployment"

                    echo "Waiting still the Update is over"
                    sh "argocd app wait ${env.ECR_REPO_NAME}-${params.service}-argocd --grpc-web"

                    echo "Logging Out"
                    sh "argocd logout ${env.ARGOCD_Domain}"
                }
            }
        }
    }
    post 
    {
        always 
        {
            script 
            {
                currentBuild.displayName = "Restarted: ${params.service} : ${currentBuild.number}"
                cleanWs()
            }
        }
        success 
        {
            script 
            {
                notifier.sendSlackNotificationSparrowpay("#CDFFFF", "SPARROWPAY_SERVICE_RESTART_SUCCESS")
            }
        }
        failure 
        {
            script 
            {
                notifier.sendSlackNotificationSparrowpay("#E74C3C", "SPARROWPAY_SERVICE_RESTART_FAILED")
            }
        }
        aborted 
        {
            script 
            {
                notifier.sendSlackNotificationSparrowpay("#9D9D9D", "SPARROWPAY_SERVICE_RESTART_ABORTED")
            }
        }
    }
}