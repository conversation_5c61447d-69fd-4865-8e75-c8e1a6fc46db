pipeline
{
    agent { label "JenkinsWorkerFleet" }

    parameters
    {
        choice(
            name: 'service',
            choices: [ 'payments' , 'webhooks' , 'gateway' , 'admin-backend' , 'notifier' ], 
            description: 'Specify which micro service to deploy'
        )
    }

    environment
    {

        AWS_DEFAULT_REGION = "us-east-1"

        // Branch Details
        ECR_REPO_NAME = 'sparrowpay'
        DEFAULT_EKS_BRANCH = "master"
        DEFAULT_CONFIG_BRANCH = "master" // Will make these 3 into one after we have some runs

        // GIT URLS
        EKS_GIT_URL = "*****************:surveysparrow/surveysparrow-production-eks.git"
        CONFIG_GIT_URL = "*****************:surveysparrow/surveysparrow-production-config.git"

        // Git Config Details
        CONFIG_EMAIL = "<EMAIL>"
        CONFIG_USERNAME = "saas-devops"

        // ARGOCD Details
        ARGOCD_PASSWORD = credentials('PlatformArgoCD')
        ARGOCD_USERNAME = "admin"
        ARGOCD_DOMAIN = "platform-argocd.surveysparrow.com"
    }

    stages
    {
        stage("Update Config")
        {
            steps
            {
                script
                {
                    echo "<----------------Updating Config----------------------->"

                    notifier = load 'Helpers/notifications.groovy'
                    helper = load 'Helpers/sparrowpay-helpers.groovy'
                    methods   = load 'Helpers/methods.groovy'
                    variables = load 'Helpers/variables.groovy'

                    
                    currentBuild.displayName = "Updating Config : ${params.service} : ${currentBuild.number}"
                    notifier.sendSlackNotificationSparrowpay("#0066CC", "SPARROWPAY_UPDATECONFIG_STARTED")

                    echo "Crating a folder 'surveysparrow-production-config' and Cloning the Config Repo: ${env.DEFAULT_CONFIG_BRANCH}"
                    dir ('surveysparrow-production-config') 
                    {
                        echo "Cloning the Repo Config Repo:${env.DEFAULT_CONFIG_BRANCH}"
                        methods.cloneConfig()
                    }

                    echo "Creating a folder 'surveysparrow-production-eks' and Cloning the EKS Repo: ${env.DEFAULT_EKS_BRANCH}"
                    dir("surveysparrow-production-eks")
                    {
                        methods.cloneEKSYAMLRepo("${env.DEFAULT_EKS_BRANCH}")
                    }

                    //
                    echo "Getting the Current and Incoming Yaml configs"
                    def incomingConfig = "${env.WORKSPACE}/surveysparrow-production-config/Platform/sparrowpay/${env.AWS_DEFAULT_REGION}/${params.service}.yaml"
                    def currentConfig = "${env.WORKSPACE}/surveysparrow-production-eks/sparrowpay/${env.AWS_DEFAULT_REGION}/${params.service}/02-${params.service}-configmap.yaml"

                    //
                    echo "Finding the other Diff of the current and incoming Config"
                    diffValidation = helper.checkFileDiff( currentConfig , incomingConfig )

                    echo "Checking if there are any Diff"
                    if (!diffValidation) 
                    {
                        echo "Files has some Changes so adding the "
                        sh "cat ${incomingConfig}"

                        echo "Removing the current present config present"
                        sh "rm -rf $currentConfig"

                        echo "Replace with the incoming config"
                        sh "cp $incomingConfig $currentConfig"

                        echo "Checking the config After"
                        sh "cat ${incomingConfig}"

                        sshagent (credentials: ["${variables.BB_CREDENTIAL_ID}"]) 
                        {
                            dir("surveysparrow-production-eks")
                            {
                            
                                echo "Configuring git with details"
                                sh "git config user.email ${env.CONFIG_EMAIL}"
                                sh "git config user.name ${env.CONFIG_USERNAME}"

                                echo "Checking the File changes"
                                sh "git status"

                                echo "Staging those changes in production"
                                sh "git add ."

                                sh "git commit -m 'Config Update: ${params.service} is Done'"
                                
                                echo "Pulling those code changes"
                                sh "git pull ${env.EKS_GIT_URL} ${env.DEFAULT_EKS_BRANCH} --rebase | true"

                                echo "Pushing the Latest deployment Changes to Git"
                                sh "git push ${env.EKS_GIT_URL} HEAD:${env.DEFAULT_EKS_BRANCH}"
                            }
                        }
                    }
                    else
                    {
                        echo "There is no change in config"
                    }
                }
            }
        }
        stage("Deployment in EKS")
        {
            steps
            {
                script
                {
                    echo "Logging into ARGO CD"
                    sh "argocd login ${env.ARGOCD_Domain} --username ${env.ARGOCD_USERNAME} --password ${env.ARGOCD_PASSWORD}"

                    echo "Applying Config Update"
                    sh "argocd app sync ${env.ECR_REPO_NAME}-${params.service}-argocd --grpc-web"

                    serviceNameCorrection = [
                        "payments" : "payment",
                        "webhooks" : "webhook",
                        "gateway" : "gateway",
                        "admin-backend" : "admin-backend",
                        "notifier" : "notifier"
                    ]

                    echo "Redeploying the Admin Backend to get the latest latest Config"
                    sh "argocd app actions run argocd/${env.ECR_REPO_NAME}-${params.service}-argocd  restart --kind Deployment --resource-name ${env.ECR_REPO_NAME}-${serviceNameCorrection[params.service]}-deployment"

                    echo "Waiting still the Update is over"
                    sh "argocd app wait ${env.ECR_REPO_NAME}-${params.service}-argocd --grpc-web"
                     
                    echo "Logging Out"
                    sh "argocd logout ${env.ARGOCD_Domain}"
                }
            }
        }
    }
    post 
    {
        always 
        {
            script 
            {
                currentBuild.displayName = "Config Updated: ${params.service} : ${currentBuild.number}"
                cleanWs()
            }
        }
        success 
        {
            script 
            {
                if ( !diffValidation )
                {
                    notifier.sendSlackNotificationSparrowpay("#2ECC71", "SPARROWPAY_UPDATECONFIG_SUCCESS")
                }
                else
                {
                    notifier.sendSlackNotificationSparrowpay("#CDFFFF", "SPARROWPAY_UPDATECONFIG_NO_CHANGE")
                }
            }
        }
        failure 
        {
            script 
            {
                notifier.sendSlackNotificationSparrowpay("#E74C3C", "SPARROWPAY_UPDATECONFIG_FAILED")
            }
        }
        aborted 
        {
            script 
            {
                notifier.sendSlackNotificationSparrowpay("#9D9D9D", "SPARROWPAY_UPDATECONFIG_ABORTED")
            }
        }
    }
}