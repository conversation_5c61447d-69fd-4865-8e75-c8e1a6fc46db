pipeline
{
    agent { label "DockerBuilderFleet" }

    parameters
    {
        string(
            name : "<PERSON><PERSON><PERSON>",
            defaultValue : "master",
            description : "Enter the branch which needs to be built"
        )
    }

    environment
    {
        AWS_DEFAULT_REGION = "us-east-1"

        // Branch Details
        ECR_REPO_NAME = "billing-bull-dashboard"
        ECR_REGISTRY_URL = "974682937630.dkr.ecr.us-east-1.amazonaws.com"

        // GIT URLS
        CODE_GIT_URL = "*****************:surveysparrow/dashboard-billing-plg-queues.git"

        EKS_CLUSTER = "platform-production-cluster"
        EKS_NAMESPACE =  "sparrowpay"
		EKS_DEPLOYMENT =  "bull-dashboard-deployment"

        BITBUCKET_CREDENTIALS = "BitBucketPrivateKey"
    }

    stages
    {
        stage("Git Clone")
        {
            steps
            {
                script
                {
                    variables = load 'Helpers/variables.groovy'
                    notifier = load 'Helpers/notifications.groovy'
                    
                    app = variables.SERVICES["SparrowPay"]
                    echo "Starting deployment !!! Sending Notifications"

                    notifier.sendSlackNotificationSparrowpay("#0066CC", "SPARROWPAY_BULL_DASHBOARD_STARTED")

                    currentBuild.displayName = "Deploying: ${params.BranchName}"
                    
                    echo "Cloning the Sparrowpay Bull Dashboard Repo branch: ${params.BranchName}"
                    dir('sparrowpay-bull-dashboard') 
                    {
                        git branch: "${params.BranchName}", credentialsId: "${env.BITBUCKET_CREDENTIALS}", url: "${env.CODE_GIT_URL}"
                    }
                }
            }
        }
        stage("Docker Build")
        {
            steps
            {
                script
                {
                    dir('sparrowpay-bull-dashboard') 
                    {
                        sh "sudo systemctl start docker"
                        sh "sleep 35"
                        withAWS(region: variables.DEFAULT_REGION, roleAccount: app.account, role: app.role) {
                            echo "Building the docker image for Bull Dashboard and Tagging the docker image with Latest"
                            sh "docker build -t ${env.ECR_REGISTRY_URL}/${env.ECR_REPO_NAME}:latest ."

                            echo "LOGGING INTO ECR"
                            sh "aws ecr get-login-password --region ${env.AWS_DEFAULT_REGION} | docker login --username AWS --password-stdin ${env.ECR_REGISTRY_URL}"
                            
                            echo  "Pushing the Docker image to ECR"
                            sh "docker push ${env.ECR_REGISTRY_URL}/${env.ECR_REPO_NAME}:latest"

                            echo  "Deleting the created image"
                            sh "docker rmi -f \$(docker images -aq) | true "
                        }
                    }
                }
            }
        }
        stage("EKS Deployment")
		{
			steps
			{
				script
                {
                    echo "Deploying the application on EKS Cluster..."
                    withAWS(region: variables.DEFAULT_REGION, roleAccount: app.account, role: app.role) {
                        withEnv(["KUBECONFIG=/tmp/.${env.EKS_CLUSTER}-config"]) {    
                            echo "LOGGING INTO EKS CLUSTER"
                            sh "aws eks --region ${env.AWS_DEFAULT_REGION} update-kubeconfig --name ${env.EKS_CLUSTER}"

                            echo "RESTARING THE DEPLOYMENT TO PULL LATEST IMAGE"
                            sh "kubectl rollout restart deployment/${env.EKS_DEPLOYMENT} -n ${env.EKS_NAMESPACE}"

                            echo "WAITING STILL THE RESTART IS COMPLETED"
                            sh "kubectl rollout status deployment/${env.EKS_DEPLOYMENT} -n ${env.EKS_NAMESPACE}"
                        }
                    }
                }
			}
		}
    }
    post 
    {
        always 
        {
            script 
            {
                currentBuild.displayName = "Deployed: ${params.BranchName}"
                cleanWs()
            }
        }
        success 
        {
            script 
            {
                notifier.sendSlackNotificationSparrowpay("#2ECC71", "SPARROWPAY_BULL_DASHBOARD_SUCCESS")
            }
        }
        failure 
        {
            script 
            {
                notifier.sendSlackNotificationSparrowpay("#E74C3C", "SPARROWPAY_BULL_DASHBOARD_FAILED")
            }
        }
        aborted 
        {
            script 
            {
                notifier.sendSlackNotificationSparrowpay("#9D9D9D", "SPARROWPAY_BULL_DASHBOARD_ABORTED")
            }
        }
    }
}