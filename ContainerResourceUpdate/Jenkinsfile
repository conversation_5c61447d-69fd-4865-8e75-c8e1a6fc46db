def setNotificationParams() {
  wrap([$class: 'BuildUser']) {
    // setting this globally for this pipeline
    notificationParams = [
      'pipeline': "ContainerUpdate",
      'region': params.region,
      'layer': layer,
      'service': service,
      'started by': env.BUILD_USER,
      'reason': params.reason
    ]
  }
}

pipeline {
  agent {
    label 'JenkinsWorkerFleet'
  }

  parameters {
    choice (
      name: 'layer',
      choices: ['api', 'application', 'reports', 'eui', 'worker', 'cron-worker', 'hulk-worker', 'generic-worker', 'notification-worker'],
      description: 'The layer to update resources for'
    )

    choice (
      name: 'region',
      choices: ['us-east-1', 'eu-central-1', 'ap-south-1', 'me-central-1', 'eu-west-2', 'ap-southeast-2', 'ca-central-1', 'me-central2-b'],
      description: 'The region to update resources for'
    )

    string (
      name: 'reason',
      defaultValue: 'No reason provided',
      description: 'The reason for updating the resources'
    )
  }

  stages {
    stage('INITIALIZE_PIPELINE') {
      steps {
        script {
          variables = load 'Helpers/variables.groovy'
          notifier   = load 'Helpers/notifications.groovy'
          methods   = load 'Helpers/methods.groovy'

          service   = env.JOB_NAME.split('/')[0]
          layer     = params.layer
          appName   = variables.REGION_APP_MAP[params.region]

          setNotificationParams()
          if (variables.APPLICATIONS.contains(layer) || variables.SCALED_DOWN_APPLICATIONS.contains(layer)) {
            yamlFile = 'app.yaml'
          } else if (variables.WORKERS.contains(layer) || variables.SCALED_DOWN_WORKERS.contains(layer)) {
            yamlFile = 'worker.yaml'
          } else {
            error "Invalid layer specified."
          }

          if (service == 'App-v1') {
            yamlPath         = "app-v1/kubectl/${params.region}/blue-green-templates/${yamlFile}"
            deploymentName   = "${appName}-${layer}"
            deploymentParams = methods.getDeploymentParameters('green', params.region)
          } else if (service == 'MicroServices') {
            microService     = env.JOB_NAME.split('/')[1]
            microServiceVars = variables.SOC[microService][variables.DEFAULT_SOC_DEPLOYMENT]
            yamlPath         = "app-v1/kubectl/${params.region}/${microServiceVars.shortName}/${yamlFile}"
            deploymentName   = "${appName}-${microServiceVars.shortName}-${layer}"
            notificationParams.service = microService
          } else {
            error "Invalid service - ${service}. Please check with the DevOps team."
          }

          notifier.sendOpsRelatedNotifcation('STARTED')
        }
      }
    }

    stage('GET_DEPLOYMENT') {
      steps {
        script {
          dir (variables.EKS_REPO_FOLDER) {
            methods.cloneEKSYAMLRepo(variables.KUBERNETES_YAML_BRANCH)
            def yamlData = readYaml file: yamlPath

            if (yamlData instanceof Map) {
              if (yamlData.spec?.template?.metadata?.labels?.app == deploymentName) {
                deployment = yamlData
              } else {
                error "No deployment found with app name: ${deploymentName}"
              }
            } else {
              def matchingDeployment = yamlData.findAll { deployment ->
                deployment.spec?.template?.metadata?.labels?.app == deploymentName
              }

              if (matchingDeployment.isEmpty()) {
                error "No deployment found with app name: ${deploymentName}"
              } else {
                deployment = matchingDeployment[0]
              }
            }
          }
        }
      }
    }

    stage('APPLY_DEPLOYMENT') {
      steps {
        script {
          dir(variables.EKS_REPO_FOLDER) {
            writeYaml file: "${deploymentName}.yaml", data: deployment, overwrite: true

            methods.authenticateToK8sCluster(params.region, variables.CLUSTER_NAME_REGION_MAP[params.region])
            if (service == 'App-v1') {
              methods.createAppDeployments(deploymentName, deploymentParams.deployment, deploymentParams.app, deploymentParams.image)
            } else if (service == 'MicroServices') {
              methods.createAppDeployments(deploymentName)
            } else {
              error "Invalid service. Please check with the DevOps team."
            }
          }
        }
      }
    }
  }

  post {
    always {
      cleanWs()
    }

    success {
      script {
        notifier.sendOpsRelatedNotifcation('SUCCESS')
      }
    }

    failure {
      script {
        notifier.sendOpsRelatedNotifcation('FAILED')
      }
    }
  }
}