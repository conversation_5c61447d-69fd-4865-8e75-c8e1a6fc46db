# EKSApplicationRestartDeployment Pipeline

A pipeline to run restart to multiple regions in EKS

## Input to Pipeline
  - Region - eu-central-1, ap-south-1, me-central-1
  - Layer - api, application, eui, hulk-worker, reports, generic-worker, cron-worker, notification-worker, all
  - Stack - Blue, Green
  - reason

## Pipeline Steps
  - Executing Restart
    - Authenticate using kubectl and restart the kubernetes deployment object.
