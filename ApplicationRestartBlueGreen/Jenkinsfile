pipeline {
  agent { label "JenkinsWorkerFleet" }

  parameters {
    choice (
      choices: [ 'ap-south-1',  'eu-central-1', 'eu-west-2', 'me-central-1', 'us-east-1', 'me-central2-b' ],
      name: 'region',
      description: 'Specify which region to run restart'
    )

    choice (
      choices: [ 'green', 'blue' ],
      name: 'stack',
      description: 'Specify which stack to run restart'
    )

    choice (
      choices: ['api', 'application', 'eui', 'hulk-worker', 'reports', 'generic-worker', 'cron-worker', 'notification-worker', 'all'],
      name: 'layer',
      description: 'Specify which layer to run restart'
    )

    string (
      name: 'reason',
      description: 'Specify the reason for restart'
    )
  }

  stages {
    stage('Executing Restart') {

      steps {
        script {
          notifier   = load 'Helpers/notifications.groovy'
          variables = load 'Helpers/variables.groovy'
          methods   = load 'Helpers/methods.groovy'

          approver = input (
            message: "Are you sure to restart - ${params.layer} - ${params.stack} in ${params.region} ?",
            submitterParameter: 'APPROVER'
          )

          notifier.sendSlackNotification('APP_RESTART_STARTED')

          deploymentParams = methods.getDeploymentParameters(params.stack, params.region)

          appName     = variables.REGION_APP_MAP[params.region]
          clusterName = variables.CLUSTER_NAME_REGION_MAP[params.region]

          methods.authenticateToK8sCluster(params.region, clusterName)
          if (params.layer == 'all') {
            def deployments = variables.SCALED_DOWN_DCS.contains(params.region) ? variables.SCALED_DOWN_DEPLOYMENTS : variables.DEPLOYMENTS
            def restarts = [:]
            for (def i = 0; i < deployments.size(); i++) {
              def deployment     = deployments[i]
              def deploymentName = appName + '-' + deployment + "-${deploymentParams.deployment}"
              restarts[deploymentName] = {
                methods.restartDeployment(deploymentName, variables.DEFAULT_NAMESPACE)
              }
            }

            parallel restarts

          } else {
            deploymentName = "${appName}-${params.layer}-${deploymentParams.deployment}"
            methods.restartDeployment(deploymentName, variables.DEFAULT_NAMESPACE)
          }
        }
      }
    }
  }

  post {
    always {
      cleanWs()
    }

    success {
      script {
        notifier.sendSlackNotification('APP_RESTART_SUCCESSFUL')
      }
    }

    failure {
      script {
        notifier.sendSlackNotification('APP_RESTART_FAILED')
      }
    }

    aborted {
      script {
        notifier.sendSlackNotification('APP_RESTART_ABORTED')
      }
    }
  }
}