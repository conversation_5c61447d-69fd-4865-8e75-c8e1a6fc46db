properties(
  [
    disableConcurrentBuilds(),
    buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '5', numToKeepStr: '5'))

  ]
)

pipeline {
  agent {
    label 'JenkinsWorkerFleet'
  }

  parameters {
    choice (
      choices: ['us-east-1', 'eu-central-1', 'ap-south-1', 'me-central-1', 'eu-west-2', 'me-central2-b'],
      description: 'Select the region to update config.',
      name: 'region'
    )

    string (
      description: 'Please provide the reason for this config update.',
      name: 'reason'
    )

    booleanParam (
      defaultValue: true,
      description: 'Check this if you want to restart the pods',
      name: 'restart'
    )
  }

  stages {
    stage('INITIALIZE_CONFIG_UPDATE') {
      steps {
        script {
          notifier   = load 'Helpers/notifications.groovy'
          variables = load 'Helpers/variables.groovy'
          methods   = load 'Helpers/methods.groovy'

          methods.prepInstance()

          currentBuild.displayName = "${params.region}"
          currentBuild.description = "${params.reason} | restarted - ${params.restart}"

          microService        = env.JOB_NAME.split('/')[1]
          microServiceVars    = variables.SOC[microService]['backend']
          appDeploymentParams = methods.getDeploymentParameters('green', params.region)
          version             = appDeploymentParams.version

          regionName = variables.REGION_DC_NAME_MAP[params.region]
          if (variables.SOC_JOB_NAMES_MAP[microService][regionName] == null) {
            error "Microservice - ${microService} is not present in ${regionName}"
          }

          notifier.sendSlackNofiticationMicroservices('SOC_CONFIG_UPDATE_STARTED')
        }
      }
    }

    stage('CONFIG_UPDATE') {
      steps {
        script {
          methods.updateMicroServiceConfig(params.region, microServiceVars.shortName)
        }
      }
    }

    stage('RESTART_CONTAINERS') {
      when {
        expression { params.restart }
      }

      steps {
        script {
          methods.authenticateToK8sCluster(params.region, variables.CLUSTER_NAME_REGION_MAP[params.region])

          def deployments = [];
          def appLayers    = microServiceVars.dcLayerMap[params.region]?.appLayers
          def workerLayers = microServiceVars.dcLayerMap[params.region]?.workerLayers
          def appName      = variables.REGION_APP_MAP[params.region]

          if (workerLayers) {
            workerLayers.each { layer ->
              deployments.add("${appName}-${microServiceVars['shortName']}-${layer}")
            }
          }

          if (appLayers) {
            appLayers.each { layer ->
              deployments.add("${appName}-${microServiceVars['shortName']}-${layer}")
            }
          }

          for (def deployment in deployments) {
            methods.restartDeployment(deployment, variables.DEFAULT_NAMESPACE)
          }
        }
      }
    }
  }

  post {
    always {
      cleanWs()
    }

    success {
      script {
        notifier.sendSlackNofiticationMicroservices('SOC_CONFIG_UPDATE_SUCCESSFUL')
      }
    }

    failure {
      script {
        notifier.sendSlackNofiticationMicroservices('SOC_CONFIG_UPDATE_FAILED')
      }
    }

    aborted {
      script {
        notifier.sendSlackNofiticationMicroservices('SOC_CONFIG_UPDATE_ABORTED')
      }
    }
  }
}