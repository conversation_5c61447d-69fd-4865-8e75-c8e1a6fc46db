def setNotificationParams() {
  wrap([$class: 'BuildUser']) {
    // setting this globally for this pipeline
    notificationParams = [
      'pipeline': "Android deployment",
      'Branch': variables.PRODUCTION_BRANCH,
      'Bump Type': params.bumpType,
      'Started By': env.BUILD_USER,
      'Release Note': params.releaseNote
    ]
  }
}

properties(
    [
        parameters(
            [
                choice(
                    choices: ['patch', 'minor', 'major'],
                    description: 'Enter the Release type:', 
                    name: 'bumpType'
                ),
                text(
                    defaultValue: 'Production Next Release', 
                    description: 'The detailed features about this Release:', 
                    name: 'releaseNote'
                )
            ]
        )
    ]
)

pipeline  {

    agent { label 'AndroidBuilderFleet' }

    stages {
        stage("Git clone") {
            steps {
                script {
                    variables = load "Helpers/variables.groovy"
                    notifier  = load "Helpers/notifications.groovy"
                    methods   = load "Helpers/methods.groovy"

                    currentBuild.displayName = "Deploying: ${variables.PRODUCTION_BRANCH}"
                    currentBuild.description = "${variables.PRODUCTION_BRANCH} - Deployment On Going"
                    setNotificationParams()

                    dir('application') {
                        methods.cloneRepo(variables.OFFLINE_APP_REPO, variables.PRODUCTION_BRANCH)
                    }

                    dir('keys') {
                        methods.cloneConfig()
                        sh "cp mobile/offline-app/* ../application/"
                    }

                }
            }
        }

        stage("Installing Dependencies") {
            steps {
                script {
                    dir('application') {
                        releaseVersion = sh (script: "aws ssm get-parameter --region ${variables.DEFAULT_REGION} --name '${variables.OFFLINE_RELEASE_SSM_PATH}' --query 'Parameter.Value' --output text", returnStdout: true).trim()
                        bundleVersion  = sh (script: "aws ssm get-parameter --region ${variables.DEFAULT_REGION} --name '${variables.OFFLINE_BUNDLE_SSM_PATH}' --query 'Parameter.Value' --output text", returnStdout: true).trim()
                        
                        sh "sed -i 's/versionName.*/$releaseVersion/g' android/app/build.gradle"
                        sh "sed -i 's/versionCode.*/versionCode $bundleVersion/g' android/app/build.gradle"

                        sh "yarn install"
                        sh "bundle install"
                    }
                }
            }
        }

        stage("Deployment of Android Bundle") {
            steps {
                dir('application') {
                    script {
                        sh "bundle exec fastlane ${variables.ANDROID_DEFAULT_LANE} env:${variables.ANDROID_DEFAULT_BUILD_ENVIRONMENT} test_note:'${params.releaseNote}' bump_type:${params.bumpType} release_note:'${params.releaseNote}'"

                        newVersion = sh (script: "cat android/app/build.gradle | grep versionName | sed 's/^ *//;s/ *\$//'", returnStdout: true).trim()
                        newBundleVersion = bundleVersion.toInteger() + 1
                        
                        sh "aws ssm put-parameter --region ${variables.DEFAULT_REGION} --name '${variables.OFFLINE_RELEASE_SSM_PATH}' --value '${newVersion}' --type String --overwrite"
                        sh "aws ssm put-parameter --region ${variables.DEFAULT_REGION} --name '${variables.OFFLINE_BUNDLE_SSM_PATH}' --value '${newBundleVersion}' --type String --overwrite"
                    }
                }
            }
        }
    }

    post {
        always {
            cleanWs()
        }
        success  {
            script {
                currentBuild.displayName = "Deployed: ${variables.PRODUCTION_BRANCH}"
                currentBuild.description = "Branch: ${variables.PRODUCTION_BRANCH} - Deployment Success"
                notifier.sendOpsRelatedNotifcationToChannel("SUCCESS", variables.OFFLINE_ANDROID_DEPLOYMENT_CHANNEL)
            }
        }

        failure {
            script {
                currentBuild.displayName = "Failure: ${variables.PRODUCTION_BRANCH}"
                currentBuild.description = "Branch: ${variables.PRODUCTION_BRANCH} - Deployment Failed"
                notifier.sendOpsRelatedNotifcationToChannel("FAILURE", variables.OFFLINE_ANDROID_DEPLOYMENT_CHANNEL)
            }
        }
        aborted {
            script {
                currentBuild.displayName = "Aborted: ${variables.PRODUCTION_BRANCH}"
                currentBuild.description = "Branch: ${variables.PRODUCTION_BRANCH} - Deployment Aborted"
                notifier.sendOpsRelatedNotifcationToChannel("ABORTED", variables.OFFLINE_ANDROID_DEPLOYMENT_CHANNEL)
            }
        }
    }
}

