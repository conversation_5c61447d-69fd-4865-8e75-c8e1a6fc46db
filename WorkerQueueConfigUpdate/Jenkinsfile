pipeline {
  agent { label "JenkinsWorkerFleet" }

  parameters {
    choice (
      choices: [ 'ap-south-1', 'eu-central-1', 'me-central-1', 'us-east-1', 'me-central2-b' ],
      name: 'region',
      description: 'Specify which region to update workers'
    )

    choice (
      choices: [ 'green', 'blue' ],
      name: 'stack',
      description: 'Specify which stack to update workers'
    )

    string (
      name: 'reason',
      description: 'Specify the reason to update the worker configuration'
    )
  }

  stages {
    stage('Updating Worker') {

      steps {
        script {
          notifier   = load 'Helpers/notifications.groovy'
          variables = load 'Helpers/variables.groovy'
          methods   = load 'Helpers/methods.groovy'

          methods.prepInstance()

          notifier.sendSlackNotification('WORKER_CONFIG_UPDATE_STARTED')

          deploymentParams = methods.getDeploymentParameters(params.stack, params.region)
          clusterName      = variables.CLUSTER_NAME_REGION_MAP[params.region]
          appName          = variables.REGION_APP_MAP[params.region]

          dir ('surveysparrow-production-eks') {
            methods.cloneEKSYAMLRepo(variables.KUBERNETES_YAML_BRANCH)
            methods.authenticateToK8sCluster(params.region, clusterName)

            dir ("app-v1/kubectl/${params.region}/blue-green-templates") {
              genericWorkerDeployment = "${appName}-generic-worker-${deploymentParams.deployment}"
              if (methods.checkIfDeploymentExists(genericWorkerDeployment, variables.DEFAULT_NAMESPACE)) {
                tagFromDeployment   = sh (script: "kubectl get deployment ${genericWorkerDeployment} -n ${variables.DEFAULT_NAMESPACE} -o json | jq '.metadata.labels.tag' | tr -d '\n\"'", returnStdout: true)
                imageFromDeployment = sh (script: "kubectl get deployment ${genericWorkerDeployment} -n ${variables.DEFAULT_NAMESPACE} -o json | jq '.spec.template.spec.containers[0].image'| cut -d: -f2 | tr -d '\n\"'", returnStdout: true)

                if ((tagFromDeployment != deploymentParams.app) || (imageFromDeployment != deploymentParams.image)) {
                  echo "Deployment failed due to one of the reasons\n"
                  echo "1.Deployment ${genericWorkerDeployment} is running on tag ${tagFromDeployment} but you are trying to update with tag ${deploymentParams.app}. Please update the tag in deployment file and try again."
                  echo "2.Deployment ${genericWorkerDeployment} is running on image ${imageFromDeployment} but you are trying to update with image ${deploymentParams.image}. Please update the image in deployment file and try again."
                  echo "PARAMS FOR DEBUGGING - DeploymentParams - ${deploymentParams.app} ${deploymentParams.image}\n Running Params - ${tagFromDeployment} ${imageFromDeployment}"
                  error "Deployment failed!"
                } else {
                  sh "STACK=${deploymentParams.deployment} TAG=${deploymentParams.app} IMAGE_TAG=${deploymentParams.image} envsubst < worker.yaml | kubectl apply -f -"
                }
              }
            }
          }
        }
      }
    }
  }

  post {
    always {
      cleanWs()
    }

    success {
      script {
        notifier.sendSlackNotification('WORKER_CONFIG_UPDATE_SUCCESSFUL')
      }
    }

    failure {
      script {
        notifier.sendSlackNotification('WORKER_CONFIG_UPDATE_FAILED')
      }
    }

    aborted {
      script {
        notifier.sendSlackNotification('WORKER_CONFIG_UPDATE_ABORTED')
      }
    }
  }
}