properties(
    [
        buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '1', numToKeepStr: '2')),
        parameters (
            [
                choice(name: "runType", choices: ["Create User",  "Update Password", "Delete User"], description: 'User Provisioning Method'),
                string(
                    description: 'Target user\'s email address', 
                    name: 'email'
                ),
                choice(name: "bastionUser", choices: ["Do Nothing", "Create Bastion User", "Update SSH", "Delete User"], description: 'Bastion User Management'),
                reactiveChoice(
                    choiceType: 'PT_MULTI_SELECT',
                    description: 'Select the regions (multiple regions can be selected)',
                    filterLength: 1,
                    filterable: false,
                    name: 'regions',
                    randomName: 'choice-parameter-regions-multiselect',
                    referencedParameters: '',
                    script: groovyScript(
                        fallbackScript: [
                            classpath: [],
                            oldScript: '',
                            sandbox: true,
                            script: 'return["us-east-1"]'
                        ],
                        script: [
                            classpath: [],
                            oldScript: '',
                            sandbox: true,
                            script: '''
                                return ["us-east-1", "ap-south-1", "eu-central-1", "eu-west-2", "me-central-1", "ap-southeast-2", "ca-central-1"]
                            '''
                        ]
                    )
                ),
                activeChoiceHtml(
                    choiceType: 'ET_FORMATTED_HTML',
                    name: 'sshkey',
                    omitValueField: false,
                    randomName: 'choice-parameter-129039043904949042390',
                    referencedParameters: 'bastionUser',
                    script: groovyScript(
                        fallbackScript: [
                            classpath: [],
                            oldScript: '',
                            sandbox: true,
                            script: 'return "<h1>Error</h1>"'
                        ],
                        script: [
                            classpath: [],
                            oldScript: '',
                            sandbox: true,
                            script: '''if (bastionUser.equals(\'Create Bastion User\') || bastionUser.equals(\'Update SSH\')) {
                            return """
                            <div class=\'jenkins-form-description\'>Public SSH Key (~/ssh/id_rsa.pub) [Not required for deleting the user]</div><br>
                            <input name=\'value\' type=\'text\' value=\'\' class=\'jenkins-input\' required><br>
                            """
                            } else {
                               return """
                               <h2>Not required for deleting the user or Do Nothing</h2>
                               """
                            }'''
                        ]
                    )
                ),
                reactiveChoice(
                    choiceType: 'PT_MULTI_SELECT', description: 'Select the Postgres Shards (aggregated from all selected regions)', filterLength: 1,
                    filterable: false, name: 'postgresShards',
                    randomName: 'choice-parameter-6116297949077901', referencedParameters: 'regions',
                    script: groovyScript(
                        fallbackScript: [
                            classpath: [],
                            oldScript: '', sandbox: true,
                            script: 'return[\'ERROR\']'
                        ],
                        script: [
                            classpath: [],
                            oldScript: '',
                            sandbox: true,
                            script: '''
                                def regionShardMap = [
                                    "us-east-1": ["ss-us-vi-shard-1", "ss-us-vi-shard-2"],
                                    "ap-south-1": ["ss-app-production-ap-mumbai"],
                                    "eu-central-1": ["ss-app-production-eu-frankfurt-cluster"],
                                    "me-central-1": ["ss-app-production-me-uae"],
                                    "eu-west-2": ["ss-app-production-eu-london"],
                                    "ap-southeast-2": ["ss-app-production-sy"],
                                    "ca-central-1": ["ss-app-production-ca"]
                                ]

                                def selectedRegions = regions.split(",")
                                def allShards = []

                                selectedRegions.each { region ->
                                    region = region.trim()
                                    if (regionShardMap.containsKey(region)) {
                                        allShards.addAll(regionShardMap[region])
                                    }
                                }

                                return allShards.unique()
                            '''
                        ]
                    )
                ),
                reactiveChoice(
                    choiceType: 'PT_MULTI_SELECT', description: 'Select the OpenSearch Domains (aggregated from all selected regions)', filterLength: 1,
                    filterable: false, name: 'openSearchDomains',
                    randomName: 'choice-parameter-61162979077901', referencedParameters: 'regions',
                    script: groovyScript(
                        fallbackScript: [
                            classpath: [],
                            oldScript: '', sandbox: true,
                            script: 'return[\'ERROR\']'
                        ],
                        script: [
                            classpath: [],
                            oldScript: '',
                            sandbox: true,
                            script: '''
                                def regionDomainMap = [
                                    "us-east-1": ["ss-us-vi-opensearch-logs", "ss-us-vi-opensearch-submissions", "ss-us-vi-elasticsearch"],
                                    "ap-south-1": ["ss-prod-elasticsearch-ap", "ss-production-logs-ap"],
                                    "eu-central-1": ["ss-opensearch-eu-logs", "ss-opensearch-eu-submission", "ss-prod-elasticsearch-eu"],
                                    "me-central-1": ["ss-prod-elasticsearch-me", "ss-production-logs-me"],
                                    "eu-west-2": ["ss-prod-elasticsearch-ln", "ss-production-logs-ln"],
                                    "ap-southeast-2": ["ss-production-opensearch-sy"],
                                    "ca-central-1": ["ss-production-opensearch-ca"]
                                ]

                                def selectedRegions = regions.split(",")
                                def allDomains = []

                                selectedRegions.each { region ->
                                    region = region.trim()
                                    if (regionDomainMap.containsKey(region)) {
                                        allDomains.addAll(regionDomainMap[region])
                                    }
                                }

                                return allDomains.unique()
                            '''
                        ]
                    )
                ),
                reactiveChoice(
                    choiceType: 'PT_SINGLE_SELECT', 
                    filterLength: 1, 
                    description: 'Provide the validity in days (Required for Bastion User Creation)',
                    filterable: false, 
                    name: 'bastionValidity', 
                    referencedParameters: 'bastionUser',
                    randomName: 'choice-parameter-2982390329343423223', 
                    script: 
                    [
                        $class: 'GroovyScript', 
                        fallbackScript: [
                            classpath: [], 
                            oldScript: '', 
                            sandbox: true, 
                            script: ''
                        ], 
                        script: [
                            classpath: [], 
                            oldScript: '', 
                            sandbox: true, 
                            script: '''
                                if(bastionUser.equals('Create Bastion User'))
                                {
                                    return ["1", "2", "3", "4", "5", "7", "14"]
                                }
                                '''
                        ]
                    ]
                ),
                reactiveChoice(
                    choiceType: 'PT_SINGLE_SELECT', 
                    filterLength: 1, 
                    description: 'Provide the validity in days (Required for Other User Creation)',
                    filterable: false, 
                    name: 'validity', 
                    referencedParameters: 'runType',
                    randomName: 'choice-parameter-29823903293343423223', 
                    script: 
                    [
                        $class: 'GroovyScript', 
                        fallbackScript: [
                            classpath: [], 
                            oldScript: '', 
                            sandbox: true, 
                            script: ''
                        ], 
                        script: [
                            classpath: [], 
                            oldScript: '', 
                            sandbox: true, 
                            script: '''
                                if(runType.equals('Create User'))
                                {
                                    return ["1", "2", "3", "4", "5", "7", "14"]
                                }
                                '''
                        ]
                    ]
                )
            ],
        )
    ]
)

pipeline {
    agent {
        label "JenkinsWorkerFleet"
    }

    stages {
        stage("Initial Setup") {
            steps {
                script {
                    variables       = load 'Helpers/variables.groovy'
                    notification    = load 'Helpers/notifications.groovy'
                    methods         = load 'Helpers/methods.groovy'

                    ssh_key         = (params.sshkey == "") ? "" : params.sshkey.replace(",", "")
                    username        = params.email.replace("@surveysparrow.com", "")
                    username        = username.replace(".", "")
                    hosts           = params.postgresShards.replaceAll(",", " ")
                    domains         = params.openSearchDomains.replaceAll(",", " ")
                    approved_by     = env.BUILD_USER

                    // Process selected regions
                    selectedRegions = params.regions.split(",").collect { it.trim() }
                    echo "Selected regions: ${selectedRegions.join(', ')}"

                    if (params.validity != "") {
                        valid_till      = methods.getValidTill(params.validity.toInteger())
                    }
                    if (params.bastionValidity != "") {
                        bastion_valid_till = methods.getValidTill(params.bastionValidity.toInteger())
                    }

                    // Define helper function for formatting OpenSearch output
                    formatOpenSearchOutput = { output, region ->
                        def lines = output.split('\n')
                        def formattedLines = []

                        formattedLines.add("<h3>🌍 Region: ${region}</h3>")
                        formattedLines.add("<div style='margin-left: 20px; background-color: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>")

                        lines.each { line ->
                            line = line.trim()
                            if (line.contains("Created opensearch User")) {
                                formattedLines.add("<h4 style='color: #28a745; margin: 0 0 10px 0;'>✅ ${line}</h4>")
                            } else if (line.contains("User name:")) {
                                formattedLines.add("<p style='margin: 5px 0;'><strong>👤 ${line}</strong></p>")
                            } else if (line.contains("Password:")) {
                                formattedLines.add("<p style='margin: 5px 0;'><strong>🔑 ${line}</strong></p>")
                            } else if (line.contains("Host Address:")) {
                                def parts = line.split("Host Address: ")
                                if (parts.length > 1) {
                                    def hostInfo = parts[1]
                                    if (hostInfo.contains("(") && hostInfo.contains(")")) {
                                        def urlPart = hostInfo.substring(0, hostInfo.indexOf("(")).trim()
                                        def domainPart = hostInfo.substring(hostInfo.indexOf("(") + 1, hostInfo.indexOf(")")).trim()
                                        formattedLines.add("<p style='margin: 5px 0;'><strong>🌐 Host Address:</strong></p>")
                                        formattedLines.add("<p style='margin: 5px 0 5px 20px;'>📍 <strong>Domain:</strong> ${domainPart}</p>")
                                        formattedLines.add("<p style='margin: 5px 0 5px 20px;'>🔗 <strong>URL:</strong> <a href='${urlPart}' target='_blank'>${urlPart}</a></p>")
                                    } else {
                                        formattedLines.add("<p style='margin: 5px 0;'><strong>🌐 ${line}</strong></p>")
                                    }
                                }
                            } else if (line && !line.isEmpty()) {
                                formattedLines.add("<p style='margin: 5px 0;'>${line}</p>")
                            }
                        }

                        formattedLines.add("</div>")
                        return formattedLines.join("")
                    }

                    // Define helper function for formatting Postgres output
                    formatPostgresOutput = { output, region ->
                        def lines = output.split('\n')
                        def formattedLines = []

                        formattedLines.add("<h3>🌍 Region: ${region}</h3>")
                        formattedLines.add("<div style='margin-left: 20px; background-color: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff;'>")

                        lines.each { line ->
                            line = line.trim()
                            if (line.contains("Created postgres User") || line.contains("Updated postgres User") || line.contains("Deleted postgres User")) {
                                formattedLines.add("<h4 style='color: #007bff; margin: 0 0 10px 0;'>✅ ${line}</h4>")
                            } else if (line.contains("User name:")) {
                                formattedLines.add("<p style='margin: 5px 0;'><strong>👤 ${line}</strong></p>")
                            } else if (line.contains("Password:")) {
                                formattedLines.add("<p style='margin: 5px 0;'><strong>🔑 ${line}</strong></p>")
                            } else if (line.contains("Host Address:") || line.contains("Database:")) {
                                formattedLines.add("<p style='margin: 5px 0;'><strong>🗄️ ${line}</strong></p>")
                            } else if (line.contains("Port:")) {
                                formattedLines.add("<p style='margin: 5px 0;'><strong>🔌 ${line}</strong></p>")
                            } else if (line && !line.isEmpty()) {
                                formattedLines.add("<p style='margin: 5px 0;'>${line}</p>")
                            }
                        }

                        formattedLines.add("</div>")
                        return formattedLines.join("")
                    }
                }
            }
        }

        stage("Bastion User") {
            when {
                expression { params.bastionUser != "Do Nothing" }
            }
            steps {
                script {
                    def bastionResults = []
                    sshagent([variables.BASTION_SSH_CREDENTIAL_ID]) {
                        selectedRegions.each { region ->
                            echo "Processing bastion user operation for region: ${region}"
                            try {
                                if (params.bastionUser.equals("Create Bastion User")){
                                    ssh_key=params.sshkey.replace(",", "")
                                    sh "ssh -o StrictHostKeyChecking=no ${variables.BASTION_USER}@${variables.BASTION_NODES[region]} bastion --no-color user create -u ${username} -H ${variables.BASTION_NODES[region]} --key ${ssh_key} --group non-sudoer --validity ${params.bastionValidity}"
                                    bastionResults.add("✅ ${region}: Bastion user created successfully")
                                } else if (params.bastionUser.equals("Update SSH")){
                                    ssh_key=params.sshkey.replace(",", "")
                                    sh "ssh -o StrictHostKeyChecking=no ${variables.BASTION_USER}@${variables.BASTION_NODES[region]} bastion --no-color user update -u ${username} -H ${variables.BASTION_NODES[region]} -k ${ssh_key}"
                                    bastionResults.add("✅ ${region}: SSH key updated successfully")
                                } else if (params.bastionUser.equals("Delete User")){
                                    sh "ssh -o StrictHostKeyChecking=no ${variables.BASTION_USER}@${variables.BASTION_NODES[region]} bastion --no-color user delete -u ${username} -H ${variables.BASTION_NODES[region]} --force"
                                    bastionResults.add("✅ ${region}: Bastion user deleted successfully")
                                }
                            } catch (Exception e) {
                                bastionResults.add("❌ ${region}: Failed - ${e.getMessage()}")
                                echo "Error in region ${region}: ${e.getMessage()}"
                            }
                        }
                    }

                    // Send consolidated notification
                    // Set global variables for notification system
                    env.regionsList = selectedRegions.join(', ')
                    env.resultsSummary = bastionResults.join('\n')

                    // Use single-region notifications for backward compatibility when only one region is selected
                    if (selectedRegions.size() == 1) {
                        // Set env.region for backward compatibility with single-region notifications
                        env.region = selectedRegions[0]
                        if (params.bastionUser.equals("Create Bastion User")) {
                            notification.sendStatusBasedNotification("BASTION_USER_SUCCESS", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        } else if (params.bastionUser.equals("Update SSH")) {
                            notification.sendStatusBasedNotification("BASTION_USER_UPDATE_INFO", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        } else if (params.bastionUser.equals("Delete User")) {
                            notification.sendStatusBasedNotification("BASTION_USER_DELETE_INFO", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        }
                    } else {
                        // Use multi-region notifications
                        if (params.bastionUser.equals("Create Bastion User")) {
                            notification.sendStatusBasedNotification("BASTION_USER_SUCCESS_MULTI", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        } else if (params.bastionUser.equals("Update SSH")) {
                            notification.sendStatusBasedNotification("BASTION_USER_UPDATE_INFO_MULTI", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        } else if (params.bastionUser.equals("Delete User")) {
                            notification.sendStatusBasedNotification("BASTION_USER_DELETE_INFO_MULTI", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        }
                    }
                }
            }
        }

        stage("Postgres User Provisioning") {
            when {
                expression { params.postgresShards != "" }
            }
            steps {
                script {
                    def postgresResults = []
                    def allOutputMessages = []

                    // Create region to shard mapping for selected shards
                    def regionShardMap = [
                        "us-east-1": ["ss-us-vi-shard-1", "ss-us-vi-shard-2"],
                        "ap-south-1": ["ss-app-production-ap-mumbai"],
                        "eu-central-1": ["ss-app-production-eu-frankfurt-cluster"],
                        "me-central-1": ["ss-app-production-me-uae"],
                        "eu-west-2": ["ss-app-production-eu-london"],
                        "ap-southeast-2": ["ss-app-production-sy"],
                        "ca-central-1": ["ss-app-production-ca"]
                    ]

                    def selectedShards = params.postgresShards.split(",").collect { it.trim() }

                    sshagent([variables.BASTION_SSH_CREDENTIAL_ID]) {
                        selectedRegions.each { region ->
                            // Find shards for this region that are in the selected list
                            def regionShards = regionShardMap[region]?.findAll { shard ->
                                selectedShards.contains(shard)
                            }

                            if (regionShards && !regionShards.isEmpty()) {
                                def regionHosts = regionShards.join(" ")
                                echo "Processing Postgres operation for region: ${region}, shards: ${regionHosts}"

                                try {
                                    def output_message = ""
                                    if (params.runType == "Create User") {
                                        output_message = sh script: "ssh -o StrictHostKeyChecking=no ${variables.BASTION_USER}@${variables.BASTION_NODES[region]} bastion --no-color postgres create -u ${username} -H ${regionHosts} --access read --validity ${params.validity}", returnStdout: true
                                        postgresResults.add("✅ ${region}: Postgres user created successfully")
                                    } else if (params.runType == "Update Password") {
                                        output_message = sh script: "ssh -o StrictHostKeyChecking=no ${variables.BASTION_USER}@${variables.BASTION_NODES[region]} bastion --no-color postgres update -u ${username} -H ${regionHosts}", returnStdout: true
                                        postgresResults.add("✅ ${region}: Postgres password updated successfully")
                                    } else if (params.runType == "Delete User") {
                                        output_message = sh script: "ssh -o StrictHostKeyChecking=no ${variables.BASTION_USER}@${variables.BASTION_NODES[region]} bastion --no-color postgres delete -u ${username} -H ${regionHosts}", returnStdout: true
                                        postgresResults.add("✅ ${region}: Postgres user deleted successfully")
                                    }
                                    // Format the output message for better readability
                                    def formattedOutput = formatPostgresOutput(output_message, region)
                                    allOutputMessages.add(formattedOutput)
                                } catch (Exception e) {
                                    postgresResults.add("❌ ${region}: Failed - ${e.getMessage()}")
                                    allOutputMessages.add("<h3>🌍 Region: ${region}</h3><div style='margin-left: 20px; color: red;'><strong>❌ Error:</strong> ${e.getMessage()}</div>")
                                    echo "Error in region ${region}: ${e.getMessage()}"
                                }
                            }
                        }
                    }

                    // Send consolidated notification and email
                    // Set global variables for notification system
                    env.regionsList = selectedRegions.join(', ')
                    env.resultsSummary = postgresResults.join('\n')

                    // Use single-region notifications for backward compatibility when only one region is selected
                    if (selectedRegions.size() == 1) {
                        // Set env.region for backward compatibility with single-region notifications
                        env.region = selectedRegions[0]
                        if (params.runType == "Create User") {
                            subject = "Your account is created on Postgres"
                            notification.sendStatusBasedNotification("BASTION_POSTGRES_USER_SUCCESS", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        } else if (params.runType == "Update Password") {
                            subject = "Your account password is updated on Postgres"
                            notification.sendStatusBasedNotification("BASTION_POSTGRES_USER_UPDATE_INFO", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        } else if (params.runType == "Delete User") {
                            subject = "Your account is deleted on Postgres"
                            notification.sendStatusBasedNotification("BASTION_POSTGRES_USER_DELETE_INFO", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        }
                    } else {
                        // Use multi-region notifications
                        def regionCount = selectedRegions.size()
                        if (params.runType == "Create User") {
                            subject = "Your account is created on Postgres across ${regionCount} regions (${env.regionsList})"
                            notification.sendStatusBasedNotification("BASTION_POSTGRES_USER_SUCCESS_MULTI", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        } else if (params.runType == "Update Password") {
                            subject = "Your account password is updated on Postgres across ${regionCount} regions (${env.regionsList})"
                            notification.sendStatusBasedNotification("BASTION_POSTGRES_USER_UPDATE_INFO_MULTI", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        } else if (params.runType == "Delete User") {
                            subject = "Your account is deleted on Postgres across ${regionCount} regions (${env.regionsList})"
                            notification.sendStatusBasedNotification("BASTION_POSTGRES_USER_DELETE_INFO_MULTI", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        }
                    }

                    def consolidatedOutput = ""
                    if (allOutputMessages.size() > 0) {
                        // Create a beautiful email header
                        def emailHeader = """
                        <div style='font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto;'>
                            <div style='background: linear-gradient(135deg, #007bff, #0056b3); color: white; padding: 20px; border-radius: 10px 10px 0 0; text-align: center;'>
                                <h1 style='margin: 0; font-size: 24px;'>🗄️ PostgreSQL Access Management</h1>
                                <p style='margin: 10px 0 0 0; font-size: 16px;'>Multi-Region User Provisioning Results</p>
                            </div>
                            <div style='background-color: #ffffff; padding: 20px; border: 1px solid #dee2e6; border-top: none;'>
                                <div style='background-color: #e3f2fd; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>
                                    <h3 style='margin: 0 0 10px 0; color: #1976d2;'>📋 Operation Summary</h3>
                                    <p style='margin: 5px 0;'><strong>👤 Username:</strong> ${username}</p>
                                    <p style='margin: 5px 0;'><strong>🔄 Operation:</strong> ${params.runType}</p>
                                    <p style='margin: 5px 0;'><strong>🌍 Regions:</strong> ${env.regionsList}</p>
                                    <p style='margin: 5px 0;'><strong>⏰ Processed at:</strong> ${new Date().format('yyyy-MM-dd HH:mm:ss')} UTC</p>
                                </div>
                        """

                        def emailFooter = """
                            </div>
                            <div style='background-color: #f8f9fa; padding: 15px; border-radius: 0 0 10px 10px; text-align: center; border: 1px solid #dee2e6; border-top: none;'>
                                <p style='margin: 0; color: #6c757d; font-size: 14px;'>
                                    🔒 This is an automated message from SurveySparrow Infrastructure Team<br>
                                    For support, please contact the DevOps team
                                </p>
                            </div>
                        </div>
                        """

                        consolidatedOutput = emailHeader + allOutputMessages.join("<hr style='margin: 20px 0; border: none; border-top: 2px solid #e9ecef;'>") + emailFooter
                        emailext body: consolidatedOutput, mimeType: 'text/html', from: variables.FROM_EMAIL, replyTo: variables.REPLY_TO_EMAIL, subject: subject, to: params.email
                    }
                }
            }
        }

        stage("OpenSearch User Provisioning") {
            when {
                expression { params.openSearchDomains != "" }
            }
            steps {
                script {
                    def opensearchResults = []
                    def allOutputMessages = []

                    // Create region to domain mapping for selected domains
                    def regionDomainMap = [
                        "us-east-1": ["ss-us-vi-opensearch-logs", "ss-us-vi-opensearch-submissions", "ss-us-vi-elasticsearch"],
                        "ap-south-1": ["ss-prod-elasticsearch-ap", "ss-production-logs-ap"],
                        "eu-central-1": ["ss-opensearch-eu-logs", "ss-opensearch-eu-submission", "ss-prod-elasticsearch-eu"],
                        "me-central-1": ["ss-prod-elasticsearch-me", "ss-production-logs-me"],
                        "eu-west-2": ["ss-prod-elasticsearch-ln", "ss-production-logs-ln"],
                        "ap-southeast-2": ["ss-production-opensearch-sy"],
                        "ca-central-1": ["ss-production-opensearch-ca"]
                    ]

                    def selectedDomains = params.openSearchDomains.split(",").collect { it.trim() }

                    sshagent([variables.BASTION_SSH_CREDENTIAL_ID]) {
                        selectedRegions.each { region ->
                            // Find domains for this region that are in the selected list
                            def regionDomains = regionDomainMap[region]?.findAll { domain ->
                                selectedDomains.contains(domain)
                            }

                            if (regionDomains && !regionDomains.isEmpty()) {
                                def regionDomainsStr = regionDomains.join(" ")
                                echo "Processing OpenSearch operation for region: ${region}, domains: ${regionDomainsStr}"

                                try {
                                    def output_message = ""
                                    if (params.runType == "Create User") {
                                        output_message = sh script: "ssh -o StrictHostKeyChecking=no ${variables.BASTION_USER}@${variables.BASTION_NODES[region]} bastion --no-color opensearch create -u ${username} -H ${regionDomainsStr} --access read --validity ${params.validity}", returnStdout: true
                                        opensearchResults.add("✅ ${region}: OpenSearch user created successfully")
                                    } else if (params.runType == "Update Password") {
                                        output_message = sh script: "ssh -o StrictHostKeyChecking=no ${variables.BASTION_USER}@${variables.BASTION_NODES[region]} bastion --no-color opensearch update -u ${username} -H ${regionDomainsStr}", returnStdout: true
                                        opensearchResults.add("✅ ${region}: OpenSearch password updated successfully")
                                    } else if (params.runType == "Delete User") {
                                        output_message = sh script: "ssh -o StrictHostKeyChecking=no ${variables.BASTION_USER}@${variables.BASTION_NODES[region]} bastion --no-color opensearch delete -u ${username} -H ${regionDomainsStr}", returnStdout: true
                                        opensearchResults.add("✅ ${region}: OpenSearch user deleted successfully")
                                    }
                                    // Format the output message for better readability
                                    def formattedOutput = formatOpenSearchOutput(output_message, region)
                                    allOutputMessages.add(formattedOutput)
                                } catch (Exception e) {
                                    opensearchResults.add("❌ ${region}: Failed - ${e.getMessage()}")
                                    allOutputMessages.add("<h3>🌍 Region: ${region}</h3><div style='margin-left: 20px; color: red;'><strong>❌ Error:</strong> ${e.getMessage()}</div>")
                                    echo "Error in region ${region}: ${e.getMessage()}"
                                }
                            }
                        }
                    }

                    // Send consolidated notification and email
                    // Set global variables for notification system
                    env.regionsList = selectedRegions.join(', ')
                    env.resultsSummary = opensearchResults.join('\n')

                    // Use single-region notifications for backward compatibility when only one region is selected
                    if (selectedRegions.size() == 1) {
                        // Set env.region for backward compatibility with single-region notifications
                        env.region = selectedRegions[0]
                        if (params.runType == "Create User") {
                            subject = "Your account is created on OpenSearch"
                            notification.sendStatusBasedNotification("BASTION_OPENSEARCH_USER_SUCCESS", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        } else if (params.runType == "Update Password") {
                            subject = "Your account password is updated on OpenSearch"
                            notification.sendStatusBasedNotification("BASTION_OPENSEARCH_USER_UPDATE_INFO", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        } else if (params.runType == "Delete User") {
                            subject = "Your account is deleted on OpenSearch"
                            notification.sendStatusBasedNotification("BASTION_OPENSEARCH_USER_DELETE_INFO", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        }
                    } else {
                        // Use multi-region notifications
                        def regionCount = selectedRegions.size()
                        if (params.runType == "Create User") {
                            subject = "Your account is created on OpenSearch across ${regionCount} regions (${env.regionsList})"
                            notification.sendStatusBasedNotification("BASTION_OPENSEARCH_USER_SUCCESS_MULTI", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        } else if (params.runType == "Update Password") {
                            subject = "Your account password is updated on OpenSearch across ${regionCount} regions (${env.regionsList})"
                            notification.sendStatusBasedNotification("BASTION_OPENSEARCH_USER_UPDATE_INFO_MULTI", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        } else if (params.runType == "Delete User") {
                            subject = "Your account is deleted on OpenSearch across ${regionCount} regions (${env.regionsList})"
                            notification.sendStatusBasedNotification("BASTION_OPENSEARCH_USER_DELETE_INFO_MULTI", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        }
                    }

                    def consolidatedOutput = ""
                    if (allOutputMessages.size() > 0) {
                        // Create a beautiful email header for OpenSearch
                        def emailHeader = """
                        <div style='font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto;'>
                            <div style='background: linear-gradient(135deg, #28a745, #1e7e34); color: white; padding: 20px; border-radius: 10px 10px 0 0; text-align: center;'>
                                <h1 style='margin: 0; font-size: 24px;'>🔍 OpenSearch Access Management</h1>
                                <p style='margin: 10px 0 0 0; font-size: 16px;'>Multi-Region User Provisioning Results</p>
                            </div>
                            <div style='background-color: #ffffff; padding: 20px; border: 1px solid #dee2e6; border-top: none;'>
                                <div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>
                                    <h3 style='margin: 0 0 10px 0; color: #155724;'>📋 Operation Summary</h3>
                                    <p style='margin: 5px 0;'><strong>👤 Username:</strong> ${username}</p>
                                    <p style='margin: 5px 0;'><strong>🔄 Operation:</strong> ${params.runType}</p>
                                    <p style='margin: 5px 0;'><strong>🌍 Regions:</strong> ${env.regionsList}</p>
                                    <p style='margin: 5px 0;'><strong>⏰ Processed at:</strong> ${new Date().format('yyyy-MM-dd HH:mm:ss')} UTC</p>
                                </div>
                        """

                        def emailFooter = """
                            </div>
                            <div style='background-color: #f8f9fa; padding: 15px; border-radius: 0 0 10px 10px; text-align: center; border: 1px solid #dee2e6; border-top: none;'>
                                <p style='margin: 0; color: #6c757d; font-size: 14px;'>
                                    🔒 This is an automated message from SurveySparrow Infrastructure Team<br>
                                    For support, please contact the DevOps team
                                </p>
                            </div>
                        </div>
                        """

                        consolidatedOutput = emailHeader + allOutputMessages.join("<hr style='margin: 20px 0; border: none; border-top: 2px solid #e9ecef;'>") + emailFooter
                        emailext body: consolidatedOutput, mimeType: 'text/html', from: variables.FROM_EMAIL, replyTo: variables.REPLY_TO_EMAIL, subject: subject, to: params.email
                    }
                }
            }
        }
    }
    post {
        always {
            cleanWs()
        }
    }
}