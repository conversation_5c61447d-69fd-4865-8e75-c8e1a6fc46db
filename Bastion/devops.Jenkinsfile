properties(
    [
        buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '1', numToKeepStr: '2')),
        parameters (
            [
                choice(name: "runType", choices: ["Create User",  "Update Password", "Delete User"], description: 'User Provisioning Method'),
                string(
                    description: 'Target user\'s email address', 
                    name: 'email'
                ),
                choice(name: "bastionUser", choices: ["Do Nothing", "Create Bastion User", "Update SSH", "Delete User"], description: 'Bastion User Management'),
                choice(name: "region", choices: ["US-VIRGINIA", "EU-FRANKFURT", "AP-MUMBAI", "ME-UAE", "UK-LONDON", "CA-CANADA", "AU-SYDNEY"], description: 'Select the region'),
                activeChoiceHtml(
                    choiceType: 'ET_FORMATTED_HTML',
                    name: 'sshkey',
                    omitValueField: false,
                    randomName: 'choice-parameter-129039043904949042390',
                    referencedParameters: 'bastionUser',
                    script: groovyScript(
                        fallbackScript: [
                            classpath: [],
                            oldScript: '',
                            sandbox: true,
                            script: 'return "<h1>Error</h1>"'
                        ],
                        script: [
                            classpath: [],
                            oldScript: '',
                            sandbox: true,
                            script: '''if (bastionUser.equals(\'Create Bastion User\') || bastionUser.equals(\'Update SSH\')) {
                            return """
                            <div class=\'jenkins-form-description\'>Public SSH Key (~/ssh/id_rsa.pub) [Not required for deleting the user]</div><br>
                            <input name=\'value\' type=\'text\' value=\'\' class=\'jenkins-input\' required><br>
                            """
                            } else {
                               return """
                               <h2>Not required for deleting the user or Do Nothing</h2>
                               """
                            }'''
                        ]
                    )
                ),
                reactiveChoice(
                    choiceType: 'PT_MULTI_SELECT', description: 'Select the Postgres Shards', filterLength: 1, 
                    filterable: false, name: 'postgresShards',
                    randomName: 'choice-parameter-6116297949077901', referencedParameters: 'region', 
                    script: groovyScript(
                        fallbackScript: [
                            classpath: [], 
                            oldScript: '', sandbox: true, 
                            script: 'return[\'ERROR\']'
                        ], 
                        script: [
                            classpath: [], 
                            oldScript: '', 
                            sandbox: true, 
                            script: '''if (region.equals("US-VIRGINIA")) {
                                    return ["ss-us-vi-shard-1", "ss-us-vi-shard-2"]
                                    } else if (region.equals("AP-MUMBAI")) {
                                    return ["ss-app-production-ap-mumbai"]
                                    } else if (region.equals("EU-FRANKFURT")) {
                                    return ["ss-app-production-eu-frankfurt-cluster"]
                                    } else if (region.equals("ME-UAE")) {
                                    return ["ss-app-production-me-uae"]
                                    } else if (region.equals("UK-LONDON")) {
                                    return ["ss-app-production-eu-london"]
                                    } else if (region.equals("AU-SYDNEY")) {
                                    return ["ss-app-production-sy"]
                                    } else if (region.equals("CA-CANADA")) {
                                    return ["ss-app-production-ca"]
                                    }
                                '''
                        ]
                    )
                ),
                reactiveChoice(
                    choiceType: 'PT_MULTI_SELECT', description: 'Select the OpenSearch Domain', filterLength: 1, 
                    filterable: false, name: 'openSearchDomains',
                    randomName: 'choice-parameter-61162979077901', referencedParameters: 'region', 
                    script: groovyScript(
                        fallbackScript: [
                            classpath: [], 
                            oldScript: '', sandbox: true, 
                            script: 'return[\'ERROR\']' 
                        ], 
                        script: [
                            classpath: [], 
                            oldScript: '', 
                            sandbox: true, 
                            script: '''if (region.equals("US-VIRGINIA")) {
                                    return ["ss-us-vi-opensearch-logs", "ss-us-vi-opensearch-submissions", "ss-us-vi-elasticsearch"]
                                    } else if (region.equals("AP-MUMBAI")) {
                                    return ["ss-prod-elasticsearch-ap", "ss-production-logs-ap"]
                                    } else if (region.equals("EU-FRANKFURT")) {
                                    return ["ss-opensearch-eu-logs", "ss-opensearch-eu-submission", "ss-prod-elasticsearch-eu"]
                                    } else if (region.equals("ME-UAE")) {
                                    return ["ss-prod-elasticsearch-me", "ss-production-logs-me"]
                                    } else if (region.equals("UK-LONDON")) {
                                    return ["ss-prod-elasticsearch-ln", "ss-production-logs-ln"]
                                    } else if (region.equals("AU-SYDNEY")) {
                                    return ["ss-production-opensearch-sy"]
                                    } else if (region.equals("CA-CANADA")) {
                                    return ["ss-production-opensearch-ca"]
                                    }
                                '''
                        ]
                    )
                ),
                activeChoiceHtml(
                    choiceType: 'ET_FORMATTED_HTML',
                    name: 'bastionValidity',
                    omitValueField: false,
                    randomName: 'choice-parameter-129039043904934042390',
                    referencedParameters: 'bastionUser',
                    script: groovyScript(
                        fallbackScript: [
                            classpath: [],
                            oldScript: '',
                            sandbox: true,
                            script: 'return "<h1>Error</h1>"'
                        ],
                        script: [
                            classpath: [],
                            oldScript: '',
                            sandbox: true,
                            script: '''if (bastionUser.equals(\'Create Bastion User\')) {
                            return """
                            <div class=\'jenkins-form-description\'>How many days would you like to limit access for? (Use only for user creation)</div><br>
                            <input name=\'value\' type=\'text\' value=\'\' class=\'jenkins-input\' required><br>
                            """
                            } else {
                               return """
                               <h2>Only required for user creation</h2>
                               """
                            }'''
                        ]
                    )
                ),
                activeChoiceHtml(
                    choiceType: 'ET_FORMATTED_HTML',
                    name: 'validity',
                    omitValueField: false,
                    randomName: 'choice-parameter-129039043904949042390',
                    referencedParameters: 'runType',
                    script: groovyScript(
                        fallbackScript: [
                            classpath: [],
                            oldScript: '',
                            sandbox: true,
                            script: 'return "<h1>Error</h1>"'
                        ],
                        script: [
                            classpath: [],
                            oldScript: '',
                            sandbox: true,
                            script: '''if (runType.equals(\'Create User\')) {
                            return """
                            <div class=\'jenkins-form-description\'>How many days would you like to limit access for? (Use only for user creation)</div><br>
                            <input name=\'value\' type=\'text\' value=\'\' class=\'jenkins-input\' required><br>
                            """
                            } else {
                               return """
                               <h2>Only required for user creation</h2>
                               """
                            }'''
                        ]
                    )
                ),
                string(
                    description: 'Approved By', 
                    name: 'approvedBy'
                )
            ],
        )
    ]
)

pipeline {
    agent {
        label "JenkinsWorkerFleet"
    }

    stages {
        stage("Initial Setup") {
            steps {
                script {
                    variables                = load 'Helpers/variables.groovy'
                    notification             = load 'Helpers/notifications.groovy'
                    methods                  = load 'Helpers/methods.groovy'

                    ssh_key                  = (params.sshkey == "") ? "" : params.sshkey.replace(",", "")
                    username                 = params.email.replace("@surveysparrow.com", "")
                    username                 = username.replace(".", "")
                    hosts                    = params.postgresShards.replaceAll(",", " ")
                    domains                  = params.openSearchDomains.replaceAll(",", " ")
                    validity_days            = (params.validity == "") ? -1 : params.validity.replace(",", "")
                    bastion_validity_days    = (params.bastionValidity == "") ? "-1" : params.bastionValidity.replace(",", "")
                    approved_by              = params.approvedBy 

                    if (validity_days != "" && validity_days.toInteger() > 0) {
                        valid_till      = methods.getValidTill(validity_days.toInteger())
                    } else if (validity_days == "-1") {
                        valid_till      = "Infinity"
                    }

                    if (bastion_validity_days != "" && bastion_validity_days.toInteger() > 0) {
                        bastion_valid_till = methods.getValidTill(bastion_validity_days.toInteger())
                    } else if (bastion_validity_days == "-1") {
                        bastion_valid_till = "Infinity"
                    }
                }
            }
        }

        stage("Bastion User") {
            when {
                expression { params.bastionUser != "Do Nothing" }
            }
            steps {
                script {
                    sshagent([variables.BASTION_SSH_CREDENTIAL_ID]) {
                        if (params.bastionUser.equals("Create Bastion User")){
                            ssh_key=params.sshkey.replace(",", "")
                            sh "ssh -o StrictHostKeyChecking=no ${variables.BASTION_USER}@${variables.BASTION_NODES["${params.region}"]} bastion --no-color user create -u ${username} -H ${variables.BASTION_NODES["${params.region}"]} --key ${ssh_key} --group non-sudoer --validity ${bastion_validity_days}"
                            notification.sendStatusBasedNotification("BASTION_USER_SUCCESS", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        } else if (params.bastionUser.equals("Update SSH")){
                            ssh_key=params.sshkey.replace(",", "")
                            sh "ssh -o StrictHostKeyChecking=no ${variables.BASTION_USER}@${variables.BASTION_NODES["${params.region}"]} bastion --no-color user update -u ${username} -H ${variables.BASTION_NODES["${params.region}"]} -k ${ssh_key}"
                            notification.sendStatusBasedNotification("BASTION_USER_UPDATE_INFO", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        } else if (params.bastionUser.equals("Delete User")){
                            sh "ssh -o StrictHostKeyChecking=no ${variables.BASTION_USER}@${variables.BASTION_NODES["${params.region}"]} bastion --no-color user delete -u ${username} -H ${variables.BASTION_NODES["${params.region}"]} --force"
                            notification.sendStatusBasedNotification("BASTION_USER_DELETE_INFO", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                       }
                    }
                }
            }
        }

        stage("Postgres User Provisioning") {
            when {
                expression { params.postgresShards != "" }
            }
            steps {
                script {
                    sshagent([variables.BASTION_SSH_CREDENTIAL_ID]) {
                        if (params.runType == "Create User") {
                            output_message = sh script: "ssh -o StrictHostKeyChecking=no ${variables.BASTION_USER}@${variables.BASTION_NODES["${params.region}"]} bastion --no-color postgres create -u ${username} -H ${hosts} --access read --validity ${validity_days}", returnStdout: true
                            subject = "Your account is created on Postgres"
                            notification.sendStatusBasedNotification("BASTION_POSTGRES_USER_SUCCESS", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        } else if (params.runType == "Update Password") {
                            output_message = sh script: "ssh -o StrictHostKeyChecking=no ${variables.BASTION_USER}@${variables.BASTION_NODES["${params.region}"]} bastion --no-color postgres update -u ${username} -H ${hosts}", returnStdout: true
                            subject = "Your account password is updated on Postgres"
                            notification.sendStatusBasedNotification("BASTION_POSTGRES_USER_UPDATE_INFO", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        } else if (params.runType == "Delete User") {
                            output_message = sh script: "ssh -o StrictHostKeyChecking=no ${variables.BASTION_USER}@${variables.BASTION_NODES["${params.region}"]} bastion --no-color postgres delete -u ${username} -H ${hosts}", returnStdout: true
                            subject = "Your account is deleted on Postgres"
                            notification.sendStatusBasedNotification("BASTION_POSTGRES_USER_DELETE_INFO", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        }
                    }
                    output_message = output_message.replaceAll(~/\n/, "<br>")
                    emailext body: output_message, mimeType: 'text/html', from: variables.FROM_EMAIL, replyTo: variables.REPLY_TO_EMAIL, subject: subject, to: params.email
                }
            }
        }

        stage("OpenSearch User Provisioning") {
            when {
                expression { params.openSearchDomains != "" }
            }
            steps {
                script {
                    sshagent([variables.BASTION_SSH_CREDENTIAL_ID]) {
                        if (params.runType == "Create User") {  
                            output_message = sh script: "ssh -o StrictHostKeyChecking=no ${variables.BASTION_USER}@${variables.BASTION_NODES["${params.region}"]} bastion --no-color opensearch create -u ${username} -H ${domains} --access read --validity ${validity_days}", returnStdout: true
                            subject = "Your account is created on Opensearch"
                            notification.sendStatusBasedNotification("BASTION_OPENSEARCH_USER_SUCCESS", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        } else if (params.runType == "Update Password") {
                            output_message = sh script: "ssh -o StrictHostKeyChecking=no ${variables.BASTION_USER}@${variables.BASTION_NODES["${params.region}"]} bastion --no-color opensearch update -u ${username} -H ${domains}", returnStdout: true
                            subject = "Your account password is updated on opensearch"
                            notification.sendStatusBasedNotification("BASTION_OPENSEARCH_USER_UPDATE_INFO", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        } else if (params.runType == "Delete User") {
                            output_message = sh script: "ssh -o StrictHostKeyChecking=no ${variables.BASTION_USER}@${variables.BASTION_NODES["${params.region}"]} bastion --no-color opensearch delete -u ${username} -H ${domains}", returnStdout: true
                            subject = "Your account is deleted on opensearch"
                            notification.sendStatusBasedNotification("BASTION_OPENSEARCH_USER_DELETE_INFO", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        }
                    }
                    output_message = output_message.replaceAll(~/\n/, "<br>")
                    emailext body: output_message, mimeType: 'text/html', from: variables.FROM_EMAIL, replyTo: variables.REPLY_TO_EMAIL, subject: subject, to: params.email
                }
            }
        }
    }
    post {
        always {
            cleanWs()
        }
    }
}