properties(
    [
        disableConcurrentBuilds(), 
        parameters(
            [
                string(
                    description: 'Note: Your PR should pass all checks and be in open state. (https://bitbucket.org/surveysparrow/{REPO_NAME}/pull-requests/{PR_NUMBER})', 
                    name: 'pullRequestLink', trim: true), 
                string(description: 'Pass your ticket link here. Note: Submitted ticket\'s title will appear in Slack Notification. (https://surveysparrow.atlassian.net/browse/{TICKET_CODE})', name: 'ticketLink', trim: true), 
                text(
                    description: 'Please describe your purpose for running this script.', name: 'purpose'
                ),
                booleanParam(
                    description: 'Do you want to run script in serial?',
                    name: 'serial'
                ),
                reactiveChoice(
                    choiceType: 'PT_CHECKBOX', description: 'Select the target regions. Not required for running script in serial', filterLength: 1, filterable: false, 
                    name: 'regions', randomName: 'choice-parameter-6215972848029377', referencedParameters: 'serial',
                    script: groovyScript(
                        fallbackScript: [
                            classpath: [], oldScript: '', sandbox: true, script: 'return []'], 
                        script: [
                            classpath: [], oldScript: '', sandbox: true, script: '''
                            if (!serial) {
                                return ["US-VIRGINIA","EU-FRANKFURT","AP-MUMBAI","ME-UAE","UK-LONDON","AU-SYDNEY","CA-CANADA"]
                            }'''
                        ]
                    )
                ),
                activeChoiceHtml(
                    choiceType: 'ET_FORMATTED_HTML',
                    name: 'executionOrder',
                    description: 'Choose the execution order for the region, separated by semicolons and including the region names. Ignore this option during parallel execution.<br>Example: <code>US-VIRGINIA;AP-MUMBAI;EU-FRANKFURT</code>',
                    omitValueField: false,
                    randomName: 'choice-parameter-129039043904949042390',
                    referencedParameters: 'serial',
                    script: groovyScript(
                        fallbackScript: [
                            classpath: [],
                            oldScript: '',
                            sandbox: true,
                            script: 'return "<p>something went wrong</p>"'
                        ],
                        script: [
                            classpath: [],
                            oldScript: '',
                            sandbox: true,
                            script: '''if (serial) {
                            return """
                            <input name=\'value\' type=\'text\' value=\'\' class=\'jenkins-input\' required><br>
                            """
                            } else {
                               return "<p>Serialization for region execution is not enabled</p>"
                            }'''
                        ]
                    )
                ),
                booleanParam(
                    description: 'If you want to export some output files to s3, check the box and make sure your script saves output files in <code>$WORKDIR/output</code> folder. You can save multiple files inside that folder.',
                     name: 'containsOutputFile'
                ),
                 booleanParam(
                    description: 'Enable Check Hooks',
                    name: 'hooks',
                    defaultValue: true
                )
            ]
        )
    ]
)

pipeline {
    agent {
        label 'JenkinsWorkerFleet'
    }

    environment {
        bitbucketCredentials = credentials('BitBucketOAuthToken')
        jiraCredentials = credentials('JiraAuthToken')
    }

    stages {
        stage("Initialize Pipeline") {
            steps {
                script {
                    variables = load "Helpers/variables.groovy"
                    methods = load "Helpers/methods.groovy"
                    notifier = load "Helpers/notifications.groovy"

                    if (params.hooks == null) {
                        hooks = "false"
                    } else {
                        hooks = params.hooks.toString()
                    }

                    pr_link_tokens = params.pullRequestLink.split("/")
                    repo_name = pr_link_tokens[4]
                    pr_id = pr_link_tokens[6]

                    execution_type = (params.serial) ? "Serial" : "Parallel"

                    if (params.serial) {
                        execution_order  = params.executionOrder.replace(",", "").replace(" ", "")
                        selected_regions_labels = execution_order.split(";")
                    } else {
                        selected_regions_labels = params.regions.split(",")
                    }

                    selected_regions = []

                    for (def region_label in selected_regions_labels) {
                        if (variables.DATA_CENTER_REGION_MAP[region_label] == null) {
                            currentBuild.description = "Invalid region selected"
                            error("Invalid region selected")
                        }
                        selected_regions.add(variables.DATA_CENTER_REGION_MAP[region_label])
                    }

                    selected_region_string = selected_regions_labels.join(", ")
                    
                    application_name     = "${env.JOB_NAME.split('/')[-3]}"
                    notification_channel = variables.REPL_SCRIPT_NOTIFICATION_CHANNEL[application_name]
                    service = variables.SERVICES[application_name]

                    current_account = sh script: "aws sts get-caller-identity --query 'Account' --output text | tr -d '\n'", returnStdout: true
                }
            }
        }
        stage("Build Verification") {
            steps {
                script {
                    methods.verifyPRChecks(repo_name, pr_id, bitbucketCredentials)
                    pr_data = methods.getPRInfoSet(repo_name, pr_id, bitbucketCredentials)

                    if (pr_data.state != "OPEN") {
                        currentBuild.description = "PR State is not OPEN"
                        error("PR State is not OPEN")
                    }

                    if (pr_data.diff_state.size != 1) {
                        currentBuild.description = "PR should contain only one file"
                        error("PR should contain only one file. You have added ${pr_data.diff_state.size} files")
                    }

                    if (pr_data.dest_branch != "master") {
                        currentBuild.description = "PR should be raised to master branch"
                        error("PR should be raised to master branch")
                    }
                    repl_file = pr_data.files[0].new.path
                    ticket_data = methods.getTicketTitle(params.ticketLink, jiraCredentials)
                    
                    message_data = [
                        "pr_id": pr_id,
                        "region": selected_region_string,
                        "app": application_name,
                        "execution_type": execution_type,
                        "ticket_title": ticket_data.title,
                        "ticket_id": ticket_data.id,
                        "hooks": hooks
                    ]
                    mainthread = notifier.sendBotMessage("REPL_SCRIPT_STARTED", notification_channel, message_data)
                    
                }
            }
        }
        stage("Script Execution Setup") {
            steps {
                script {
                    dir("ContextFiles"){ 
                        methods.cloneEKSYAMLRepo(variables.KUBERNETES_YAML_BRANCH) 
                    }
                    for (def region in selected_regions) {
                        app = variables.REPL_APPLICATION[application_name][region]
                        if (app == null) {
                            currentBuild.description = "Invalid region selected"
                            error("Application ${application_name} is not configured for ${region}. \nTip: Check the region name in your parameters selected. If you have enrolled a new region, please update the configuration in the variables.groovy file.")
                        }
                        methods.setupScriptExecution(variables.SS_SCRIPT_REPO, service.namespace, pr_id, region, repl_file, pr_data.src_branch)
                    }

                }
            }
        }
        stage("Sequential Script Execution") {
            when {
                expression { params.serial }
            }
            steps {
                script {
                    script_states = [:]
                    for (def _region in selected_regions) {
                        def region = _region
                        app = variables.REPL_APPLICATION[application_name][region]
                        if (service.account != current_account) {
                            withAWS(region: region, roleAccount: service.account, role: service.role) {    
                                methods.executeReplScript(application_name, ticket_data.id, "${region}.repl.configmap.yaml", "${region}.orchestrator-repl-script.yaml", service.namespace, app.jobcontext, pr_id, region, app.cluster, app.bucket, params.containsOutputFile, repl_file, hooks, app.var_cmd_a, app.var_cmd_b)
                            }
                        } else {
                            methods.executeReplScript(application_name, ticket_data.id, "${region}.repl.configmap.yaml", "${region}.orchestrator-repl-script.yaml", service.namespace, app.jobcontext, pr_id, region, app.cluster, app.bucket, params.containsOutputFile, repl_file, hooks, app.var_cmd_a, app.var_cmd_b)
                        }
                        if (script_states[region].status == "Failed") {
                            exit("Failed in ${region}, cancelled execution for remaining regions.")
                        } else {
                            echo "\033[1;32mScript passed in ${region}, moving to next region [SUCCESS]\033[0m"
                        }
                    }
                }
            }
        }
        stage('Parallel Script Execution') {
            when {
                expression { !params.serial }
            }
            steps {
                script {
                    def parallelStages = [:]
                    script_states = [:]
                    for (def _region in selected_regions) {
                        def region = _region
                        def stageName = region
                        parallelStages[stageName] = {
                            app = variables.REPL_APPLICATION[application_name][region]
                            if (service.account != current_account) {
                                withAWS(region: region, roleAccount: service.account, role: service.role) {    
                                    methods.executeReplScript(application_name, ticket_data.id, "${region}.repl.configmap.yaml", "${region}.orchestrator-repl-script.yaml", service.namespace, app.jobcontext, pr_id, region, app.cluster, app.bucket, params.containsOutputFile, repl_file, hooks, app.var_cmd_a, app.var_cmd_b)
                                }
                                return
                            }
                            methods.executeReplScript(application_name, ticket_data.id, "${region}.repl.configmap.yaml", "${region}.orchestrator-repl-script.yaml", service.namespace, app.jobcontext, pr_id, region, app.cluster, app.bucket, params.containsOutputFile, repl_file, hooks, app.var_cmd_a, app.var_cmd_b)
                        }
                    }
                    parallel parallelStages
                }
            }
        }
        stage("Merge PR") {
            steps {
                script {
                    allPassed = true
                    for (def region in selected_regions) {
                        def state = script_states[region]
                        if (state.status == "Failed") {
                            allPassed = false
                            break
                        }
                    }
                    if (allPassed) {
                        methods.mergePR(repo_name, pr_id, bitbucketCredentials)
                    } else {
                        echo "One or more regions failed, skipping merge."
                    }
                }
            }
        }
        stage("Script Summary") {
            steps {
                script {
                    echo "\033[1mScript Results\nTicket Title: ${ticket_data.title}\nTicket ID: ${ticket_data.id}\nPR Link: ${params.pullRequestLink} \033[0m"
                    echo "Ticket ID: ${ticket_data.id}"
                    for (def region in selected_regions) {
                        def _region = region
                        def state = script_states[_region]

                        def status_color = (state.status == "Failed") ? "31" : "32"
                        def duration_color = (state.duration < 300) ? "32" : (state.duration < 3600) ? "33" : "31"
                        def output_file_link = (params.containsOutputFile) ? state.output : "\033[1;33mNo output file\033[0m"
                        echo "Results for ${_region} \033[1;${status_color}m[${state.status}]\033[0m \n\033[1mDuration:\033[0m \033[1;${duration_color}m${state.duration}\033[0m seconds \n\033[1mExecuted Repl Relative Path:\033[0m ${repl_file} \n\033[1mLogs:\033[0m ${state.logs} \n\033[1mOutput:\033[0m ${output_file_link}"   
                    }
                    if (allPassed) {
                        echo "\033[1;32mYour script passed in all selected regions and your PR is merged.\033[0m"
                    } else {
                        error("\033[1;31mOne or more regions failed.\033[0m")
                    }
                }
            }
        }
    }
    post {
        always {
            cleanWs()
        }
        aborted {
            script{
                notifier.sendBotMessage("REPL_SCRIPT_ABORTED", notification_channel, message_data)
            }
        }
        success {
            script {
                notifier.sendBotMessage("REPL_SCRIPT_SUCCESS", notification_channel, message_data)
            }
        }
        failure {
            script {
                notifier.sendBotMessage("REPL_SCRIPT_FAILURE", notification_channel, message_data)
            }
        }
    }
}