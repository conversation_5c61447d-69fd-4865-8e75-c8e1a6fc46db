properties(
  [
    parameters(
      [
        choice(
          choices: ['us-east-1', 'eu-central-1', 'eu-west-2', 'ap-south-1', 'me-central-1', 'ap-southeast-2', 'ca-central-1', 'me-central2-b'], 
          description: 'Name of the environment to update config file', 
          name: 'region'
        ),
        [
          $class: 'ChoiceParameter', 
          choiceType: 'PT_SINGLE_SELECT', 
          description: 'Config to be updated', 
          filterLength: 1, 
          filterable: false, 
          name: 'config', 
          randomName: 'choice-parameter-9840454115122344', 
          script: [
            $class: 'GroovyScript', 
            fallbackScript: [
              classpath: [], 
              oldScript: '', 
              sandbox: true, 
              script: 'return [\'ERROR\']'
            ], 
            script: [
              classpath: [], 
              oldScript: '', 
              sandbox: true, 
              script: 'return [\'app\', \'haproxy\', \'arena\']'
            ]
          ]
        ], 
        [
          $class: 'CascadeChoiceParameter', 
          choiceType: 'PT_RADIO', 
          description: 'The Stack that needs config update', 
          filterLength: 1, 
          filterable: false, 
          name: 'stack', 
          randomName: 'choice-parameter-9840454116189397', 
          referencedParameters: 'config', 
          script: [
            $class: 'GroovyScript', 
            fallbackScript: [
              classpath: [], 
              oldScript: '', 
              sandbox: true, 
              script: 'return [\'ERROR\']'
            ], 
            script: [
              classpath: [], 
              oldScript: '', 
              sandbox: true, 
              script: '''if (config.equals(\'app\')) {
              return [\'blue\', \'green\']
              }
              if (config.equals(\'haproxy\')) {
              return [\'blue-green\', \'green\']
              }
              if (config.equals(\'arena\')) {
              return []
              }'''
            ]
          ]
        ],
        string (
          name: 'reason',
          description: "Reason to perform the Config Update"
        ),
        booleanParam (
          name: 'restart',
          description: "Restart the pods ?"
        )
      ]
    )
  ]
)

pipeline {
  agent { label "JenkinsWorkerFleet" }

  stages {
    stage('Git Clone') {
      steps {
        script {
          notifier   = load 'Helpers/notifications.groovy'
          variables = load 'Helpers/variables.groovy'
          methods   = load 'Helpers/methods.groovy'

          notifier.sendSlackNotification('CONFIG_UPDATE_STARTED')
          
          currentBuild.displayName = "${params.region}"
          currentBuild.description = "${params.config} | ${params.stack}"

          methods.prepInstance()
          dir ('surveysparrow-production-config') {
            methods.cloneConfig()
          }

          if (params.config == 'app') {
            deploymentParams = methods.getDeploymentParameters(params.stack, params.region)
            stack = deploymentParams.configmap
            if (deploymentParams.deployment != deploymentParams.configmap) {
              echo "Deployment and Configmap are not same"
              error "Deployment and Configmap are not same"
            }
          }

          if (params.config == 'haproxy') {
            stack = params.stack
          }

          if (params.config == 'arena') {
            stack = "\'\'"
          }
        }
      }
    }

    stage ('Config Update') {
      steps {
        script {
          methods.authenticateToK8sCluster(params.region, variables.CLUSTER_NAME_REGION_MAP[params.region])

          if (params.config == 'app') {
            dir ('surveysparrow-production-config') {
              sh "jq '.version |= \"${deploymentParams.version}\"' ${params.region}.json > temp.json && mv temp.json ${params.region}.json"
            }
          }

          sh "python3 Helpers/updatecm.py -c ${params.config} -e ${params.region} -s ${stack}"
          sh "kubectl apply -f Helpers/cm-template.yaml"
        }
      }
    }

    stage('Restarting Containers') {

      when {
        expression {
          return params.restart
        }
      }

      steps {
        script {
          echo 'Restarting the application on EKS Cluster...'

          methods.authenticateToK8sCluster(params.region, variables.CLUSTER_NAME_REGION_MAP[params.region])

          switch (params.config) {
            case 'app':
              def appName  = variables.REGION_APP_MAP[params.region]
              def restarts = [:]
              def deployments = variables.SCALED_DOWN_DCS.contains(params.region) ? variables.SCALED_DOWN_DEPLOYMENTS : variables.DEPLOYMENTS
              for (def i = 0; i < deployments.size(); i++) {
                def deployment     = deployments[i]
                def deploymentName = appName + '-' + deployment + "-${deploymentParams.deployment}"
                restarts[deploymentName] = {
                  methods.restartDeployment(deploymentName, variables.DEFAULT_NAMESPACE)
                }
              }

              parallel restarts
              break
            case 'haproxy':
              methods.restartDeployment("haproxy-forwarder", variables.DEFAULT_NAMESPACE)
              break
            case 'arena':
              def deploymentName = variables.REGION_APP_MAP[params.region] + '-arena'
              methods.restartDeployment(deploymentName, variables.DEFAULT_NAMESPACE)
              break
            default:
              echo "[-] Nothing to be done"
              break
          }
        }
      }
    }
  }

  post {
    always {
      cleanWs()
    }

    success {
      script {
        notifier.sendSlackNotification('CONFIG_UPDATE_SUCCESSFUL')
      }
    }

    failure {
      script {
        notifier.sendSlackNotification('CONFIG_UPDATE_FAILED')
      }
    }

    aborted {
      script {
        notifier.sendSlackNotification('CONFIG_UPDATE_ABORTED')
      }
    }
  }
}