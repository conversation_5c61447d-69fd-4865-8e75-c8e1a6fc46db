# ConfigUpdate Pipeline

A pipeline to update config in EKS

## Input to Pipeline
  - Config - app, arena, haproxy
  - Region - ap-south-1, me-central-1

## Pipeline Steps
  - Git Clone
    - Clone the surveysparrow-production-config repo

  - Config Update
    - Use the template files and update the respective ConfigMap selected from paramaters
  
  - Restarting Containers
    - Restart the respective containers for the updated ConfigMap to be pulled into the containers
  
## Supporting Files
  - app-cm.yaml
    - The template file for app ConfigMap
  - haproxy-cm.yaml
    - The template file for haproxy ConfigMap
  - updatecm.py
    - Script to update the template file from surveysparrow-production-config repo