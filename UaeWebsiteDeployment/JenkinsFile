def sendSlackNotification(color, message) 
{
    wrap([$class: 'BuildUser'])
    {
        slackSend (
            color: "${color}",
            message: "${message}",
            teamDomain: 'surveysparrow',
            tokenCredentialId: 'SlackIntegrationToken',
            channel: 'production-deployment-website'
        )  
    }
}

pipeline
{

    // Worker node in which the test will run
    agent { label 'website-deployment-testing' }

    // The Environment Variables for the Build
    environment
    {
        WEBSITE_REPO_URL="*****************:surveysparrow/surveysparrow-site.git"
        WEBSITE_REPO_CRED="BitBucketPrivateKey"
        BUCKET_NAME="ss-website-deployments-me-uae"
        APPLICATION_INSTANCE_GROUP="ss-site-deployment-grp"
        APPLICATION="ss-site"
        REGION="me-central-1"
        deployment_branch="surveysparrow.ae"
    }

    stages
    {
        stage('Git Clone')
        {
            steps
            {
                script
                {
                    dir('surveysparrow-site')
                    {
                        echo"Cloning the Website repo"
                        git branch: "${env.deployment_branch}", credentialsId: "${env.WEBSITE_REPO_CRED}", url: "${env.WEBSITE_REPO_URL}"
                    }
                }
            }
        }
        stage('Compiling the Project') 
        {
            steps
            {
                dir('surveysparrow-site')
                {
                    script
                    {
                        // Changing the build name to version param
                        workspace_path = sh (script: "pwd | tr -d '\n'", returnStdout: true)

                        echo "Making a new version of the application. For Environment:: UAE"
                        version_number = sh (script: "echo 'uae-v1.0.${currentBuild.number}' | tr -d '\n'", returnStdout: true)

                        currentBuild.displayName = "Version: ${version_number}"

                        echo "Sending slack notification for deployment start"
                        
                        sendSlackNotification (
                            "#0066CC",
                            ":rocket: Deployment of Branch Name:  `${env.deployment_branch}` \nStarted by: `${env.BUILD_USER}` \nApplication: `UAE-WEBSITE` !! \nVersion: `${version_number}` \n For More details - ${env.BUILD_URL}"
                        )
                        
                    
                        echo "Compiling the Project"
                        echo "$workspace_path"

                        dir('wp-content/themes/survey-sparrow/assets')
                        {
                            nodejs('Node-v10.20.1')
                            {
                                echo "[+] Running NPM Install"
                                sh "npm install"
                                sh "npm install --only=dev"

                                echo "[+] Running Gulp Prod"
                                sh """
                                    npm install -g gulp-cli@2.2.1
                                    gulp prod
                                """
                            }
                        }
                    }
                }
            }
        }
        stage('Making a Zip file') 
        {
            steps 
            {
                dir('surveysparrow-site')
                {
                    echo "Running bash build.sh"
                    sh "bash build.sh"

                    echo "Renaming the Zip file"
                    sh "mv wordpress-4.8.zip ${version_number}.zip"

                    echo "Pushing the Zip file to survey-sparrow-site-source-code bucket"

                    echo "aws s3 cp ${workspace_path}/${version_number}.zip s3://${env.BUCKET_NAME} --region ${env.REGION}"
                    sh "aws s3 cp ${workspace_path}/${version_number}.zip s3://${env.BUCKET_NAME} --region ${env.REGION}"
                }
            }
        }
        stage('CodeDeploy Deployment')
        {
            steps
            {
                echo "Deployment to Code Deploy"
                script
                {
                    sh "pip3 install boto3 argparse"
                    sh "REGION=${env.REGION} python3 Helpers/uae-website-helper.py -d -a ${env.APPLICATION} -g ${env.APPLICATION_INSTANCE_GROUP} -b ${env.BUCKET_NAME} -k ${version_number}.zip"
                    echo "Deployment Completed"
                }
            }
        }
    }
    post 
    {
        always {
            script
            {
                currentBuild.description = "Deployment of Branch Name: ${env.deployment_branch}"
                cleanWs()
            }
        }
        success
        {
            script
            {
                sendSlackNotification (
                "#2ECC71",
                ":sparrow_love: Deployment Branch Name: `${env.deployment_branch}` \n Started by: `${env.BUILD_USER}` \nSUCCESSFUL Done :tada: \nREGION: `UAE` !!! \n Current Version: `${version_number}`"
                )
            }
        }

        failure 
        {
            script
            {
                sendSlackNotification (
                "#E74C3C",
                ":red_circle: Deployment of Branch Name: `${env.deployment_branch}` \n Started by: `${env.BUILD_USER}` FAILED \nREGION: UAE !!! \n For More details - ${env.BUILD_URL}"
                )
            }
        }
    } 
}