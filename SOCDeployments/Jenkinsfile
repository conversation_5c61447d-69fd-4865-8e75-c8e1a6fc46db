properties(
  [
    disableConcurrentBuilds(),
    buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '5', numToKeepStr: '5'))

  ]
)

STAGE_COUNT   = 4
CURRENT_STAGE = 0

pipeline {
  options {
    parallelsAlwaysFailFast()
  }

  agent {
    label "DockerBuilderFleet"
  }

  tools {
    nodejs 'Node-v18.20.2'
  }

  parameters {
    string (
      description: "Release branch that was merged.",
      name: 'releasebranch'
    )

    string (
      description: "Deployment Tag to be deployed.",
      name: "deploymenttag"
    )
  }

  environment {
    SLACK_TOKEN = credentials('SlackBotToken')
  }

  stages {
    stage('DEPLOYMENT_PREP') {
      steps {
        script {
          variables = load 'Helpers/variables.groovy'
          methods   = load 'Helpers/methods.groovy'
          notifier   = load 'Helpers/notifications.groovy'

          // methods.prepInstance()
          sh "jq -h"
          actualJobName    = env.JOB_NAME
          jobNameTokens    = env.JOB_NAME.split('/')
          regionName       = jobNameTokens[-1]
          env.JOB_NAME     = jobNameTokens[1]
          region           = variables.REGION_CODE_DC_NAME_MAP[regionName]
          microServiceVars = variables.SOC[env.JOB_NAME]['backend']
          lastSuccessfulBuildDuration = methods.getLastSuccessfulBuildDuration(actualJobName)

          message_timestamp = notifier.sendSOCDeploymentProgressNotification(CURRENT_STAGE, lastSuccessfulBuildDuration - currentBuild.duration, null, 'DEPLOYMENT_STARTED')
          notifier.sendSlackNofiticationMicroservices('SOC_DEPLOYMENT_STARTED')

          dir(microServiceVars.folderName) {
            methods.cloneMicroServicesRepo(microServiceVars.repoUrl, params.deploymenttag)

            deploymentParams = methods.getDeploymentParameters('green', region)
            version          = deploymentParams.version
            packageJsonHash  = methods.checkToInstallNodeModules()
          }

          currentBuild.displayName = "#${params.deploymenttag}"
          currentBuild.description = "${params.releasebranch}"
          PERCENTAGE = methods.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          notifier.sendSOCDeploymentProgressNotification(PERCENTAGE, lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
        }
      }
    }

    stage ('BUILD') {
      steps {
        script {
          notifier.sendSOCDeploymentProgressNotification(PERCENTAGE, lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
          notifier.sendSlackNofiticationMicroservices('SOC_BUILD_STARTED')
          dir(microServiceVars.folderName) {
            dockerRepo = variables.ECR_REPO_APP[region]
            methods.authenticateToContainerRegistry(region, dockerRepo)
            methods.buildAndPushDockerImage(dockerRepo, microServiceVars['service'], env.BUILD_NUMBER)
          }
          PERCENTAGE = methods.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          notifier.sendSOCDeploymentProgressNotification(PERCENTAGE, lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
        }
      }

      post {
        failure {
          script {
            echo "FAILED"
            notifier.sendSlackNofiticationMicroservices('SOC_BUILD_FAILED')
          }
        }
      }
    }

    stage ('UPDATE_CONFIG') {
      steps {
        script {
          notifier.sendSOCDeploymentProgressNotification(PERCENTAGE, lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
          notifier.sendSlackNofiticationMicroservices('SOC_DEPLOYMENT_APPROVAL')
          input (message: "Approve to start the deployment.")

          methods.updateMicroServiceConfig(region, microServiceVars.shortName)
          PERCENTAGE = methods.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          notifier.sendSOCDeploymentProgressNotification(PERCENTAGE, lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
        }
      }
    }

    stage ('DEPLOY') {
      steps {
        script {
          notifier.sendSOCDeploymentProgressNotification(PERCENTAGE, lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
          methods.authenticateToK8sCluster(region, variables.CLUSTER_NAME_REGION_MAP[region])

          def deployments = [];
          def appLayers    = microServiceVars.dcLayerMap[region]?.appLayers
          def workerLayers = microServiceVars.dcLayerMap[region]?.workerLayers
          def appName      = variables.REGION_APP_MAP[region]

          if (appLayers) {
            appLayers.each { layer ->
              deployments.add("${appName}-${microServiceVars['shortName']}-${layer}")
            }
          }

          if (workerLayers) {
            workerLayers.each { layer ->
              deployments.add("${appName}-${microServiceVars['shortName']}-${layer}")
            }
          }

          for (def deployment in deployments) {
            methods.restartDeployment(deployment, variables.DEFAULT_NAMESPACE)
          }
          PERCENTAGE = methods.calculatePercentage(++CURRENT_STAGE, STAGE_COUNT)
          notifier.sendSOCDeploymentProgressNotification(PERCENTAGE, lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
        }
      }
    }
  }

  post {
    always {
      script {
        echo "ALWAYS"
        // step([$class: 'NewRelicDeploymentNotifier',
        //   notifications: [
        //     [
        //       apiKey: "NewrelicNotifier",
        //       applicationId: '',
        //       european: false,
        //       entityGuid: "${variables.REGION_ENTITY_GUID_MAP[env.JOB_NAME]}",
        //       changelog: "",
        //       commit: "${params.deploymenttag}",
        //       deeplink: "${env.BUILD_URL}",
        //       deploymentType: "ROLLING",
        //       description: "Deployment Tag: ${params.deploymenttag}",
        //       groupId: "${env.JOB_NAME}",
        //       user: "${env.BUILD_USER}",
        //       version: "${params.deploymenttag}",
        //       timestamp: ''
        //     ]
        //   ]
        // ])
      }
    }

    success {
      script {
        notifier.sendSOCDeploymentProgressNotification(100, currentBuild.duration, message_timestamp, 'DEPLOYMENT_SUCCESSFUL')
        notifier.sendSlackNofiticationMicroservices('SOC_DEPLOYMENT_SUCCESS')
        cleanWs()
      }
    }

    failure {
      script {
        notifier.sendSOCDeploymentProgressNotification(methods.calculatePercentage(CURRENT_STAGE, STAGE_COUNT), currentBuild.duration, message_timestamp, 'DEPLOYMENT_FAILED')
        notifier.sendSlackNofiticationMicroservices('SOC_DEPLOYMENT_FAILED')
        cleanWs()
      }
    }

    aborted {
      script {
        notifier.sendSOCDeploymentProgressNotification(methods.calculatePercentage(CURRENT_STAGE, STAGE_COUNT), currentBuild.duration, message_timestamp, 'DEPLOYMENT_ABORTED')
        notifier.sendSlackNofiticationMicroservices('SOC_DEPLOYMENT_ABORTED')
        cleanWs()
      }
    }
  }
}