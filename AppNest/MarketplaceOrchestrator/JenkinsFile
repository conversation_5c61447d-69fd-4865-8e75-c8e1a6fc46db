pipeline 
{
	agent 
	{
		label "JenkinsWorkerFleet"
	}

	environment 
	{
		PROJECT_KEY 	  = "SSE"
		JIRA_TOKEN 		  = credentials('JiraAuthToken')
		RELEASE_NOTES_APP = "AppNest"
	}

	parameters 
	{
		string (
		name: 'releaseBranch',
		description: "Release branch to deploy"
		)
	}

	stages 
	{
		stage('Jira Issue List') 
		{
			steps 
			{
				script 
				{
					variables  = load 'Helpers/variables.groovy'
					notifier   = load 'Helpers/notifications.groovy'
					methods    = load 'Helpers/methods.groovy'

					methods.prepInstance()

					GIT_TAG_FULL = "Appnest-${new Date().format('dd-MM-yyyy')}"

					issuekeys = sh(returnStdout: true, script: "RELEASE_BRANCH=${params.releaseBranch} JIRA_TOKEN=${JIRA_TOKEN} PROJECT_KEY=${PROJECT_KEY} APPLICATION=${env.RELEASE_NOTES_APP} DEPLOYMENT_TAG=${GIT_TAG_FULL} python3 Helpers/jirahelper.py -k").trim()
					issueKeysArr = "${issuekeys.replace('[','')}"
					issueKeysArr = "${issueKeysArr.replace(']', '')}"

					issueList = []
					for(issue in issueKeysArr.split(',')) 
					{
						issueList.add(issue.replace("'", '').trim())
					}
				}
			}
		}

		stage("Deployment Message")
		{
			steps
			{
				script
				{
					jiraSendDeploymentInfo environmentId: 'us-east-1', environmentName: 'us-east-1', environmentType: 'production', issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'successful'
					jiraSendDeploymentInfo environmentId: 'eu-central-1', environmentName: 'eu-central-1', environmentType: 'production', issueKeys: issueList, site: 'surveysparrow.atlassian.net', state: 'successful'

					input(message: 'Post message to #deployment ?')
					sh "RELEASE_BRANCH=${releaseBranch} DEPLOYMENT_TAG=${GIT_TAG_FULL} JIRA_TOKEN=${JIRA_TOKEN} PROJECT_KEY=${PROJECT_KEY} APPLICATION=${env.RELEASE_NOTES_APP} python3 Helpers/jirahelper.py -p"
					sh "RELEASE_BRANCH=${releaseBranch} DEPLOYMENT_TAG=${GIT_TAG_FULL} JIRA_TOKEN=${JIRA_TOKEN} PROJECT_KEY=${PROJECT_KEY} APPLICATION=${env.RELEASE_NOTES_APP} python3 Helpers/jirahelper.py -r"
				}
			}
		}
	}
}