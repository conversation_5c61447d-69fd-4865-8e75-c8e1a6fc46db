def marketplace_admin_portal = [
  'Surveysparrow-Marketplace/AppNest-AdminPortal-Deployments/Marketplace-Admin-Portal-EU': [
    AWS_DEFAULT_REGION: "eu-central-1",
    ECR_NAME: "ss-admin-portal-eu",
	EKS_CLUSTER: "ss-marketplace-eu-cluster",
	NAMESPACE: "admin-portal",
	DEPLOYMENT: "admin-portal-deployment",
	ECR_REGISTRY_URL: "234462785196.dkr.ecr.eu-central-1.amazonaws.com"
  ],
  'Surveysparrow-Marketplace/AppNest-AdminPortal-Deployments/Marketplace-Admin-Portal-US': [
    AWS_DEFAULT_REGION: "us-east-1",
    ECR_NAME: "ss-admin-portal",
	EKS_CLUSTER: "ss-marketplace-cluster",
	NAMESPACE: "admin-portal",
	DEPLOYMENT: "admin-portal-deployment",
	ECR_REGISTRY_URL: "234462785196.dkr.ecr.us-east-1.amazonaws.com"
  ],
  'Surveysparrow-Marketplace/AppNest-AdminPortal-Deployments/Marketplace-Admin-Portal-AP': [
    AWS_DEFAULT_REGION: "ap-south-1",
    ECR_NAME: "ss-admin-portal-marketplace",
	EKS_CLUSTER: "ss-marketplace-ap-cluster",
	NAMESPACE: "admin-portal",
	DEPLOYMENT: "admin-portal-deployment",
	ECR_REGISTRY_URL: "234462785196.dkr.ecr.ap-south-1.amazonaws.com"
  ],
  'Surveysparrow-Marketplace/AppNest-AdminPortal-Deployments/Marketplace-Admin-Portal-ME': [
    AWS_DEFAULT_REGION: "me-central-1",
    ECR_NAME: "ss-admin-portal-marketplace",
	EKS_CLUSTER: "ss-marketplace-me-cluster",
	NAMESPACE: "admin-portal",
	DEPLOYMENT: "admin-portal-deployment",
	ECR_REGISTRY_URL: "234462785196.dkr.ecr.me-central-1.amazonaws.com"
  ],
  'Surveysparrow-Marketplace/AppNest-AdminPortal-Deployments/Marketplace-Admin-Portal-UK': [
    AWS_DEFAULT_REGION: "eu-west-2",
    ECR_NAME: "ss-admin-portal-marketplace",
	EKS_CLUSTER: "ss-marketplace-uk-cluster",
	NAMESPACE: "admin-portal",
	DEPLOYMENT: "admin-portal-deployment",
	ECR_REGISTRY_URL: "234462785196.dkr.ecr.eu-west-2.amazonaws.com"
  ],
  'Surveysparrow-Marketplace/AppNest-AdminPortal-Deployments/Marketplace-Admin-Portal-CA': [
    AWS_DEFAULT_REGION: "ca-central-1",
    ECR_NAME: "ss-admin-portal-marketplace",
	EKS_CLUSTER: "ss-marketplace-ca-cluster",
	NAMESPACE: "admin-portal",
	DEPLOYMENT: "admin-portal-deployment",
	ECR_REGISTRY_URL: "234462785196.dkr.ecr.ca-central-1.amazonaws.com"
  ],
  'Surveysparrow-Marketplace/AppNest-AdminPortal-Deployments/Marketplace-Admin-Portal-SY': [
    AWS_DEFAULT_REGION: "ap-southeast-2",
    ECR_NAME: "ss-admin-portal-marketplace",
	EKS_CLUSTER: "ss-marketplace-sy-cluster",
	NAMESPACE: "admin-portal",
	DEPLOYMENT: "admin-portal-deployment",
	ECR_REGISTRY_URL: "234462785196.dkr.ecr.ap-southeast-2.amazonaws.com"
  ]
]

pipeline
{
    agent 
    {
      label 'MarketplaceDockerbuilderFleet'
    }

	environment 
	{
		AWS_DEFAULT_REGION    = "${marketplace_admin_portal[env.JOB_NAME]['AWS_DEFAULT_REGION']}"
		ECR_REGISTRY_URL      = "${marketplace_admin_portal[env.JOB_NAME]['ECR_REGISTRY_URL']}"
		BITBUCKET_CREDENTIALS = "BitBucketPrivateKey"

		DEV_PORTAL_GIT_URL        = "*****************:surveysparrow/sparrowapps-dev-portal.git"
        PRODUCTION_CONFIG_GIT_URL = "*****************:surveysparrow/surveysparrow-production-config.git"
		PRODUCTION_CONFIG_REPO    = "marketplace"

		ECR_NAME    = "${marketplace_admin_portal[env.JOB_NAME]['ECR_NAME']}"
		EKS_CLUSTER = "${marketplace_admin_portal[env.JOB_NAME]['EKS_CLUSTER']}"

		NAMESPACE  =  "${marketplace_admin_portal[env.JOB_NAME]['NAMESPACE']}"
		DEPLOYMENT =  "${marketplace_admin_portal[env.JOB_NAME]['DEPLOYMENT']}"

		workspace_path = sh (script: "pwd | tr -d '\n'", returnStdout: true)
    }

    parameters 
	{
		string (
			defaultValue: 'production',
			description: 'Branch that is to be deployed.',
			name: 'branchName'
		)
    }
    stages 
    {
		stage("Git Clone")
		{
			steps
            {
				script
                {
					variables = load 'Helpers/variables.groovy'
                    notifier  = load 'Helpers/notifications.groovy'
                    methods   = load 'Helpers/methods.groovy'

					notifier.sendSlackNotificationMarketplace( "#0066CC", "MARKETPLACE_ADMIN_PORTAL_DEPLOYMENT_STARTED" )
					currentBuild.displayName = "Deploying: ${params.branchName}"

					dir ('admin-portal') 
					{
						git branch: "${params.branchName}", credentialsId: "${variables.BB_CREDENTIAL_ID}", url: "${env.DEV_PORTAL_GIT_URL}"
					}
					dir ('surveysparrow-production-config') 
					{
						methods.cloneConfig()
					}
				}
			}
		}
		stage("Docker Build")
		{
			steps
			{
				script
				{
					sh "cp ${env.workspace_path}/surveysparrow-production-config/marketplace/admin-portal/${env.AWS_DEFAULT_REGION}.env ${env.workspace_path}/admin-portal/admin-portal/.env"

					dir ('admin-portal/admin-portal')
					{

						sh "sudo systemctl start docker"
						sh "sleep 35"
						
						echo "LOGGING INTO ECR"
						sh "aws ecr get-login-password --region ${env.AWS_DEFAULT_REGION} | docker login --username AWS --password-stdin ${env.ECR_REGISTRY_URL}"

						echo "BUILDING AND TAGGING DOCKER IMAGE"
						sh "docker build -t ${env.ECR_REGISTRY_URL}/${env.ECR_NAME}:latest ."
						
						echo "PUSHING THE TAGED DOCKER IMAGE"
						sh "docker push ${env.ECR_REGISTRY_URL}/${env.ECR_NAME}:latest"
					}	
				}
			}
		}
		stage("EKS Deployment")
		{
			steps
			{
				script
                {
                    echo 'Deploying the application on EKS Cluster...'

					echo "LOGGING INTO EKS CLUSTER"
					sh "aws eks --region ${env.AWS_DEFAULT_REGION} update-kubeconfig --name ${env.EKS_CLUSTER}"

					echo "RESTARING THE DEPLOYMENT TO PULL LATEST IMAGE"
					sh "kubectl rollout restart deployment/${env.DEPLOYMENT} -n ${env.NAMESPACE}"

					echo "WAITING STILL THE RESTART IS COMPLETED"
					sh "kubectl rollout status deployment/${env.DEPLOYMENT} -n ${env.NAMESPACE}"

                }
			}
		}
    }
    post
    {
		always 
		{
			script
			{
				cleanWs()
			}
		}
		success 
        {
            script 
            {
				currentBuild.displayName = "Deployed: ${params.branchName}"
                notifier.sendSlackNotificationMarketplace("#2ECC71", "MARKETPLACE_ADMIN_PORTAL_DEPLOYMENT_SUCCESS")
            }
        }
        failure 
        {
            script 
            {
				currentBuild.displayName = "Failed: ${params.branchName}" 
                notifier.sendSlackNotificationMarketplace("#E74C3C", "MARKETPLACE_ADMIN_PORTAL_DEPLOYMENT_FAILURE")
            }
        }
        aborted 
        {
            script 
            {
				currentBuild.displayName = "Aborted: ${params.branchName}"   
                notifier.sendSlackNotificationMarketplace("#9D9D9D", "MARKETPLACE_ADMIN_PORTAL_DEPLOYMENT_ABORTED")                
            }
        }
    }
}