import boto3
import helper
import aws_apis
import os
import sys
import time

region = os.getenv("AWS_DEFAULT_REGION")
aws_registry_url  = os.getenv("AWS_REGISTRY_URL")
repo_name = os.getenv("ECR_REPO_NAME")
task_definition_name = os.getenv("TASK_DEFINITION_NAME")
ecs_cluster = os.getenv("ECS_CLUSTER")
ecs_service = os.getenv("ECS_SERVICE")
bucket_name=os.getenv("BUCKET_NAME")
env_object=os.getenv("ENV_OBJECT")

# ECR boto3 client
client_ecr = boto3.client(
    'ecr',
    region_name=region
)

# ECS boto3 client
client_ecs = boto3.client(
    'ecs' ,
    region_name=region,
)

# S3 boto3 client
client_s3 = boto3.client(
    's3' ,
    region_name=region,
)

try:
    # Getting the last pushed image in ECR
    version , pushed_at = helper.get_latest_master_image(client_ecr , repo_name)
    print(f'app {version} pushed at {pushed_at}')

    # Getting only the latest task definition for the given ECS cluster
    list_task_definition_response = aws_apis.list_task_definitions(client_ecs , task_definition_name)

    lastTaskDefintion=list_task_definition_response["taskDefinitionArns"][0]

    # Getting the last Task Definition Number
    lastTaskDefintionNo=int(lastTaskDefintion.split('/')[-1].split(':')[1])
    print(f"Uploading the new task defintion env to S3: {lastTaskDefintionNo+1}")

    # Assigning the env file a ID
    destination=str("td-"+str(lastTaskDefintionNo+1)+"/.env")

    # Copying the new Config to the S3 Bucket
    copy_object_response = client_s3.copy_object(
        Bucket=bucket_name,
        CopySource={'Bucket': bucket_name, 'Key': env_object},
        Key=destination
    )

    # New uploaded ENV File ARN
    s3_arn=str("arn:aws:s3:::"+bucket_name+"/"+destination)

    # Getting the latest task defintion details to replicate for the new task definition
    describe_task_definition_response = aws_apis.describe_task_definition(client_ecs , list_task_definition_response["taskDefinitionArns"][0])

    # Getting the container_defintion to create a new task defintion
    container_definition = describe_task_definition_response["taskDefinition"]["containerDefinitions"][0]

    # The new image URI created with the latest inserted image in ECR
    image_uri = aws_registry_url+":"+version
    print(f'The image URI: {image_uri}')

    # Creating the new task defintion with the previous created task definition
    register_task_definition_response = aws_apis.register_task_definition(client_ecs , describe_task_definition_response , container_definition , image_uri , s3_arn)
    print(register_task_definition_response["taskDefinition"]["revision"])

    # The latest task definition revision
    task_revision = str(register_task_definition_response["taskDefinition"]["revision"])

    # Update the service with the latest task defintion revision
    update_service_response = aws_apis.update_service(
        client_ecs,
        ecs_cluster,
        ecs_service,
        task_definition_name+":" + task_revision
    )

    print(update_service_response)
    time.sleep(10.0)

    # Waiting for the deployment to be Completed
    print("\nWaiting for the deployment to be Completed\n")
    while True:
        service_response = aws_apis.describe_services(client_ecs , ecs_service , ecs_cluster)

        service_status = service_response['services'][0]['deployments'][0]['rolloutState']

        if service_status == 'COMPLETED':
            print('Deployment complete! The service is running the latest version of the task definition.')
            break

        elif service_status == 'IN_PROGRESS':
            print('The service is in the process of deploying a new version of the task definition. The new version will become active once all tasks have been updated.')

        elif service_status == 'FAILED':
            print('The service failed to deploy a new version of the task definition. Check the deployment status for more information.')
            sys.exit(1)

        else:
            print(f'\nUnknown status: {service_status}\n')

        time.sleep(10.0)
        
except Exception as err:
    print(err)
    sys.exit(1)
