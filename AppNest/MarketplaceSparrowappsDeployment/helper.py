import aws_apis

def get_latest_master_image(client_ecr , repo_name):

    list_images_response = aws_apis.list_images(client_ecr , repo_name)

    latest = None
    temp_tag = None

    for image in list_images_response:
        tag = image['imageTag']

        img = aws_apis.describe_images(client_ecr , repo_name , tag)
        pushed_at = img['imagePushedAt']
        if latest is None:
            latest = pushed_at
            temp_tag = tag
        else:
            if latest < pushed_at:
                latest = pushed_at
                temp_tag = tag
    return temp_tag, latest
