
def list_images(client_ecr , repo_name):
    list_images_response = client_ecr.list_images(
        repositoryName=repo_name,
        maxResults=1000
    )
    return list_images_response['imageIds']

def describe_images(client_ecr , repo_name , tag):
    describe_image_list = client_ecr.describe_images(
            repositoryName=repo_name,
            imageIds=[
                {
                    'imageTag': tag
                },
            ]
        )
    return describe_image_list['imageDetails'][0]

def list_task_definitions(client_ecs , family ):
    list_task_def_response = client_ecs.list_task_definitions(
        familyPrefix=family,
        status='ACTIVE',
        sort='DESC',
        maxResults=1
    )
    return list_task_def_response

def describe_task_definition(client_ecs , task):
    describe_task_definition_response = client_ecs.describe_task_definition(
        taskDefinition=task,
    )
    return describe_task_definition_response

def register_task_definition(client_ecs , describe_task_definition_response , container_definition ,image_uri , arn):
    register_task_definition_response = client_ecs.register_task_definition(
        family=describe_task_definition_response["taskDefinition"]["family"],
        networkMode='bridge',
        containerDefinitions=[
            {
                'name': container_definition["name"],
                'image': image_uri,
                'cpu': int(describe_task_definition_response["taskDefinition"]["cpu"]),
                'memoryReservation': int(describe_task_definition_response["taskDefinition"]["memory"]),
                'links': container_definition["links"] if container_definition.get("links") is not None else [],
                'portMappings': container_definition["portMappings"],
                'essential': container_definition["essential"],
                'entryPoint': container_definition["entryPoint"] if container_definition.get("entryPoint") is not None else [],
                'command': container_definition["command"] if container_definition.get("command") is not None else [],
                'environment': container_definition["environment"] if container_definition.get("environment") is not None else [],
                'environmentFiles': [{'value': arn, 'type': 's3'}],
                'mountPoints': container_definition["mountPoints"] if container_definition.get("mountPoints") is not None else [],
                'volumesFrom': container_definition["volumesFrom"] if container_definition.get("volumesFrom") is not None else [],
                'dnsSearchDomains': container_definition["dnsSearchDomains"] if container_definition.get("dnsSearchDomains") is not None else [],
                'dockerSecurityOptions': container_definition["dockerSecurityOptions"] if container_definition.get("dockerSecurityOptions") is not None else [],
                # 'logConfiguration': container_definition["logConfiguration"] if container_definition.get("logConfiguration") is not None else [],
                'systemControls': container_definition["systemControls"] if container_definition.get("systemControls") is not None else [],
            }
        ],
        cpu=describe_task_definition_response["taskDefinition"]["cpu"],
        memory=describe_task_definition_response["taskDefinition"]["memory"],
        volumes=describe_task_definition_response["taskDefinition"]["volumes"],
        placementConstraints=describe_task_definition_response["taskDefinition"]["placementConstraints"],
        requiresCompatibilities=describe_task_definition_response["taskDefinition"]["requiresCompatibilities"],
        taskRoleArn= describe_task_definition_response["taskDefinition"]["taskRoleArn"],
        executionRoleArn= describe_task_definition_response["taskDefinition"]["executionRoleArn"]
    )
    print(register_task_definition_response)

    return register_task_definition_response

def describe_services(client_ecs , service_name , ecs_cluster):
    describe_services_response = client_ecs.describe_services(
        cluster=ecs_cluster,
        services=[
            service_name
        ],
    )

    return describe_services_response

def update_service(client_ecs  , cluster , service , task_revision):
    update_service_response = client_ecs.update_service(
        cluster=cluster,
        service=service,
        taskDefinition= task_revision,
    )

    print(f'update_service_response: {update_service_response}')

