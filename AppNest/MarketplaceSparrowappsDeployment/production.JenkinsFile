
def marketplace_sparrowapps = [
  'Surveysparrow-Marketplace/AppNest-SparrowApps-Deployments/Marketplace-SparrowApps-US': [
    AWS_DEFAULT_REGION: 'us-east-1',
    ECR_REPO_NAME: 'ss-sparrowapps',
    TASK_DEFINITION_NAME: 'ss-sparrowapps',
    ECS_CLUSTER: 'ss-sparrowapps-cluster',
    ECS_SERVICE: 'sparrowapps_service',
    BUCKET_NAME: 'ss-marketplace-ecs-us',
    ENV_OBJECT: 'current-us.env',
  ],
  'Surveysparrow-Marketplace/AppNest-SparrowApps-Deployments/Marketplace-SparrowApps-EU': [
    AWS_DEFAULT_REGION: 'eu-central-1',
    ECR_REPO_NAME: 'ss-sparrowapps',
    TASK_DEFINITION_NAME: 'ss-sparrowapps',
    ECS_CLUSTER: 'ss-sparrowapps-cluster-EU',
    ECS_SERVICE: 'sparrowapps_service',
    BUCKET_NAME: 'ss-marketplace-ecs-eu',
    ENV_OBJECT: 'current-eu.env',
  ],
  'Surveysparrow-Marketplace/AppNest-SparrowApps-Deployments/Marketplace-SparrowApps-AP': [
    AWS_DEFAULT_REGION: 'ap-south-1',
    ECR_REPO_NAME: 'ss-sparrowapps-marketplace',
    TASK_DEFINITION_NAME: 'ss-sparrowapps',
    ECS_CLUSTER: 'ss-sparrowapps-cluster',
    ECS_SERVICE: 'sparrowapps_service',
    BUCKET_NAME: 'ss-marketplace-ecs-ap',
    ENV_OBJECT: 'current-ap.env',
  ],
  'Surveysparrow-Marketplace/AppNest-SparrowApps-Deployments/Marketplace-SparrowApps-UK': [
    AWS_DEFAULT_REGION: 'eu-west-2',
    ECR_REPO_NAME: 'ss-sparrowapps-marketplace',
    TASK_DEFINITION_NAME: 'ss-sparrowapps',
    ECS_CLUSTER: 'ss-sparrowapps-cluster',
    ECS_SERVICE: 'sparrowapps_service',
    BUCKET_NAME: 'ss-marketplace-ecs-uk',
    ENV_OBJECT: 'current-uk.env',
  ],
  'Surveysparrow-Marketplace/AppNest-SparrowApps-Deployments/Marketplace-SparrowApps-ME': [
    AWS_DEFAULT_REGION: 'me-central-1',
    ECR_REPO_NAME: 'ss-sparrowapps-marketplace',
    TASK_DEFINITION_NAME: 'ss-sparrowapps',
    ECS_CLUSTER: 'ss-sparrowapps-cluster',
    ECS_SERVICE: 'sparrowapps_service',
    BUCKET_NAME: 'ss-marketplace-ecs-me',
    ENV_OBJECT: 'current-me.env',
  ],
  'Surveysparrow-Marketplace/AppNest-SparrowApps-Deployments/Marketplace-SparrowApps-CA': [
    AWS_DEFAULT_REGION: 'ca-central-1',
    ECR_REPO_NAME: 'ss-sparrowapps-marketplace',
    TASK_DEFINITION_NAME: 'ss-sparrowapps',
    ECS_CLUSTER: 'ss-sparrowapps-cluster',
    ECS_SERVICE: 'sparrowapps_service',
    BUCKET_NAME: 'ss-marketplace-ecs-ca',
    ENV_OBJECT: 'current-me.env',
  ],
  'Surveysparrow-Marketplace/AppNest-SparrowApps-Deployments/Marketplace-SparrowApps-SY': [
    AWS_DEFAULT_REGION: 'ap-southeast-2',
    ECR_REPO_NAME: 'ss-sparrowapps-marketplace',
    TASK_DEFINITION_NAME: 'ss-sparrowapps',
    ECS_CLUSTER: 'ss-sparrowapps-cluster',
    ECS_SERVICE: 'sparrowapps_service',
    BUCKET_NAME: 'ss-marketplace-ecs-sy',
    ENV_OBJECT: 'current-me.env',
  ]
]

pipeline
{
    // Running in agent DockerBuilderFleet
    agent 
    { 
        label 'MarketplaceDockerbuilderFleet' 
    }

    // Global Environment variables
    environment
    {

        AWS_DEFAULT_REGION   = "${marketplace_sparrowapps[env.JOB_NAME]['AWS_DEFAULT_REGION']}"
        ECR_REPO_NAME        = "${marketplace_sparrowapps[env.JOB_NAME]['ECR_REPO_NAME']}"
        TASK_DEFINITION_NAME = "${marketplace_sparrowapps[env.JOB_NAME]['TASK_DEFINITION_NAME']}"
        ECS_CLUSTER          = "${marketplace_sparrowapps[env.JOB_NAME]['ECS_CLUSTER']}"
        ECS_SERVICE          = "${marketplace_sparrowapps[env.JOB_NAME]['ECS_SERVICE']}"
        BUCKET_NAME          = "${marketplace_sparrowapps[env.JOB_NAME]['BUCKET_NAME']}"
        ENV_OBJECT           = "${marketplace_sparrowapps[env.JOB_NAME]['ENV_OBJECT']}"

        APPNEST_GIT_URL      = '*****************:surveysparrow/sparrowapps.git'

    }

    // Parameter which we will be giving at the start of a new Build Deployment
    parameters
    {
        // The branch which needs to be deployed in ECS
        string (
            name : "BranchName",
            defaultValue : "production", 
            description : "Enter the branch which needs to be built"
        )
        // To Build A Docker image alone for Manual Migration Runs
        booleanParam ( 
            name: 'dockerBuildOnly',
            defaultValue: true, 
            description: 'By DEFAULT it will TRUE as by default it will build a docker image, push and deploy that image' 
        )
        // To have a deployment alone
        booleanParam ( 
            name: 'deployOnly',
            defaultValue: true, 
            description: 'By DEFAULT it will be True as it will deploy that image that has been built' 
        )
    }

    stages
    {
        stage("Git Clone")
        {
            steps
            {
                script
                {
                    variables = load 'Helpers/variables.groovy'
                    notifier  = load 'Helpers/notifications.groovy'
                    methods   = load 'Helpers/methods.groovy'

                    currentBuild.displayName = "Deploying: ${params.BranchName}"
                    if ( params.dockerBuildOnly && params.deployOnly )
                    {
                        echo "Normal Deployment Slack message"
                        notifier.sendSlackNotificationMarketplace( "#0066CC", "MARKETPLACE_SPARROWAPPS_DEPLOYMENT_STARTED" )
                    }
                    else
                    {
                        echo "Docker Build Deployment Slack message"
                        notifier.sendSlackNotificationMarketplace( "#0066CC", "MARKETPLACE_SPARROWAPPS_DOCKER_BUILD_STARTED" )
                    }
                
                    dir('sparrowapps') 
                    {
                        methods.cloneRepoWithGit(env.APPNEST_GIT_URL, params.BranchName)
                    }
                }
            }
        }
        stage("Build the docker image and upload to ECR")
        {
            when 
            {
                expression 
                {
                    return params.dockerBuildOnly
                }
            }
            steps
            {
                script
                {
                    echo "<----------------------Build the docker image-------------------->"

                    dir('sparrowapps')
                    {
                        methods.initDocker(env.AWS_DEFAULT_REGION, variables.APPNEST_ECR_REPO_APP[env.AWS_DEFAULT_REGION])

                        echo "Getting the last git COMMIT HASH"
                        script
                        {
                            GIT_COMMIT_HASH = sh (script: "git log -n 1 --pretty=format:'%H' | head -c 7", returnStdout: true)
                            echo "<------------------GIT_COMMIT_HASH---------------------------->"
                            echo "${GIT_COMMIT_HASH}"
                            echo "<------------------------------------------------------------->"
                            ECR_BUILD_ID = sh (script: "echo '${params.BranchName}_${GIT_COMMIT_HASH}_${currentBuild.number}'", returnStdout: true)
                            echo "<------------------ECR_BUILD_ID------------------------------->"
                            echo "${ECR_BUILD_ID}"
                            echo "<------------------------------------------------------------->"
                            //Generating the image name which will be be a combination of (BRANCH_NAME + LAST_GIT_COMMIT_HASH + CURRENT_JENKINS_BUILD_NO).
                        }
                        
                        echo "Building Docker Image with ECR TAG"
                        def imageName = "${variables.APPNEST_ECR_REPO_APP[env.AWS_DEFAULT_REGION]}/${env.ECR_REPO_NAME}:$ECR_BUILD_ID"
                        sh "docker build -t ${env.ECR_REPO_NAME} ."
                        sh "docker tag ${env.ECR_REPO_NAME}:latest $imageName"

                        echo "Pushing Docker image to ECR"
                        sh "docker push $imageName"

                        echo "Removing all the Docker Images"
                        sh "docker rmi -f \$(docker images -aq) | true"
                    }
                }
            }
        }
        stage("Upload the environment variables")
        {
            when 
            {
                expression 
                {
                    return params.deployOnly
                }
            }
            steps
            {
                script
                {
                    echo "<----------------Upload the ENV variables to S3----------------------->"
                    
                    echo "Cloning the production config Repo"
                    dir ('surveysparrow-production-config') 
					{
						methods.cloneConfig()
					}

                    echo "Getting the workspace path"
                    workspace_path = sh (script: "pwd | tr -d '\n'", returnStdout: true)
                    
                    echo "Pushing all the new Env to S3 for Deployment"
                    sh "aws s3 cp $workspace_path/surveysparrow-production-config/marketplace/sparrowapps/${env.AWS_DEFAULT_REGION}/production.env s3://${env.BUCKET_NAME}/${env.ENV_OBJECT}"
                    
                }
            }
        }
        stage("Trigger ECS deployment")
        {
            when 
            {
                expression 
                {
                    return params.deployOnly
                }
            }
            steps
            {
                script
                {
                    echo "<-----------------------Trigger ECS deployment------------------------->"

                    echo  "Installing boto3"
                    sh "python3 -m pip install boto3"

                    echo  "Running the python script for task definition creation and updating the service"

                    def registryUrl = "${variables.APPNEST_ECR_REPO_APP[env.AWS_DEFAULT_REGION]}/${env.ECR_REPO_NAME}"

                    sh "AWS_DEFAULT_REGION=${env.AWS_DEFAULT_REGION} AWS_REGISTRY_URL=${registryUrl} ECR_REPO_NAME=${env.ECR_REPO_NAME} TASK_DEFINITION_NAME=${env.TASK_DEFINITION_NAME} ECS_CLUSTER=${env.ECS_CLUSTER} ECS_SERVICE=${env.ECS_SERVICE} BUCKET_NAME=${env.BUCKET_NAME} ENV_OBJECT=${env.ENV_OBJECT} python3 AppNest/MarketplaceSparrowappsDeployment/ecs_deploy.py"
                }
            }
        }
    }
    post 
    {
        always 
        {
            script
            {
                currentBuild.displayName = "Deployed: ${params.BranchName}"
                cleanWs()
            }
        }
        success 
        {
            script
            {
                if ( params.dockerBuildOnly )
                {
                    notifier.sendSlackNotificationMarketplace("2ECC71" , "MARKETPLACE_SPARROWAPPS_DOCKER_BUILD_SUCCESS")
                }
                else
                {
                    notifier.sendSlackNotificationMarketplace("2ECC71" , "MARKETPLACE_SPARROWAPPS_DEPLOYMENT_SUCCESS")
                }
            }
        }
        failure 
        {
            script
            {
                notifier.sendSlackNotificationMarketplace("#E74C3C", "MARKETPLACE_SPARROWAPPS_DEPLOYMENT_FAILURE")
            }
        }
        aborted 
        {
            script
            {
                notifier.sendSlackNotificationMarketplace("#9D9D9D", "MARKETPLACE_SPARROWAPPS_DEPLOYMENT_ABORTED")
            }
        }
    }
}