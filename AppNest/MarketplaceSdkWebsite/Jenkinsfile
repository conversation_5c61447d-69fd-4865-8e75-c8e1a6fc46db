
pipeline
{
    agent 
    {
      label 'MarketplaceDockerbuilderFleet'
    }

    environment 
    {
        AWS_DEFAULT_REGION = "us-east-1"
        ECR_REGISTRY_URL = "234462785196.dkr.ecr.us-east-1.amazonaws.com"
        DEV_PORTAL_GIT_URL = "*****************:surveysparrow/sparrowapps-sdk-documentaion.git"
        ECR_NAME = "ss-marketplace-sdk-website"
        EKS_CLUSTER = "ss-marketplace-cluster"
        DEPLOYMENT = "sdk-documnetation-frontend"
        NAMESPACE = "sdk-documnetation"
        DISTRIBUTION_ID = "E2DP421MEF6X3F"
        BITBUCKET_CREDENTIALS = "BitBucketPrivateKey"
    }

    parameters 
    {
        string (
            defaultValue: 'production',
            description: 'Branch that is to be deployed.',
            name: 'branchName'
        )
    }
    stages 
    {
        stage("Git Clone")
        {
            steps
            {
                script
                {
                    notifier = load 'Helpers/notifications.groovy'
					variables = load 'Helpers/variables.groovy'

                    currentBuild.displayName = "Deploying: ${params.branchName} : ${currentBuild.number}"   

                    notifier.sendSlackNotificationMarketplace("#0066CC", "MARKETPLACE_SDK_DEPLOYMENT_STARTED")

                    dir ('marketplace-sdk-website') 
                    {
                        git branch: "${params.branchName}", credentialsId: "${variables.BB_CREDENTIAL_ID}", url: "${env.DEV_PORTAL_GIT_URL}"
                    }
                }
            }
        }
        stage("Docker Build")
        {
            steps
            {
                script
                {
                    workspace_path = sh (script: "pwd | tr -d '\n'", returnStdout: true)
                    
                    dir ('marketplace-sdk-website')
                    {
                        sh "sudo systemctl start docker"
                        sh "sleep 45"
                        
                        echo "LOGGING INTO ECR"
                        sh "aws ecr get-login-password --region ${env.AWS_DEFAULT_REGION} | docker login --username AWS --password-stdin ${env.ECR_REGISTRY_URL}"

                        echo "BUILDING AND TAGGING DOCKER IMAGE"
                        sh "docker build -t ${env.ECR_REGISTRY_URL}/${env.ECR_NAME}:latest ."

                        echo "PUSHING THE TAGED DOCKER IMAGE"
						sh "docker push ${env.ECR_REGISTRY_URL}/${env.ECR_NAME}:latest"
                    }   
                }
            }
        }
        stage("EKS Deployment")
		{
			steps
			{
				script
                {
                    echo 'Deploying the application on EKS Cluster...'
					
                    echo "LOGGING INTO EKS CLUSTER"
					sh "aws eks --region ${env.AWS_DEFAULT_REGION} update-kubeconfig --name ${env.EKS_CLUSTER}"

					echo "RESTARING THE DEPLOYMENT TO PULL LATEST IMAGE"
					sh "kubectl rollout restart deployment/${env.DEPLOYMENT} -n ${env.NAMESPACE}"
                    
					echo "WAITING STILL THE RESTART IS COMPLETED"
					sh "kubectl rollout status deployment/${env.DEPLOYMENT} -n ${env.NAMESPACE}"

                    notifier.sendSlackNotificationMarketplace("#D2DE32", "MARKETPLACE_SDK_DEPLOYMENT_SUCCESS")
                }
			}
		}
        stage("CDN Invalidation")
		{
			steps
			{
				script
                {
                    notifier.sendSlackNotificationMarketplace("#61A3BA", "MARKETPLACE_SDK_CACHE_CLEAR_STARTED")
                    echo 'Creating Cloudfront Invalidation'
					def create_invalidation_response = sh( script: "aws cloudfront create-invalidation --distribution-id ${env.DISTRIBUTION_ID} --paths '/*'" , returnStdout: true).trim()
                    def create_invalidation_obj = readJSON text: "${create_invalidation_response}" 
                    def invalidation_id = create_invalidation_obj.Invalidation.Id
                    echo "Invalidation ID: ${invalidation_id}"
                    while(true)
                    {
                        def get_invalidation_response =  sh( script: "aws cloudfront get-invalidation --id ${invalidation_id} --distribution-id ${env.DISTRIBUTION_ID}" , returnStdout: true).trim()
                        def get_invalidation_obj = readJSON text: "${get_invalidation_response}"
                        def status = get_invalidation_obj.Invalidation.Status
                        if( status == "Completed" )
                        {
                            echo "Invalidation Completed $status"
                            break
                        }
                        
                        echo "Invalidation Not yet Completed $status"
                        sleep(10)
                    }
                    notifier.sendSlackNotificationMarketplace("#A2C579", "MARKETPLACE_SDK_CACHE_CLEAR_COMPLETED")
                }
			}
		}
    }
    post
    {
		always 
		{
            script
            {
                currentBuild.displayName = "Deployed: ${params.branchName} : ${currentBuild.number}"   
                cleanWs()
            }
		}
		success 
        {
            script 
            {
                notifier.sendSlackNotificationMarketplace("#2ECC71", "MARKETPLACE_SDK_DEPLOYMENT_SUCCESS")
            }
        }
        failure 
        {
            script 
            {
                notifier.sendSlackNotificationMarketplace("#E74C3C", "MARKETPLACE_SDK_DEPLOYMENT_FAILURE")
            }
        }
        aborted 
        {
            script 
            {
                notifier.sendSlackNotificationMarketplace("#9D9D9D", "MARKETPLACE_SDK_DEPLOYMENT_ABORTED")
            }
        }
    }
}
