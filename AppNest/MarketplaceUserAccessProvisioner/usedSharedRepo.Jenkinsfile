@Library('sparrow@userAccessProvision') _

pipeline {
    agent {
        label "JenkinsWorkerFleet"
    }

    parameters {
        choice(name: "runType", choices: ["Create User",  "Update Password", "Delete User"], description: 'User Provisioning Method')
        string(
            description: 'Target user\'s email address', 
            name: 'email'
        )
        choice(name: "bastionUser", choices: ["Do Nothing", "Create Bastion User", "Update SSH", "Delete User"], description: 'Bastion User Management')
        choice(name: "region", choices: ["us-east-1", "ap-south-1", "eu-central-1", "eu-west-2", "me-central-1"], description: 'Select the region')
        activeChoiceHtml(
            choiceType: 'ET_FORMATTED_HTML',
            name: 'sshkey',
            omitValueField: false,
            randomName: 'choice-parameter-129039043904949042390',
            referencedParameters: 'bastionUser',
            script: groovyScript(
                fallbackScript: [
                    classpath: [],
                    oldScript: '',
                    sandbox: true,
                    script: 'return "<h1>Error</h1>"'
                ],
                script: [
                    classpath: [],
                    oldScript: '',
                    sandbox: true,
                    script: '''if (bastionUser.equals(\'Create Bastion User\') || bastionUser.equals(\'Update SSH\')) {
                    return """
                    <div class=\'jenkins-form-description\'>Public SSH Key (~/ssh/id_rsa.pub) [Not required for deleting the user]</div><br>
                    <input name=\'value\' type=\'text\' value=\'\' class=\'jenkins-input\' required><br>
                    """
                    } else {
                        return """
                        <h2>Not required for deleting the user or Do Nothing</h2>
                        """
                    }'''
                ]
            )
        )
        reactiveChoice(
            choiceType: 'PT_MULTI_SELECT', description: 'Select the Postgres Shards', filterLength: 1, 
            filterable: false, name: 'postgresShards',
            randomName: 'choice-parameter-61162979490779012', referencedParameters: 'region', 
            script: groovyScript(
                fallbackScript: [
                    classpath: [], 
                    oldScript: '', sandbox: true, 
                    script: 'return[\'ERROR\']'
                ], 
                script: [
                    classpath: [], 
                    oldScript: '', 
                    sandbox: true, 
                    script: '''if (region.equals("us-east-1")) {
                            return ["sparrowapps-US"]
                            } else if (region.equals("ap-south-1")) {
                            return ["sparrowapps-AP",]
                            } else if (region.equals("eu-central-1")) {
                            return ["sparrowapps-EU"]
                            } else if (region.equals("me-central-1")) {
                            return ["sparrowapps-ME"]
                            } else if (region.equals("eu-west-2")) {
                            return ["sparrowapps-UK"]
                            }
                        '''
                ]
            )
        )
        reactiveChoice(
            choiceType: 'PT_MULTI_SELECT', description: 'Select the RabbitMQ Broker', filterLength: 1, 
            filterable: false, name: 'rabbitMqBrokers',
            randomName: 'choice-parameter-61162979490779015', referencedParameters: 'region', 
            script: groovyScript(
                fallbackScript: [
                    classpath: [], 
                    oldScript: '', sandbox: true, 
                    script: 'return[\'ERROR\']'
                ], 
                script: [
                    classpath: [], 
                    oldScript: '', 
                    sandbox: true, 
                    script: '''if (region.equals("ap-south-1")) {
                            return ["RabbitMq-AP",]
                            } else if (region.equals("me-central-1")) {
                            return ["heimdall-rabbitmq-ME"]
                            } else if (region.equals("eu-west-2")) {
                            return ["heimdall-rabbitmq-UK"]
                            }
                        '''
                ]
            )
        )
        reactiveChoice(
            choiceType: 'PT_SINGLE_SELECT',
            filterLength: 1,
            filterable: false,
            name: 'access',
            referencedParameters: 'runType',
            randomName: 'choice-parameter-2982390329023223',
            script: [
                $class: 'GroovyScript',
                fallbackScript: [
                    classpath: [],
                    oldScript: '',
                    sandbox: true,
                    script: ''
                ],
                script: [
                    classpath: [],
                    oldScript: '',
                    sandbox: true,
                    script: '''if(runType.equals('Create User')) {
                        return ['read', 'write']
                    }'''
                ]
            ]
        )
    }

    
    stages {
        stage("Initial Setup") {
            steps {
                script {
                    variables                = load 'Helpers/variables.groovy'
                    notification             = load 'Helpers/notifications.groovy'
                    methods                  = load 'Helpers/methods.groovy'
                    nodeIP                   = variables.BASTION_NODES_MARKETPLACE[params.region]
                    ssh_key                  = (params.sshkey == "") ? "" : params.sshkey.replace(",", "")
                    username                 = params.email.replace("@surveysparrow.com", "")
                    username                 = username.replace(".", "")
                    hosts                    = params.postgresShards.replaceAll(",", " ")
                    rabbitmqHost             = params.rabbitMqBrokers.replaceAll(",", " ")
                    validity_days            = -1
                    bastion_validity_days    = "-1"
                    approved_by              = params.approvedBy
                    valid_till      = "Infinity"
                    bastion_valid_till = "Infinity"
                    // if (validity_days != "" && validity_days.toInteger() > 0) {
                    //     valid_till      = methods.getValidTill(validity_days.toInteger())
                    // } else if (validity_days == "-1") {
                        // valid_till      = "Infinity"
                    // }

                    // if (bastion_validity_days != "" && bastion_validity_days.toInteger() > 0) {
                    //     bastion_valid_till = methods.getValidTill(bastion_validity_days.toInteger())
                    // } else if (bastion_validity_days == "-1") {
                        // bastion_valid_till = "Infinity"
                    // }
                }
            }
        }

        stage("Bastion User") {
            when {
                expression { params.bastionUser != "Do Nothing" }
            }
            steps {
                script {
                    sshagent(['bastion-master']) {
                        userAccessProvisioner.handleBastionUser(variables, nodeIP, params, username, validity_days)
                        if (params.bastionUser.equals("Create Bastion User")){
                            // notification.sendStatusBasedNotification("BASTION_USER_SUCCESS", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        } else if (params.bastionUser.equals("Update SSH")){
                            // notification.sendStatusBasedNotification("BASTION_USER_UPDATE_INFO", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        } else if (params.bastionUser.equals("Delete User")){
                            // notification.sendStatusBasedNotification("BASTION_USER_DELETE_INFO", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        }
                    }
                }
            }
        }

        stage("Postgres User Provisioning") {
            when {
                expression { params.postgresShards != "" }
            }
            steps {
                script {
                    sshagent(['bastion-master']) {
                        userAccessProvisioner.handlePostgresUser(variables, nodeIP, params, username, hosts, validity_days)
                        if (params.runType == "Create User") {
                            // notification.sendStatusBasedNotification("BASTION_POSTGRES_USER_SUCCESS", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        } else if (params.runType == "Update Password") {
                            // notification.sendStatusBasedNotification("BASTION_POSTGRES_USER_UPDATE_INFO", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        } else if (params.runType == "Delete User") {
                            // notification.sendStatusBasedNotification("BASTION_POSTGRES_USER_DELETE_INFO", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        }
                    }
                }
            }
        }



        stage("RabbitMq User Provisioning") {
            when {
                expression { params.rabbitMqBrokers != "" }
            }
            steps {
                script {
                    sshagent(['bastion-master']) {
                        userAccessProvisioner.handleRabbitMqUser(variables, nodeIP, params, username, rabbitmqHost, validity_days)
                        if (params.runType == "Create User") {
                            // notification.sendStatusBasedNotification("BASTION_RABBITMQ_USER_SUCCESS", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        } else if (params.runType == "Update Password") {
                            // notification.sendStatusBasedNotification("BASTION_RABBITMQ_USER_UPDATE_INFO", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        } else if (params.runType == "Delete User") {
                            // notification.sendStatusBasedNotification("BASTION_RABBITMQ_USER_DELETE_INFO", variables.PRODUCTION_ACCESS_REQUEST_CHANNEL)
                        }
                    }
                }
            }
        }

        
    }

    post {
        always {
            cleanWs()
        }
    }
}