def notification_type = [
    "Restart": [
        "started": "BULLBOARD_RESTART_STARTED",
        "successful": "BULLBOARD_RESTART_SUCCESSFUL",
        "failed": "BULLBOARD_RESTART_FAILED",
        "aborted": "BULLBOARD_RESTART_ABORTED"
    ],
    "ConfigUpdate": [
        "started": "BULLBOARD_CONFIGUPDATE_STARTED",
        "successful": "BULLBOARD_CONFIGUPDATE_SUCCESSFUL",
        "failed": "BULLBOARD_CONFIGUPDATE_FAILED",
        "aborted": "BULLBOARD_CONFIGUPDATE_ABORTED"
    ]
]

pipeline {
    agent {
        label 'JenkinsWorkerFleet'
    }

    parameters { 
        choice name: 'pipeline_type', 
            choices: [ 
                'ConfigUpdate',
                'Restart'
            ], 
            description: 'Type of pipeline to run'
        choice name: 'region', choices: ['us-east-1', 'ap-south-1', 'eu-central-1', 'eu-west-2', 'me-central-1', 'ap-southeast-2', 'ca-central-1'], description: 'Region to deploy the service'
    }

    stages {
        stage('Initial Pipeline Setup') {
            steps {       
                script {
                    variables         = load "Helpers/variables.groovy"
                    methods           = load "Helpers/methods.groovy"
                    notifier          = load "Helpers/notifications.groovy"
                    
                    methods.prepInstance()
                    notifier.sendSlackNotification(notification_type[params.pipeline_type]['started'])     
                }
            }
        }
        stage('Pull Config Repo from Bitbucket') {
            when {
                expression { params.pipeline_type == 'ConfigUpdate' }
            }
            steps {
                script {
                    dir (variables.CONFIG_REPO_FOLDER) {
                        methods.cloneConfig()
                    }
                }
            }
        }

        stage('Update Context Files') {
            when {
                expression { params.pipeline_type == 'ConfigUpdate' }
            }
            steps {
                script {
                    sh "python3 Helpers/updatecm.py -c bullboard -e ${params.region}"
                }
            }
        }

        stage('Update Config/Restart') {
            steps {
                script {
                    sh "aws eks --region ${params.region} update-kubeconfig --name ${variables.CLUSTER_NAME_REGION_MAP[params.region]}"
                    if (params.pipeline_type == 'ConfigUpdate') {
                        sh "kubectl apply -f Helpers/cm-template.yaml"
                    }
                    methods.restartDeployment("${variables.REGION_APP_MAP[params.region]}-bull-dashboard", variables.DEFAULT_NAMESPACE)
                }
            }
        }
    }

    post {
        always {
            echo "Cleaning Deployment Service Workspace"
            cleanWs()
        }
        success {
            script{
                notifier.sendSlackNotification(notification_type[params.pipeline_type]['successful'])
            }
        }
        failure {
            script {
                notifier.sendSlackNotification(notification_type[params.pipeline_type]['failed'])
            }
        }
        aborted {
            script{
                notifier.sendSlackNotification(notification_type[params.pipeline_type]['aborted'])
            }
        }
    }
}