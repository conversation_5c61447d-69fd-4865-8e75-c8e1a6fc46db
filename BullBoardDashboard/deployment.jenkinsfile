pipeline {
    agent {
        label 'ArmDockerBuilderFleet'
    }

    environment {
        IMAGE_NAME = "bull-dashboard"
    }

    parameters {
        string name: 'branchname', defaultValue: 'main', description: 'Service repo\'s branch to deploy'
        choice name: 'region', choices: ['us-east-1', 'ap-south-1', 'eu-central-1', 'eu-west-2', 'me-central-1', 'ap-southeast-2', 'ca-central-1'], description: 'Region to deploy the service'
    }

    stages {
        stage('Pull Code and Checkout Branch') {
            steps {       
                script{
                    variables         = load "Helpers/variables.groovy"
                    methods           = load "Helpers/methods.groovy"
                    notifier          = load "Helpers/notifications.groovy"

                    methods.prepInstance()
                    notifier.sendSlackNotification('BULLBOARD_DEPLOYMENT_STARTED')
                    dir('bullboard') {
                        methods.cloneRepo(variables.BULLBOARD_REPO_URL, params.branchname)
                    }             
                }
            }
        }

        stage('Image Build & Push') {
            steps {
                script {
                    methods.initDocker(params.region, variables.ECR_REPO_APP[params.region])
                    dir('bullboard') {
                        sh "docker build -f Dockerfiles/production.dockerfile -t ${variables.ECR_REPO_APP[params.region]}/$IMAGE_NAME:latest ."
                    }
                    sh "docker push ${variables.ECR_REPO_APP[params.region]}/$IMAGE_NAME:latest"
                }
            }
            post {
                always {
                    echo "Cleaning Docker Build"
                    sh "docker rmi -f \$(docker images -aq) | true"
                }
            }
        }

        stage('Pull Config Repo from Bitbucket') {
            steps {
                script {
                    dir (variables.CONFIG_REPO_FOLDER) {
                        methods.cloneConfig()
                    }
                }
            }
        }

        stage('Update Context Files') {
            steps {
                script {
                    sh "python3 Helpers/updatecm.py -c bullboard -e ${params.region}"
                }
            }
        }

        stage('Deploy to EKS') {
            steps {
                script {
                    sh "aws eks --region ${params.region} update-kubeconfig --name ${variables.CLUSTER_NAME_REGION_MAP[params.region]}"
                    sh "kubectl apply -f Helpers/cm-template.yaml"
                    methods.restartDeployment("${variables.REGION_APP_MAP[params.region]}-bull-dashboard", variables.DEFAULT_NAMESPACE)
                }
            }
        }
    }

    post {
        always {
            echo "Cleaning Deployment Service Workspace"
            cleanWs()
        }
        success {
            script {
                notifier.sendSlackNotification('BULLBOARD_DEPLOYMENT_SUCCESSFUL')
            }
        }

        failure {
            script {
                notifier.sendSlackNotification('BULLBOARD_DEPLOYMENT_FAILED')
            }
        }

        aborted {
            script {
                notifier.sendSlackNotification('BULLBOARD_DEPLOYMENT_ABORTED')
            }
        }
    }
}