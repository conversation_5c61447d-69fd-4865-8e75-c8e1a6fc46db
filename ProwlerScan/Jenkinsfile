pipeline {
  agent {
    label 'ProwlerScannerFleet'
  }

  triggers {
    cron('H 8 * * 1')
  }

  environment {
    SLACK_API_TOKEN = credentials('SlackBotToken')
    JIRA_TOKEN      = credentials('JiraAuthToken')
  }

  stages {
    stage('SETUP') {
      steps {
        script {
          variables = load 'Helpers/variables.groovy'
          methods   = load 'Helpers/methods.groovy'

          methods.prepInstance()
          methods.installProwler()
        }
      }
    }

    stage('RUN_PROWLER') {
      steps {
        script {
          methods.runProwlerInParallel()
        }
      }
    }

    stage('CREATE_JIRA_TICKETS') {
      steps {
        script {
          sh "JIRA_TOKEN=$JIRA_TOKEN python3 Helpers/prowlerjira.py"
        }
      }
    }
  }
}
