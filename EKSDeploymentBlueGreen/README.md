# EKS Blue Green Deployment Pipeline

A pipeline to deploy to multiple regions in EKS using Blue Green Deployment Strategy

## Input to Pipeline
  - BranchName - The branch that should be deployed.
  - CompileAssets - If we should do webpack build.

## Pipeline Steps
  - DEPLOYMENT PREP
    - Fetch the asset version from parameter store if compile assets is false, else generate new and insert into parameter store.
    - Fetch the Blue and Green Deployments, ConfigMaps, Services from Parameter Store
    - Check if we are deploying the same thing again.

  - CHECK MIGRATIONS
    - Migrations are automated here, Spawn an instance in that Specific Region and run the specified command to check if PRE and POST Migration Exists

  - Build
    - WEBPACK BUILD
      - If compile assets is true, run webpack build
    - RUNNING_PRE_MIGRATIONS
      - Run Migrations which are marked as PRE
    - DOCKER IMAGE BUILD
      - Build Docker image and push to ECR
  
  - VERIFY WEBPACK BUILD
    - Verify if the webpack build was proper by doing curl to crucial bundles
  
  - DEPLOY_TO_BLUE
    - From the Blue Deployment fetched in DEPLOYMENT_PREP, Update the ConfiigMap of the Blue Deployment.
    - Clone the EKS repo and move to the Templates Directory
    - Apply the Templates to create a Blue Deployment based on Deployment and Release Tag
    - Update the Blue Service to Point to Blue Deployments
    - Create the Blue HAProxy Deployment

  - SWITCH TO BLUE GREEN
    - Wait for Approval to Switch to Blue Green Deployment Config of HAProxy Forwarder
    - Update the HAProxy forwarder config to Blue-Green which will forward specific account traffic to Blue Deployment
    - Restart the HAProxy Forwarder

  - PROMOTE BLUE TO GREEN
    - Wait for Approval to Promote Blue to Green and route all traffic to Current Blue Deployment, which will be green once we switch.
    - Update the Green Service to Point to the Blue Deployment
    - Update the HAProxy Forwarder Deployment config to use Green Alone

  - RUNNING POST MIGRATIONS
    - Once the Deployment is Done, spawn an instance in that specific region and run the specified command to run the POST migrations

  - CLEANUP BLUE
    - Delete all the resources created for the Blue Deployment, now we will be deleting the green deployment, which will be serving the old code.