def CLUSTER_NAME_JOBNAME_MAP = [
  'us-east-1': 'ss-production-us',
  'eu-central-1': 'ss-production-eu',
  'ap-south-1': 'ss-production-ap',
  'me-central-1': 'ss-production-me',
  'eu-west-2': 'ss-production-eu-ln',
  'ap-southeast-2': 'ss-production-ap-sy',
  'ca-central-1': 'ss-production-ca'
]

def JOBNAME_REGION_MAP = [
  'App-v1/BlueGreenDeployments/US-BlueGreen': 'us-east-1',
  'App-v1/BlueGreenDeployments/EU-BlueGreen': 'eu-central-1',
  'App-v1/BlueGreenDeployments/AP-BlueGreen': 'ap-south-1',
  'App-v1/BlueGreenDeployments/ME-BlueGreen': 'me-central-1',
  'App-v1/BlueGreenDeployments/UK-BlueGreen': 'eu-west-2',
  'App-v1/BlueGreenDeployments/SY-BlueGreen': 'ap-southeast-2',
  'App-v1/BlueGreenDeployments/CA-BlueGreen': 'ca-central-1',
]

STAGE_COUNT       = 11
CURRENT_STAGE_NUM = 0

pipeline {
  options {
    parallelsAlwaysFailFast()
  }

  agent {
    label "DockerBuilderFleet"
  }

  tools {
    nodejs "Node-v14.18.2"
  }

  parameters {
    string (
      description: "Release branch to be deployed.",
      name: 'releasebranch'
    )

    string (
      description: 'Deployment Tag',
      name: 'deploymenttag'
    )

    booleanParam (
      name: 'compileassets',
      defaultValue: true,
      description: 'If compile assets ?'
    )
  }
  
  environment {
    JOB_NAME = "${JOB_NAME_REGION_MAP[env.JOB_NAME]}"
    CLUSTER_NAME = "${CLUSTER_NAME_JOBNAME_MAP[env.JOB_NAME]}"
    SLACK_TOKEN = credentials('SlackBotToken')
  }

  stages {
    stage('DEPLOYMENT_PREP') {
      steps {
        script {
          env.CURRENT_STAGE = 'DEPLOYMENT_PREP'

          variables = load 'Helpers/variables.groovy'
          methods   = load 'Helpers/methods.groovy'
          notifier   = load 'Helpers/notifications.groovy'

          methods.prepInstance()
          actualJobName               = variables.BLUEGREEN_JOBNAME_REGION_MAP.find { it.value == env.JOB_NAME }?.key
          lastSuccessfulBuildDuration = methods.getLastSuccessfulBuildDuration(actualJobName)
          message_timestamp           = notifier.sendDeploymentProgressNotification(CURRENT_STAGE_NUM, lastSuccessfulBuildDuration - currentBuild.duration, null, 'DEPLOYMENT_STARTED')
          notifier.sendDeploymentNotification('DEPLOYMENT_STARTED')

          dir('app-v1') {
            methods.cloneApp()
            greenDeploymentParams = methods.getDeploymentParameters('green', env.JOB_NAME)
            blueDeploymentParams  = methods.getDeploymentParameters('blue', env.JOB_NAME)
            version               = params.compileassets ? methods.getAssetVersionFromRepo() : greenDeploymentParams.version
            packageJsonHash       = methods.checkToInstallNodeModules()
          }

          currentBuild.displayName = "#${params.deploymenttag}"
          currentBuild.description = "Starting Deployment - Blue - ${blueDeploymentParams.deployment} | Green - ${greenDeploymentParams.deployment}"

          if (blueDeploymentParams.deployment == greenDeploymentParams.deployment || blueDeploymentParams.configmap == greenDeploymentParams.configmap || blueDeploymentParams.service == greenDeploymentParams.service) {
            echo "[-] Blue and Green are same. Aborting deployment."
            error "[-] Blue and Green are same. Aborting deployment."
          }

          blueDeploymentParams.version = version
          blueDeploymentParams.image   = params.deploymenttag
          blueDeploymentParams.app     = params.deploymenttag
          echo "\n\n=====================================================\n\n"
          echo "Blue Deployment - ${blueDeploymentParams.deployment}\nBlue ConfigMap - ${blueDeploymentParams.configmap}\nBlue App - ${blueDeploymentParams.app}\nBlue Service - ${blueDeploymentParams.service}\nBlue Version - ${blueDeploymentParams.version}\nBlue Image = ${blueDeploymentParams.image}"
          echo "========================================================="
          echo "Green Deployment - ${greenDeploymentParams.deployment}\nGreen ConfigMap - ${greenDeploymentParams.configmap}\nGreen App - ${greenDeploymentParams.app}\nGreen Service - ${greenDeploymentParams.service}\nGreen Version - ${greenDeploymentParams.version}\nGreen Image = ${greenDeploymentParams.image}"
          echo "\n\n=====================================================\n\n"

          env.CURRENT_STAGE = 'DEPLOYMENT_PREP'
          notifier.sendDeploymentProgressNotification(methods.calculatePercentage(++CURRENT_STAGE_NUM, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
        }
      }
    }

    stage ('CHECK_MIGRATIONS') {
      agent {
        label "DockerBuilderFleet-${env.JOB_NAME}"
      }

      steps {
        script {
          notifier.sendDeploymentProgressNotification(methods.calculatePercentage(CURRENT_STAGE_NUM, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
          env.CURRENT_STAGE = 'CHECK_MIGRATIONS'
          migrations = methods.checkMigrations()
          if (!migrations.preMigrationExists) {
            STAGE_COUNT -= 1
          }

          if (!migrations.postMigrationExists) {
            STAGE_COUNT -= 1
          }
          notifier.sendDeploymentProgressNotification(methods.calculatePercentage(++CURRENT_STAGE_NUM, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
        }
      }

      post {
        success {
          script {
            notifier.sendDeploymentNotification('BUILD_STARTED')
          }
        }
      }
    }

    stage ('BUILD') {
      steps {
        script {
          notifier.sendDeploymentProgressNotification(methods.calculatePercentage(CURRENT_STAGE_NUM, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
          methods.buildAssetsAndDockerImage()
          notifier.sendDeploymentProgressNotification(methods.calculatePercentage(++CURRENT_STAGE_NUM, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
        }
      }

      post {
        failure {
          script {
            notifier.sendDeploymentNotification('BUILD_FAILED')
          }
        }
      }
    }

    stage ('VERIFY_WEBPACK_BUILD') {
      steps {
        script {
          notifier.sendDeploymentProgressNotification(methods.calculatePercentage(CURRENT_STAGE_NUM, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
          methods.verifyWebpackBuild(version)
          notifier.sendDeploymentProgressNotification(methods.calculatePercentage(++CURRENT_STAGE_NUM, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
        }
      }

      post {
        failure {
          script {
            notifier.sendDeploymentNotification('WEBPACK_BUILD_FAILED')
          }
        }
      }
    }

    stage('RUNNING_PRE_MIGRATIONS') {
      agent {
        label "DockerBuilderFleet-${env.JOB_NAME}"
      }

      when {
        beforeAgent true
        expression {
          return migrations.preMigrationExists
        }
      }

      steps {
        script {
          catchError(buildResult: 'SUCCESS', message: 'Stage Failed', stageResult: 'FAILURE') {
            env.CURRENT_STAGE = 'RUNNING_PRE_MIGRATIONS'
            notifier.sendDeploymentProgressNotification(methods.calculatePercentage(CURRENT_STAGE_NUM, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
            notifier.sendDeploymentNotification('PRE_MIGRATION_STARTED')
            input (message: "Approve to start running pre migrations.")

            preMigrationStatus = methods.runMigrations('PRE')
            if (preMigrationStatus == 0) {
              notifier.sendDeploymentProgressNotification(methods.calculatePercentage(++CURRENT_STAGE_NUM, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
              notifier.sendDeploymentNotification('PRE_MIGRATION_SUCCESSFUL')
              echo "Migrations ran successfully"
              sh "aws ssm put-parameter --region ${variables.DEFAULT_REGION} --name '/${env.JOB_NAME}/premigration/status' --value 0 --type SecureString --overwrite"
            } else {
              notifier.sendDeploymentNotification('PRE_MIGRATION_FAILED')
            }
          }
        }
      }
    }

    stage ('DEPLOY_TO_BLUE') {
      steps {
        script {
          env.CURRENT_STAGE = 'DEPLOY_TO_BLUE'
          notifier.sendDeploymentProgressNotification(methods.calculatePercentage(CURRENT_STAGE_NUM, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
          notifier.sendDeploymentNotification('DEPLOYMENT_TO_BLUE_STARTED')
          echo "\n ------------------------------------ Updating Blue Config Map -------------------------------------------------- \n"
          methods.updateAppConfig(blueDeploymentParams.deployment)
          echo "\n ------------------------------------ Updated Blue Config Map -------------------------------------------------- \n"
          env.CURRENT_STAGE = 'BLUE_CONFIG_UPDATED'

          methods.deployToBlue(blueDeploymentParams, greenDeploymentParams)
          notifier.sendDeploymentProgressNotification(methods.calculatePercentage(++CURRENT_STAGE_NUM, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
        }
      }

      post {
        failure {
          script {
            notifier.sendDeploymentNotification('DEPLOYMENT_TO_BLUE_FAILED')
          }
        }
      }
    }

    stage ('SWITCH_TO_BLUE_GREEN') {
      steps {
        script {
          env.CURRENT_STAGE = 'SWITCH_TO_BLUE_GREEN'
          dir ("surveysparrow-production-eks/kubectl/${env.JOB_NAME}/blue-green-templates") {
            notifier.sendDeploymentProgressNotification(methods.calculatePercentage(CURRENT_STAGE_NUM, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
            notifier.sendDeploymentNotification('SWITCH_TO_BLUE_GREEN')
            APPROVER = input ( message: "Deployment of Blue is Complete, update HAProxy config to Blue Green?", submitterParameter: 'APPROVER' )

            sh "aws eks update-kubeconfig --region ${env.JOB_NAME} --name ${CLUSTER_NAME}"

            echo "\n ------------------------------------ Updating HAProxy to Blue Green -------------------------------------------------- \n"
            methods.createOrUpdateHAProxyDeployment('haproxy-forwarder', 'blue-green')
            methods.waitForHAProxyDeploymentAndPods('haproxy-forwarder')
            echo "\n ------------------------------------ Updated HAProxy to Blue Green -------------------------------------------------- \n"

            env.CURRENT_STAGE = 'HAPROXY_BLUE_GREEN_SWITCHED'
            notifier.sendDeploymentProgressNotification(methods.calculatePercentage(++CURRENT_STAGE_NUM, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
          }
        }
      }

      post {
        failure {
          script {
            notifier.sendDeploymentNotification('SWITCH_TO_BLUE_GREEN_FAILED')
          }
        }
      }
    }

    stage ('RUN_PRODUCTION_SANITY') {
      steps {
        catchError(buildResult: 'SUCCESS', message: 'Stage Failed', stageResult: 'FAILURE') {
          script {
            notifier.sendDeploymentProgressNotification(methods.calculatePercentage(CURRENT_STAGE_NUM, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
            methods.runAutomationSanity()
            notifier.sendDeploymentProgressNotification(methods.calculatePercentage(++CURRENT_STAGE_NUM, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
          }
        }
      }
    }

    stage('PROMOTE_BLUE_TO_GREEN') {
      steps {
        script {
          env.CURRENT_STAGE = 'PROMOTE_BLUE_TO_GREEN'
          notifier.sendDeploymentProgressNotification(methods.calculatePercentage(CURRENT_STAGE_NUM, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
          notifier.sendDeploymentNotification('SWITCH_TO_GREEN')
          APPROVER = input message: 'Promote Blue to Green?', submitterParameter: 'APPROVER'

          dir ("surveysparrow-production-eks/kubectl/${env.JOB_NAME}/blue-green-templates") {
            sh "aws eks update-kubeconfig --region ${env.JOB_NAME} --name ${CLUSTER_NAME}"

            echo "\n ------------------------------------ Switching Blue Service to Green Service -------------------------------------------------- \n"
            methods.updateService(greenDeploymentParams.service, params.deploymenttag)
            echo "\n ------------------------------------ Switched Blue Service to Green Deployment -------------------------------------------------- \n"

            env.CURRENT_STAGE = 'PROMOTE_BLUE_SERVICE_TO_GREEN'

            echo "\n ------------------------------------ Updating Blue Green HA Proxy Deployment to Green Deployment -------------------------------------------------- \n"
            methods.createOrUpdateHAProxyDeployment('haproxy-forwarder', 'green')
            methods.waitForHAProxyDeploymentAndPods('haproxy-forwarder')
            echo "\n ------------------------------------ Updated Blue Green HA Proxy Deployment to Green Deployment -------------------------------------------------- \n"

            env.CURRENT_STAGE = 'PROMOTE_BLUE_HAPROXY_TO_GREEN'

            sh "aws dynamodb put-item --table-name ${region_dynamo_map[env.JOB_NAME]} --item '{\"token\": {\"S\": \"VERSION\"},\"LATEST\": {\"S\": \"${version}\"}}' --region ${env.JOB_NAME}"
            notifier.sendDeploymentProgressNotification(methods.calculatePercentage(++CURRENT_STAGE_NUM, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
          }
        }
      }

      post {
        failure {
          script {
            notifier.sendDeploymentNotification('SWITCH_TO_GREEN_FAILED')
          }
        }
      }
    }

    stage('RUNNING_POST_MIGRATIONS') {
      agent {
        label "DockerBuilderFleet-${env.JOB_NAME}"
      }

      when {
        beforeAgent true
        expression {
          return migrations.postMigrationExists
        }
      }

      steps {
        script {
          catchError(buildResult: 'SUCCESS', message: 'Stage Failed', stageResult: 'FAILURE') {
            env.CURRENT_STAGE = 'RUNNING_POST_MIGRATIONS'
            notifier.sendDeploymentProgressNotification(methods.calculatePercentage(CURRENT_STAGE_NUM, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
            notifier.sendDeploymentNotification('POST_MIGRATION_STARTED')
            input (message: "Approve to start running post migrations.")

            postMigrationStatus = methods.runMigrations('POST')
            if (postMigrationStatus == 0) {
              notifier.sendDeploymentProgressNotification(methods.calculatePercentage(++CURRENT_STAGE_NUM, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
              notifier.sendDeploymentNotification('POST_MIGRATION_SUCCESSFUL')
              echo "Migrations ran successfully"
            } else {
              notifier.sendDeploymentNotification('POST_MIGRATION_FAILED')
            }
          }
        }
      }
    }

    stage('CLEANUP_BLUE') {
      steps {
        script {
          env.CURRENT_STAGE = 'CLEANUP_BLUE'
          notifier.sendDeploymentProgressNotification(methods.calculatePercentage(CURRENT_STAGE_NUM, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
          dir ("surveysparrow-production-eks/kubectl/${env.JOB_NAME}/blue-green-templates") {
            sh "aws eks update-kubeconfig --region ${env.JOB_NAME} --name ${CLUSTER_NAME}"
            echo "\n------------------------------------ Deleting Blue App and HA Proxy Deployment --------------------------------------------------\n"
            methods.deleteAppDeployments('app', greenDeploymentParams.deployment, greenDeploymentParams.app, greenDeploymentParams.image)
            echo "\n------------------------------------ Deleted Blue App and HA Proxy Deployment --------------------------------------------------\n"

            currentBuild.description = "Current Blue - ${greenDeploymentParams.deployment} | Green - ${blueDeploymentParams.deployment}"
            notifier.sendDeploymentProgressNotification(methods.calculatePercentage(++CURRENT_STAGE_NUM, STAGE_COUNT), lastSuccessfulBuildDuration - currentBuild.duration, message_timestamp)
          }
        }
      }
    }
  }

  post {
    always {
      script {
        step([$class: 'NewRelicDeploymentNotifier',
          notifications: [
            [
              apiKey: "NewrelicNotifier",
              applicationId: '',
              european: false,
              entityGuid: "${variables.REGION_ENTITY_GUID_MAP[env.JOB_NAME]}",
              changelog: "",
              commit: "${params.deploymenttag}",
              deeplink: "${env.BUILD_URL}",
              deploymentType: "BLUE_GREEN",
              description: "Deployment Tag: ${params.deploymenttag}",
              groupId: "${env.JOB_NAME}",
              user: "${env.BUILD_USER}",
              version: "${params.deploymenttag}",
              timestamp: ''
            ]
          ]
        ])

        if (env.CURRENT_STAGE == 'CLEANUP_BLUE' || env.CURRENT_STAGE == 'RUNNING_POST_MIGRATIONS') {

          echo "\n ------------------------------------ Switching Values in Parameter Store -------------------------------------------------- \n"
          methods.updateDeploymentParameters('green', blueDeploymentParams)
          methods.updateDeploymentParameters('blue', greenDeploymentParams)
          echo "\n ------------------------------------ Switched Values in Parameter Store -------------------------------------------------- \n"

          echo "\n ------------------------------------ CURRENT GREEN -------------------------------------------------- \n"
          echo "Green Deployment - ${blueDeploymentParams.deployment}\nGreen ConfigMap - ${blueDeploymentParams.configmap}\nGreen App - ${blueDeploymentParams.app}\nGreen Service - ${greenDeploymentParams.service}\nGreen Version - ${blueDeploymentParams.version}\nGreen Image = ${blueDeploymentParams.image}"
          echo "\n ------------------------------------ NEXT BLUE -------------------------------------------------- \n"
          echo "Blue Deployment - ${greenDeploymentParams.deployment}\nBlue ConfigMap - ${greenDeploymentParams.configmap}\nBlue App - Based on TAG\nBlue Service - ${blueDeploymentParams.service}\nBlue Version - Based on next Deployment\nBlue Image = Based on TAG"
          echo "\n -------------------------------------------------------------------------------------------------- \n"
        }
      }
    }

    success {
      script {
        notifier.sendDeploymentProgressNotification(100, currentBuild.duration, message_timestamp, 'DEPLOYMENT_SUCCESSFUL')
        notifier.sendDeploymentNotification('DEPLOYMENT_SUCCESSFUL')
        cleanWs()
      }
    }

    failure {
      script {
        methods.handleDeploymentFailure(env.CURRENT_STAGE)
        notifier.sendDeploymentProgressNotification(methods.calculatePercentage(CURRENT_STAGE_NUM, STAGE_COUNT), currentBuild.duration, message_timestamp, 'DEPLOYMENT_FAILED')
        notifier.sendDeploymentNotification('DEPLOYMENT_FAILED')
        cleanWs()
      }
    }

    aborted {
      script {
        methods.handleDeploymentFailure(env.CURRENT_STAGE)
        notifier.sendDeploymentProgressNotification(methods.calculatePercentage(CURRENT_STAGE_NUM, STAGE_COUNT), currentBuild.duration, message_timestamp, 'DEPLOYMENT_ABORTED')
        notifier.sendDeploymentNotification('DEPLOYMENT_ABORTED')
        cleanWs()
      }
    }
  }
}