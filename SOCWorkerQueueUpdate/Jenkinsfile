properties(
  [
    disableConcurrentBuilds(),
    buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '5', numToKeepStr: '5'))

  ]
)

pipeline {
  agent { 
    label "JenkinsWorkerFleet" 
  }

  parameters {
    choice (
      choices: ['us-east-1', 'eu-central-1', 'ap-south-1', 'me-central-1', 'eu-west-2', 'me-central2-b'],
      name: 'region',
      description: 'Specify which region to update workers'
    )

    string (
      name: 'reason',
      description: 'Specify the reason to update the worker configuration'
    )
  }

  stages {
    stage('UPDATING_WORKER_CONFIG') {

      steps {
        script {
          notifier   = load 'Helpers/notifications.groovy'
          variables = load 'Helpers/variables.groovy'
          methods   = load 'Helpers/methods.groovy'

          methods.prepInstance()

          clusterName      = variables.CLUSTER_NAME_REGION_MAP[params.region]
          appName          = variables.REGION_APP_MAP[params.region]
          microService     = env.JOB_NAME.split('/')[1]
          microServiceVars = variables.SOC[microService]['backend']

          regionName = variables.REGION_DC_NAME_MAP[params.region]
          if (variables.SOC_JOB_NAMES_MAP[microService][regionName] == null) {
            error "Microservice - ${microService} is not present in ${regionName}"
          }

          notifier.sendSlackNofiticationMicroservices('SOC_WORKER_CONFIG_UPDATE_STARTED')

          dir (variables.EKS_REPO_FOLDER) {
            methods.cloneEKSYAMLRepo(variables.KUBERNETES_YAML_BRANCH)
            methods.authenticateToK8sCluster(params.region, clusterName)

            dir ("app-v1/kubectl/${params.region}/${microServiceVars.shortName}") {
              workerDeployment = "${appName}-${microServiceVars.shortName}-worker"
              if (methods.checkIfDeploymentExists(workerDeployment, variables.DEFAULT_NAMESPACE)) {
                sh "kubectl apply -f worker.yaml"
              }
            }
          }
        }
      }
    }
  }

  post {
    always {
      cleanWs()
    }

    success {
      script {
        notifier.sendSlackNofiticationMicroservices('SOC_WORKER_CONFIG_UPDATE_SUCCESSFUL')
      }
    }

    failure {
      script {
        notifier.sendSlackNofiticationMicroservices('SOC_WORKER_CONFIG_UPDATE_FAILED')
      }
    }

    aborted {
      script {
        notifier.sendSlackNofiticationMicroservices('SOC_WORKER_CONFIG_UPDATE_ABORTED')
      }
    }
  }
}