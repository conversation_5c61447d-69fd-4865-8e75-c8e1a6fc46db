pipeline {
    agent { label "DockerBuilderFleet" }

    environment {
        ARGOCD_PASSWORD = credentials('PlatformArgoCD')
    }

    stages {
        stage("Git Clone") {
            steps {
                script {
                    variables = load 'Helpers/variables.groovy'
                    helper = load 'Helpers/sparrowpay-helpers.groovy'
                    notifier = load 'Helpers/notifications.groovy'
                    methods = load 'Helpers/methods.groovy'

                    application_code  = env.JOB_NAME.split('/')[-3]
                    service_code  = env.JOB_NAME.split('/')[-2]
                    region_code   = env.JOB_NAME.split('/')[-1]

                    region = variables.REGION_CODE_DC_NAME_MAP[region_code]
                    app = variables.SPARROWPAY_SERVICE
                    service = variables.SPARROWPAY_SERVICENAME_MAPPING[service_code]

                    cluster = variables.SPARROWPAY_CLUSTER_NAME[region]

                    notifier.sendSlackNotificationSparrowpay("#0066CC", "SPARROWPAY_DEPLOYMENT_STARTED")
                    currentBuild.displayName = "Deploying: ${service} : ${currentBuild.number}"
                    
                    DEPLOYMENT_FILE_NAME = "03-${service}-deployment.yaml"
                    CONFIGMAP_FILE_NAME = "02-${service}-configmap.yaml"
                    
                    dir('sparrowpay') {
                        methods.cloneRepo(variables.SPARROWPAY_REPO, variables.PRODUCTION_RELEASE_BRANCH)
                    }

                    dir (variables.CONFIG_REPO_FOLDER) {
                        methods.cloneConfig()
                    }
                }
            }
        }
        stage("Docker Build") {
            when { 
                expression { 
                    service != 'frontend'
                } 
            }
            steps {
                script {
                    sh "sudo systemctl start docker"
                    GIT_COMMIT_HASH = sh (script: "git log -n 1 --pretty=format:'%H' | head -c 7", returnStdout: true)
                    ECR_BUILD_ID = sh (script: "echo '${variables.PRODUCTION_BRANCH}_${GIT_COMMIT_HASH}_${currentBuild.number}' | tr -d '\n'", returnStdout: true)
                    dir("sparrowpay/${service}") {                    
                        sh "docker build -t ${app.namespace}-${service} -f ./Dockerfiles/production -t ${variables.PLATFORM_ECR_REPO_APP[region]}/${app.namespace}-${service}:$ECR_BUILD_ID -t ${variables.PLATFORM_ECR_REPO_APP[region]}/${app.namespace}-${service}:latest ."
                    }                        
                }
            }
        }
        stage("Pre Deployments Preps") {
            when { 
                expression { 
                    service != 'frontend'
                } 
            }
            steps {
                script {
                    dir(variables.EKS_REPO_FOLDER) {
                        methods.cloneEKSYAMLRepo(variables.KUBERNETES_YAML_BRANCH)
                        def incomingConfig = "${env.WORKSPACE}/${variables.CONFIG_REPO_FOLDER}/Platform/sparrowpay/${region}/${service}.yaml"
                        def currentConfig = "${env.WORKSPACE}/${variables.EKS_REPO_FOLDER}/sparrowpay/${region}/${service}/${CONFIGMAP_FILE_NAME}"

                        diffValidation = helper.checkFileDiff(currentConfig, incomingConfig)

                        if (!diffValidation) {
                            sh "rm -rf $currentConfig"
                            sh "cp '$incomingConfig' '$currentConfig'"
                        }
                        sh "sed -i 's+${variables.PLATFORM_ECR_REPO_APP[region]}/${app.namespace}.*+${variables.PLATFORM_ECR_REPO_APP[region]}/${app.namespace}-${service}:$ECR_BUILD_ID+g' ./sparrowpay/${region}/${service}/${DEPLOYMENT_FILE_NAME}"
                    }
                }
            }
        }
        stage("Webpack Build and Upload") {
            when { 
                expression { 
                    service == 'frontend'
                } 
            }
            steps {
                script {
                    dir('sparrowpay/frontend') {
                        FULL_GIT_COMMIT_HASH = sh (script: "git log -n 1 --pretty=format:'%H'", returnStdout: true)
                        nodejs('Node-v16.9.0') {
                            sh "npm install --legacy-peer-deps"
                            sh "rm -rf ./client/dist"
                            sh "npm run build"

                        }
                        dir('client') {
                            ASSET_URL = "${variables.ASSET_BUCKET}/sparrowpay/production-$FULL_GIT_COMMIT_HASH"
                            sh "aws s3 cp dist s3://$ASSET_URL --recursive --quiet"

                            def responseCode = sh( script: "curl -s -o /dev/null -w \"%{http_code}\" https://${ASSET_URL}/super_admin.app.bundle.js", returnStdout: true )
                            
                            if (responseCode != "200") {
                                error 'Asset Bundle Not Accessible'
                            }
                        }
                    }
                }
            }
        }
        stage("Upload the docker image to ECR") {
            when  { 
                expression { 
                    service != 'frontend'
                } 
            }
            steps {
                script {
                    withAWS(region: region, roleAccount: app.account, role: app.role) { 
                        sh "aws ecr get-login-password --region ${region} | docker login --username AWS --password-stdin ${variables.PLATFORM_ECR_REPO_APP[region]}"
                        sh "docker push ${variables.PLATFORM_ECR_REPO_APP[region]}/${app.namespace}-${service}:$ECR_BUILD_ID"
                        sh "docker push ${variables.PLATFORM_ECR_REPO_APP[region]}/${app.namespace}-${service}:latest"
                        sh "docker rmi -f \$(docker images -aq) | true "
                    }
                }
            }
        }
        stage("Syncing EKS Deployments") {
            when { 
                expression { 
                    service != 'frontend'
                } 
            }
            steps {
                script {
                    dir(variables.EKS_REPO_FOLDER) {
                        methods.commitAndPush("Changing sparrowpay service: ${service} deployment manifest with image tag:$ECR_BUILD_ID", variables.KUBERNETES_YAML_BRANCH)
                    }
                }
            }
        }
        stage("Deployment in EKS") {
            steps {
                script {
                    notifier.sendSlackNotificationSparrowpay("#3B3E3D", "SPARROWPAY_ARGO_SYNC")
                    currentBuild.displayName = "Syncing: ${service} : ${currentBuild.number}"

                    sh "argocd login ${variables.PLATFORM_ARGOCD_DOMAIN} --username ${variables.PLATFORM_ARGOCD_USERNAME} --password ${env.ARGOCD_PASSWORD}"

                    if(service == "frontend") {
                        methods.prepInstance()

                        withAWS(region: region, roleAccount: app.account, role: app.role) { 
                            secrets = sh( script: "aws secretsmanager get-secret-value --region ${region} --secret-id ${variables.ADMIN_BACKEND_SECRET_ID} | jq '.SecretString'" , returnStdout: true )

                            def resultMap = new groovy.json.JsonSlurper().parseText(secrets)
                            def secretJson = readJSON text: resultMap

                            secretJson['DIST_ID'] = FULL_GIT_COMMIT_HASH

                            def updatedSecretValue = groovy.json.JsonOutput.prettyPrint(groovy.json.JsonOutput.toJson(secretJson))

                            sh "aws secretsmanager update-secret --region ${region} --secret-id ${variables.ADMIN_BACKEND_SECRET_ID} --secret-string '${updatedSecretValue}'"
                        }
                        sh "sleep 30"

                        sh "argocd app sync ${app.namespace}-admin-backend-argocd --grpc-web"
                        sh "argocd app wait ${app.namespace}-admin-backend-argocd --grpc-web"
                        sh "argocd app actions run argocd/${app.namespace}-admin-backend-argocd  restart --kind Deployment --resource-name ${app.namespace}-admin-backend-deployment"
                        sh "argocd app wait ${app.namespace}-admin-backend-argocd --grpc-web"
                        sh "argocd logout ${variables.PLATFORM_ARGOCD_DOMAIN}"
                    }

                    if(service != "frontend") {
                        sh "argocd app sync ${app.namespace}-${service}-argocd --grpc-web"
                        sh "argocd app wait ${app.namespace}-${service}-argocd --grpc-web"
                        sh "argocd logout ${variables.PLATFORM_ARGOCD_DOMAIN}"
                    }
                }
            }
            post {
                always {
                    script {
                        if (service == "payments" || service == "admin-backend") {
                            withAWS(region: region, roleAccount: app.account, role: app.role) { 
                                withEnv(["KUBECONFIG=/tmp/.kube-${app.account}-${region}"]) {    
                                    sh "aws eks --region ${region} update-kubeconfig --name ${cluster}"
                                    sh "kubectl logs --selector=job-name=premigration-${service} -n ${app.namespace} --tail=-1"
                                    sh "kubectl logs --selector=job-name=postmigration-${service} -n ${app.namespace} --tail=-1"
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    post {
        always {
            script {
                currentBuild.displayName = "Deployed: ${service} : ${currentBuild.number}"
                currentBuild.description = "Deployed Branch: ${variables.PRODUCTION_BRANCH}"   
                cleanWs()
            }
        }
        success {
            script {
                notifier.sendSlackNotificationSparrowpay("#2ECC71", "SPARROWPAY_DEPLOYMENT_SUCCESS")
            }
        }
        failure {
            script {
                notifier.sendSlackNotificationSparrowpay("#E74C3C", "SPARROWPAY_DEPLOYMENT_FAILURE")
            }
        }
        aborted {
            script {
                notifier.sendSlackNotificationSparrowpay("#9D9D9D", "SPARROWPAY_DEPLOYMENT_ABORTED")
            }
        }
    }
}
